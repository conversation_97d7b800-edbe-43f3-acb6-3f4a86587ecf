{"version": 3, "file": "IconBase.es-DrgWL7aQ.js", "sources": ["../../../node_modules/@phosphor-icons/react/dist/lib/context.es.js", "../../../node_modules/@phosphor-icons/react/dist/lib/IconBase.es.js"], "sourcesContent": ["import { createContext as r } from \"react\";\nconst o = r({\n  color: \"currentColor\",\n  size: \"1em\",\n  weight: \"regular\",\n  mirrored: !1\n});\nexport {\n  o as IconContext\n};\n", "import * as e from \"react\";\nimport { IconContext as h } from \"./context.es.js\";\nconst p = e.forwardRef(\n  (s, a) => {\n    const {\n      alt: n,\n      color: r,\n      size: t,\n      weight: o,\n      mirrored: c,\n      children: i,\n      weights: m,\n      ...x\n    } = s, {\n      color: d = \"currentColor\",\n      size: l,\n      weight: f = \"regular\",\n      mirrored: g = !1,\n      ...w\n    } = e.useContext(h);\n    return /* @__PURE__ */ e.createElement(\n      \"svg\",\n      {\n        ref: a,\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: t != null ? t : l,\n        height: t != null ? t : l,\n        fill: r != null ? r : d,\n        viewBox: \"0 0 256 256\",\n        transform: c || g ? \"scale(-1, 1)\" : void 0,\n        ...w,\n        ...x\n      },\n      !!n && /* @__PURE__ */ e.createElement(\"title\", null, n),\n      i,\n      m.get(o != null ? o : f)\n    );\n  }\n);\np.displayName = \"IconBase\";\nexport {\n  p as default\n};\n"], "names": ["o", "r", "p", "e.forwardRef", "s", "a", "n", "t", "c", "i", "m", "x", "d", "l", "f", "g", "w", "e.useContext", "h", "e.createElement"], "mappings": "sCACA,MAAMA,EAAIC,EAAAA,cAAE,CACV,MAAO,eACP,KAAM,MACN,OAAQ,UACR,SAAU,EACZ,CAAC,ECJKC,EAAIC,EAAAA,WACR,CAACC,EAAGC,IAAM,CACR,KAAM,CACJ,IAAKC,EACL,MAAOL,EACP,KAAMM,EACN,OAAQP,EACR,SAAUQ,EACV,SAAUC,EACV,QAASC,EACT,GAAGC,CACT,EAAQP,EAAG,CACL,MAAOQ,EAAI,eACX,KAAMC,EACN,OAAQC,EAAI,UACZ,SAAUC,EAAI,GACd,GAAGC,CACT,EAAQC,EAAAA,WAAaC,CAAC,EAClB,OAAuBC,EAAAA,cACrB,MACA,CACE,IAAKd,EACL,MAAO,6BACP,MAAOE,GAAgBM,EACvB,OAAQN,GAAgBM,EACxB,KAAMZ,GAAgBW,EACtB,QAAS,cACT,UAAWJ,GAAKO,EAAI,eAAiB,OACrC,GAAGC,EACH,GAAGL,CACX,EACM,CAAC,CAACL,GAAqBa,EAAAA,cAAgB,QAAS,KAAMb,CAAC,EACvDG,EACAC,EAAE,IAAIV,GAAgBc,CAAC,CAC7B,CACE,CACF,EACAZ,EAAE,YAAc", "x_google_ignoreList": [0, 1]}