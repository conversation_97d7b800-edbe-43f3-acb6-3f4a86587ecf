import{r as e}from"./mui-51Y1Yx8M.js";const h=e.createContext({color:"currentColor",size:"1em",weight:"regular",mirrored:!1}),x=e.forwardRef((n,s)=>{const{alt:t,color:l,size:r,weight:o,mirrored:a,children:c,weights:g,...m}=n,{color:w="currentColor",size:i,weight:u="regular",mirrored:d=!1,...f}=e.useContext(h);return e.createElement("svg",{ref:s,xmlns:"http://www.w3.org/2000/svg",width:r??i,height:r??i,fill:l??w,viewBox:"0 0 256 256",transform:a||d?"scale(-1, 1)":void 0,...f,...m},!!t&&e.createElement("title",null,t),c,g.get(o??u))});x.displayName="IconBase";export{x as p};
//# sourceMappingURL=IconBase.es.CTyFFwXt.js.map
