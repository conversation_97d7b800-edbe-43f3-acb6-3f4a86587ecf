{"version": 3, "file": "ArrowLeft.es-DzNS2JFL.js", "sources": ["../../../node_modules/@phosphor-icons/react/dist/defs/ArrowLeft.es.js"], "sourcesContent": ["import * as e from \"react\";\nconst a = /* @__PURE__ */ new Map([\n  [\n    \"bold\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M228,128a12,12,0,0,1-12,12H69l51.52,51.51a12,12,0,0,1-17,17l-72-72a12,12,0,0,1,0-17l72-72a12,12,0,0,1,17,17L69,116H216A12,12,0,0,1,228,128Z\" }))\n  ],\n  [\n    \"duotone\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M112,56V200L40,128Z\", opacity: \"0.2\" }), /* @__PURE__ */ e.createElement(\"path\", { d: \"M216,120H120V56a8,8,0,0,0-13.66-5.66l-72,72a8,8,0,0,0,0,11.32l72,72A8,8,0,0,0,120,200V136h96a8,8,0,0,0,0-16ZM104,180.69,51.31,128,104,75.31Z\" }))\n  ],\n  [\n    \"fill\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M224,128a8,8,0,0,1-8,8H120v64a8,8,0,0,1-13.66,5.66l-72-72a8,8,0,0,1,0-11.32l72-72A8,8,0,0,1,120,56v64h96A8,8,0,0,1,224,128Z\" }))\n  ],\n  [\n    \"light\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M222,128a6,6,0,0,1-6,6H54.49l61.75,61.76a6,6,0,1,1-8.48,8.48l-72-72a6,6,0,0,1,0-8.48l72-72a6,6,0,0,1,8.48,8.48L54.49,122H216A6,6,0,0,1,222,128Z\" }))\n  ],\n  [\n    \"regular\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z\" }))\n  ],\n  [\n    \"thin\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M220,128a4,4,0,0,1-4,4H49.66l65.17,65.17a4,4,0,0,1-5.66,5.66l-72-72a4,4,0,0,1,0-5.66l72-72a4,4,0,0,1,5.66,5.66L49.66,124H216A4,4,0,0,1,220,128Z\" }))\n  ]\n]);\nexport {\n  a as default\n};\n"], "names": ["a", "e.createElement", "e.Fragment"], "mappings": "sCACK,MAACA,EAAoB,IAAI,IAAI,CAChC,CACE,OACgBC,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,8IAA+I,CAAC,CACnP,EACE,CACE,UACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,sBAAuB,QAAS,KAAK,CAAE,EAAmBA,EAAAA,cAAgB,OAAQ,CAAE,EAAG,+IAAgJ,CAAC,CAC3U,EACE,CACE,OACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,8HAA+H,CAAC,CACnO,EACE,CACE,QACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,kJAAmJ,CAAC,CACvP,EACE,CACE,UACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,uJAAwJ,CAAC,CAC5P,EACE,CACE,OACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,kJAAmJ,CAAC,CACvP,CACA,CAAC", "x_google_ignoreList": [0]}