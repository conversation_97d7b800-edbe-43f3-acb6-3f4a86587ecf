import{r as t,j as e,o as de,S as E,ae as k,a0 as Z,O as T,B as z,b as H,t as P,af as X,I as F,a2 as ee,a4 as U,T as G,a7 as te,a9 as C,ag as q,m as I,ah as me,g as _,ai as ue,aj as pe}from"./mui-51Y1Yx8M.js";import{o as he}from"./jobs-emails-list-CwGQNb10.js";import{w as Q,A as ge,B as Y,t as D,p as S,R as A,C as fe,H as xe,a as je}from"./index-mpIrzq_n.js";import{u as Ee,a as be,C as v,o as ye,b as we,s as J,n as ve}from"./types-CdLjW2Sm.js";import{e as Ce,o as Se}from"./PencilSimple.es-DvJcW97f.js";import{D as ie,u as $e}from"./DataGridPremium-B9W7LygO.js";import{use<PERSON><PERSON><PERSON> as Te}from"./router-Bd8Y3ArC.js";import{u as He,b as Me}from"./administration-rFUZJOEr.js";import{p as Ve}from"./IconBase.es-DrgWL7aQ.js";import"./vendor-Csw2ODfV.js";import"./ArrowLeft.es-DzNS2JFL.js";import"./redux-BKAL-i9G.js";const Je=new Map([["bold",t.createElement(t.Fragment,null,t.createElement("path",{d:"M232.49,80.49l-128,128a12,12,0,0,1-17,0l-56-56a12,12,0,1,1,17-17L96,183,215.51,63.51a12,12,0,0,1,17,17Z"}))],["duotone",t.createElement(t.Fragment,null,t.createElement("path",{d:"M232,56V200a16,16,0,0,1-16,16H40a16,16,0,0,1-16-16V56A16,16,0,0,1,40,40H216A16,16,0,0,1,232,56Z",opacity:"0.2"}),t.createElement("path",{d:"M205.66,85.66l-96,96a8,8,0,0,1-11.32,0l-40-40a8,8,0,0,1,11.32-11.32L104,164.69l90.34-90.35a8,8,0,0,1,11.32,11.32Z"}))],["fill",t.createElement(t.Fragment,null,t.createElement("path",{d:"M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40ZM205.66,85.66l-96,96a8,8,0,0,1-11.32,0l-40-40a8,8,0,0,1,11.32-11.32L104,164.69l90.34-90.35a8,8,0,0,1,11.32,11.32Z"}))],["light",t.createElement(t.Fragment,null,t.createElement("path",{d:"M228.24,76.24l-128,128a6,6,0,0,1-8.48,0l-56-56a6,6,0,0,1,8.48-8.48L96,191.51,219.76,67.76a6,6,0,0,1,8.48,8.48Z"}))],["regular",t.createElement(t.Fragment,null,t.createElement("path",{d:"M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"}))],["thin",t.createElement(t.Fragment,null,t.createElement("path",{d:"M226.83,74.83l-128,128a4,4,0,0,1-5.66,0l-56-56a4,4,0,0,1,5.66-5.66L96,194.34,221.17,69.17a4,4,0,1,1,5.66,5.66Z"}))]]),ae=t.forwardRef((n,i)=>t.createElement(Q,{ref:i,...n,weights:Je}));ae.displayName="CheckIcon";const re=t.forwardRef((n,i)=>t.createElement(Q,{ref:i,...n,weights:Ce}));re.displayName="PencilSimpleIcon";const ne=new Map([["bold",t.createElement(t.Fragment,null,t.createElement("path",{d:"M216,48H180V36A28,28,0,0,0,152,8H104A28,28,0,0,0,76,36V48H40a12,12,0,0,0,0,24h4V208a20,20,0,0,0,20,20H192a20,20,0,0,0,20-20V72h4a12,12,0,0,0,0-24ZM100,36a4,4,0,0,1,4-4h48a4,4,0,0,1,4,4V48H100Zm88,168H68V72H188ZM116,104v64a12,12,0,0,1-24,0V104a12,12,0,0,1,24,0Zm48,0v64a12,12,0,0,1-24,0V104a12,12,0,0,1,24,0Z"}))],["duotone",t.createElement(t.Fragment,null,t.createElement("path",{d:"M200,56V208a8,8,0,0,1-8,8H64a8,8,0,0,1-8-8V56Z",opacity:"0.2"}),t.createElement("path",{d:"M216,48H176V40a24,24,0,0,0-24-24H104A24,24,0,0,0,80,40v8H40a8,8,0,0,0,0,16h8V208a16,16,0,0,0,16,16H192a16,16,0,0,0,16-16V64h8a8,8,0,0,0,0-16ZM96,40a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8v8H96Zm96,168H64V64H192ZM112,104v64a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm48,0v64a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Z"}))],["fill",t.createElement(t.Fragment,null,t.createElement("path",{d:"M216,48H176V40a24,24,0,0,0-24-24H104A24,24,0,0,0,80,40v8H40a8,8,0,0,0,0,16h8V208a16,16,0,0,0,16,16H192a16,16,0,0,0,16-16V64h8a8,8,0,0,0,0-16ZM112,168a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm48,0a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm0-120H96V40a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8Z"}))],["light",t.createElement(t.Fragment,null,t.createElement("path",{d:"M216,50H174V40a22,22,0,0,0-22-22H104A22,22,0,0,0,82,40V50H40a6,6,0,0,0,0,12H50V208a14,14,0,0,0,14,14H192a14,14,0,0,0,14-14V62h10a6,6,0,0,0,0-12ZM94,40a10,10,0,0,1,10-10h48a10,10,0,0,1,10,10V50H94ZM194,208a2,2,0,0,1-2,2H64a2,2,0,0,1-2-2V62H194ZM110,104v64a6,6,0,0,1-12,0V104a6,6,0,0,1,12,0Zm48,0v64a6,6,0,0,1-12,0V104a6,6,0,0,1,12,0Z"}))],["regular",t.createElement(t.Fragment,null,t.createElement("path",{d:"M216,48H176V40a24,24,0,0,0-24-24H104A24,24,0,0,0,80,40v8H40a8,8,0,0,0,0,16h8V208a16,16,0,0,0,16,16H192a16,16,0,0,0,16-16V64h8a8,8,0,0,0,0-16ZM96,40a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8v8H96Zm96,168H64V64H192ZM112,104v64a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Zm48,0v64a8,8,0,0,1-16,0V104a8,8,0,0,1,16,0Z"}))],["thin",t.createElement(t.Fragment,null,t.createElement("path",{d:"M216,52H172V40a20,20,0,0,0-20-20H104A20,20,0,0,0,84,40V52H40a4,4,0,0,0,0,8H52V208a12,12,0,0,0,12,12H192a12,12,0,0,0,12-12V60h12a4,4,0,0,0,0-8ZM92,40a12,12,0,0,1,12-12h48a12,12,0,0,1,12,12V52H92ZM196,208a4,4,0,0,1-4,4H64a4,4,0,0,1-4-4V60H196ZM108,104v64a4,4,0,0,1-8,0V104a4,4,0,0,1,8,0Zm48,0v64a4,4,0,0,1-8,0V104a4,4,0,0,1,8,0Z"}))]]),se=t.forwardRef((n,i)=>t.createElement(Q,{ref:i,...n,weights:ne}));se.displayName="TrashIcon";const oe=t.forwardRef((n,i)=>t.createElement(Ve,{ref:i,...n,weights:ne}));oe.displayName="TrashIcon";function ke({children:n,...i}){return e.jsx(de,{...i,children:n})}const Ze=ge.injectEndpoints({endpoints:n=>({getJobs:n.query({query:i=>{const r=new URLSearchParams;return i.$top!==void 0&&r.append("$top",i.$top.toString()),i.$skip!==void 0&&r.append("$skip",i.$skip.toString()),i.$count!==void 0&&r.append("$count",i.$count.toString()),i.$filter&&r.append("$filter",i.$filter),i.$orderby&&r.append("$orderby",i.$orderby),i.$select&&r.append("$select",i.$select),{url:`/jobs?${r.toString()}`,method:"GET"}},providesTags:["Job"]}),getJob:n.query({query:i=>({url:`/jobs/${i}`,method:"GET"}),providesTags:(i,r,c)=>[{type:"Job",id:c}]}),createJob:n.mutation({query:i=>({url:"/jobs",method:"POST",body:i,headers:{"Content-Type":"application/json"}}),invalidatesTags:["Job"]}),updateJob:n.mutation({query:({id:i,job:r})=>({url:`/jobs/${i}`,method:"PUT",body:{model:r},headers:{"Content-Type":"application/json"}}),invalidatesTags:(i,r,{id:c})=>[{type:"Job",id:c},"Job"]}),deleteJob:n.mutation({query:i=>({url:`/jobs/${i}`,method:"DELETE"}),invalidatesTags:["Job"]}),getJobTriggers:n.query({query:({jobId:i,params:r})=>{const c=new URLSearchParams;return r.$top!==void 0&&c.append("$top",r.$top.toString()),r.$skip!==void 0&&c.append("$skip",r.$skip.toString()),r.$count!==void 0&&c.append("$count",r.$count.toString()),r.$filter&&c.append("$filter",r.$filter),r.$orderby&&c.append("$orderby",r.$orderby),r.$select&&c.append("$select",r.$select),{url:`/jobs/${i}/triggers?${c.toString()}`,method:"GET"}}}),getJobTrigger:n.query({query:({jobId:i,triggerId:r})=>({url:`/jobs/${i}/triggers/${r}`,method:"GET"})}),createJobTrigger:n.mutation({query:({jobId:i,trigger:r})=>({url:`/jobs/${i}/triggers`,method:"POST",body:r,headers:{"Content-Type":"application/json"}}),invalidatesTags:["Job"]}),updateJobTrigger:n.mutation({query:({jobId:i,triggerId:r,trigger:c})=>({url:`/jobs/${i}/triggers/${r}`,method:"PUT",body:{model:c},headers:{"Content-Type":"application/json"}}),invalidatesTags:["Job"]}),deleteJobTrigger:n.mutation({query:({jobId:i,triggerId:r})=>({url:`/jobs/${i}/triggers/${r}`,method:"DELETE"}),invalidatesTags:["Job"]})})}),{useGetJobQuery:De,useCreateJobMutation:Fe,useUpdateJobMutation:ze}=Ze;function Ae({successEmails:n,errorEmails:i,onEmailsChange:r}){const[c,g]=t.useState([]),[f,x]=t.useState(null),[w,b]=t.useState(""),[M,h]=t.useState(""),$=t.useRef(null),m=t.useRef(null),y=t.useCallback(a=>{if(r){const d=a.filter(o=>o.isSuccess).map(o=>o.email),l=a.filter(o=>o.isFailure).map(o=>o.email);r(d,l)}},[r]);t.useEffect(()=>{const a=new Map;for(const l of c)a.set(l.email,{email:l.email,isSuccess:!1,isFailure:!1});for(const l of n){const o=l.trim(),u=a.get(o);if(u){u.isSuccess=!0;continue}a.set(o,{email:o,isSuccess:!0,isFailure:!1})}for(const l of i){const o=l.trim(),u=a.get(o);if(u){u.isFailure=!0;continue}a.set(o,{email:o,isSuccess:!1,isFailure:!0})}const d=Array.from(a.values());g(d)},[n,i]);const V=()=>{var u;const a=((u=$.current)==null?void 0:u.value)||"",d=j(a);if(d){b(d);return}b("");const l=a.trim(),o=[...c,{email:l,isSuccess:!0,isFailure:!0}];g(o),y(o),$.current&&($.current.value="")},N=(a,d)=>{g(l=>{const o=l.map(u=>u.email===a?{...u,isSuccess:d}:u);return y(o),o})},B=(a,d)=>{g(l=>{const o=l.map(u=>u.email===a?{...u,isFailure:d}:u);return y(o),o})},L=a=>{x(a),h(""),setTimeout(()=>{m.current&&(m.current.focus(),requestAnimationFrame(()=>{m.current&&m.current.select()}))},50)},R=()=>{var l;const a=((l=m.current)==null?void 0:l.value)||"",d=j(a,!0);if(d){h(d);return}h(""),f&&a.trim()&&g(o=>{const u=o.map(W=>W.email===f?{...W,email:a.trim()}:W);return y(u),u}),x(null)},O=()=>{x(null),h("")},s=a=>{g(d=>{const l=d.filter(o=>o.email!==a);return y(l),l}),f===a&&x(null)},p=a=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a),j=(a,d=!1)=>a.trim()?p(a.trim())?c.some(o=>o.email===a.trim()&&(!d||o.email!==f))?"This email address already exists":"":"Please enter a valid email address":"Email address is required",le=()=>{w&&b("")},ce=[{field:"email",headerName:"Email",flex:1,minWidth:200,renderCell:a=>f===a.row.email?e.jsx(P,{title:M||"",open:!!M,placement:"bottom-start",arrow:!0,slotProps:{tooltip:{className:"jobs-emails-list__error-tooltip"}},children:e.jsx(z,{className:"jobs-emails-list__edit-input-container",children:e.jsx(T,{inputRef:m,defaultValue:f,error:!!M,onChange:()=>{M&&h("")},onKeyDown:l=>{l.key==="Enter"?(l.preventDefault(),l.stopPropagation(),R()):l.key==="Escape"?(l.preventDefault(),l.stopPropagation(),O()):["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Home","End"].includes(l.key)&&l.stopPropagation()},size:"small",sx:{width:"100%"}})})}):e.jsx("span",{onDoubleClick:()=>L(a.row.email),className:"jobs-emails-list__email-cell",children:a.value})},{field:"isSuccess",headerName:"On Successful Completion",width:180,align:"center",headerAlign:"center",renderCell:a=>e.jsx(X,{checked:a.value,onChange:d=>N(a.row.email,d.target.checked),size:"small",sx:{padding:0}})},{field:"isFailure",headerName:"Failure",width:120,align:"center",headerAlign:"center",renderCell:a=>e.jsx(X,{checked:a.value,onChange:d=>B(a.row.email,d.target.checked),size:"small",sx:{padding:0}})},{field:"actions",headerName:"",width:100,align:"center",headerAlign:"center",sortable:!1,disableColumnMenu:!0,renderCell:a=>f===a.row.email?e.jsx(F,{onClick:R,size:"small",sx:{padding:"4px"},children:e.jsx(ae,{fontSize:"small"})}):e.jsxs(E,{direction:"row",spacing:.5,alignItems:"center",children:[e.jsx(F,{onClick:()=>L(a.row.email),size:"small",sx:{padding:"4px"},children:e.jsx(re,{fontSize:"small"})}),e.jsx(F,{onClick:()=>s(a.row.email),size:"small",sx:{padding:"4px"},children:e.jsx(se,{fontSize:"small"})})]})}];return e.jsxs(E,{children:[e.jsxs(E,{direction:"row",spacing:2,alignItems:"end",children:[e.jsxs(k,{fullWidth:!0,error:!!w,children:[e.jsx(Z,{children:"Add email"}),e.jsx(T,{inputRef:$,error:!!w,onChange:le,onKeyDown:a=>{a.key==="Enter"&&(a.preventDefault(),a.stopPropagation(),V(),setTimeout(()=>{a.target.focus()},0))}}),w&&e.jsx(z,{className:"jobs-emails-list__error-message",children:w})]}),e.jsx(H,{variant:"outlined",onClick:V,children:"Add"})]}),e.jsx(ie,{rows:c,columns:ce,getRowId:a=>a.email,pagination:!0,pageSizeOptions:[5,10,25],initialState:{pagination:{paginationModel:{pageSize:10,page:0}}},disableRowSelectionOnClick:!0,rowHeight:52,className:"jobs-emails-list__data-grid"})]})}const K=[{label:"JobType.AgencyWorkersDeactivationJob",value:0},{label:"JobType.NullJob",value:1},{label:"JobType.ReportJob",value:2}],Ne=ye({name:J().min(1,"Name is required").max(255),type:ve().gte(0).lte(K.length),description:J().max(255).optional(),parameters:J().min(1,"Parameters are required"),successEmails:J().max(512).optional(),errorEmails:J().max(512).optional(),disabled:we()}),Le={name:"",type:0,description:"",parameters:"",successEmails:"",errorEmails:"",disabled:!1};function Re({jobId:n}){const i=Te(),r=n!==void 0,{data:c,error:g,isLoading:f}=De(n,{skip:!r||!n}),[x,{isLoading:w}]=Fe(),[b,{isLoading:M}]=ze(),{control:h,handleSubmit:$,formState:{errors:m},setValue:y,watch:V,reset:N}=Ee({defaultValues:Le,resolver:be(Ne)});t.useEffect(()=>{if(r&&(c!=null&&c.entity)){const s={...c.entity};N(s)}},[c,r,N]),t.useEffect(()=>{g&&(Y.error(g),D.error("Failed to load job data"))},[g]);const B=t.useCallback((s,p)=>{y("successEmails",s.join(";")),y("errorEmails",p.join(";"))},[y]),L=V("successEmails")||"",R=V("errorEmails")||"",O=t.useCallback(async s=>{try{if(r&&n)await b({id:n,job:s}).unwrap(),D.success("Job updated"),i(S.administration.jobs.edit(n));else{const p={name:s.name,type:s.type,description:s.description,parameters:s.parameters,emailSuccess:s.successEmails,emailFail:s.errorEmails,disabled:s.disabled},{id:j}=await x(p).unwrap();D.success("Job created"),i(S.administration.jobs.edit(j,!0))}}catch(p){Y.error(p),D.error("Something went wrong!")}},[i,r,n,b,x]);return e.jsx("form",{onSubmit:$(O),children:e.jsxs(ee,{children:[e.jsxs(U,{sx:{justifyContent:"space-between",alignItems:"center"},children:[e.jsx(G,{variant:"h4",children:r?"Edit job":"Create new job"}),e.jsxs(z,{children:[e.jsx(H,{color:"secondary",component:A,href:S.administration.jobs.index(),children:"Cancel"}),e.jsx(H,{type:"submit",variant:"contained",children:r?"Update":"Create"})]})]}),e.jsx(te,{children:e.jsx(E,{divider:e.jsx(_,{}),spacing:4,children:e.jsxs(E,{spacing:3,children:[e.jsx(G,{variant:"h5",children:"Detail"}),e.jsxs(C,{container:!0,spacing:3,direction:"row",sx:{justifyContent:"center",alignItems:"center"},children:[e.jsx(C,{size:{md:6,xs:12},children:e.jsx(v,{control:h,name:"name",render:({field:s})=>e.jsx(P,{disableFocusListener:!0,title:e.jsx(I,{href:"https://aristotelos.cz",target:"_blank",children:"Documentation:"}),arrow:!0,children:e.jsxs(k,{error:!!m.name,fullWidth:!0,children:[e.jsx(Z,{required:!0,children:"Job Name"}),e.jsx(T,{required:!0,...s}),m.name?e.jsx(q,{children:m.name.message}):null]})})})}),e.jsx(C,{size:{md:6,xs:12},children:e.jsx(v,{control:h,name:"type",render:({field:s})=>e.jsx(me,{...s,getOptionLabel:p=>p.label,onChange:(p,j)=>{j&&s.onChange(j.value)},options:K,popupIcon:e.jsx(fe,{fontSize:"var(--icon-fontSize-sm)"}),renderInput:p=>e.jsxs(k,{error:!!m.type,fullWidth:!0,children:[e.jsx(Z,{children:"Job Type"}),e.jsx(T,{...p.InputProps,inputProps:p.inputProps}),m.type?e.jsx(q,{children:m.type.message}):null]}),renderOption:(p,j)=>t.createElement(ke,{...p,key:j.value,value:j.value},j.label),value:K.find(p=>p.value===s.value)})})}),e.jsx(C,{size:{xs:12},children:e.jsx(v,{control:h,name:"description",render:({field:s})=>e.jsx(P,{disableFocusListener:!0,title:e.jsx(I,{href:"https://aristotelos.cz",target:"_blank",children:"Documentation:"}),arrow:!0,children:e.jsxs(k,{error:!!m.description,fullWidth:!0,children:[e.jsx(Z,{children:"Description"}),e.jsx(T,{multiline:!0,...s}),m.description?e.jsx(q,{children:m.description.message}):null]})})})}),e.jsx(C,{size:{xs:12},children:e.jsx(v,{control:h,name:"parameters",render:({field:s})=>e.jsx(P,{disableFocusListener:!0,title:e.jsx(I,{href:"https://aristotelos.cz",target:"_blank",children:"Documentation:"}),arrow:!0,children:e.jsxs(k,{error:!!m.name,fullWidth:!0,children:[e.jsx(Z,{required:!0,children:"Parameters"}),e.jsx(T,{required:!0,multiline:!0,...s}),m.parameters?e.jsx(q,{children:m.parameters.message}):null]})})})}),e.jsxs(C,{size:{xs:12},children:[e.jsx(_,{}),e.jsxs(E,{direction:"column",children:[e.jsx(G,{children:" Emails "}),e.jsx(Ae,{errorEmails:R.split(";").map(s=>s.trim()).filter(s=>s),successEmails:L.split(";").map(s=>s.trim()).filter(s=>s),onEmailsChange:B})]}),e.jsx(_,{})]}),e.jsx(v,{control:h,name:"successEmails",render:({field:s})=>e.jsx("input",{...s,type:"hidden"})}),e.jsx(v,{control:h,name:"errorEmails",render:({field:s})=>e.jsx("input",{...s,type:"hidden"})})]})]})})}),e.jsxs(U,{sx:{justifyContent:"space-between",alignItems:"center"},children:[e.jsx(v,{control:h,name:"disabled",render:({field:s})=>e.jsx(ue,{control:e.jsx(pe,{checked:s.value,onChange:s.onChange}),label:"Disabled"})}),e.jsxs(z,{children:[e.jsx(H,{color:"secondary",component:A,href:S.administration.jobs.index(),children:"Cancel"}),e.jsx(H,{type:"submit",variant:"contained",children:r?"Update":"Create"})]})]})]})})}function qe({jobId:n}){const i=$e(),[r,c]=t.useState([]),[g,f]=He(),{data:x}=Me({jobId:`${n}`});t.useEffect(()=>{x&&x.value!=null&&c(x.value)},[n,x,f.isSuccess]),t.useEffect(()=>{f.isError&&D.error("Something went wrong!")},[f.isError]);const w=[{field:"id",headerName:"Id",type:"number",align:"center",headerAlign:"center",width:50},{field:"name",headerName:"Name",type:"string",minWidth:125},{field:"description",headerName:"Description",minWidth:250,type:"string"},{field:"parameters",headerName:"Parameters",minWidth:200,type:"string",align:"center",headerAlign:"center"},{field:"type",headerName:"Type",minWidth:150},{field:"disabled",headerName:"Disabled",width:100,type:"boolean"},{field:"actions",headerName:"",width:100,align:"center",headerAlign:"center",sortable:!1,disableColumnMenu:!0,renderCell:b=>e.jsxs(E,{direction:"row",spacing:.5,alignItems:"center",children:[e.jsx(F,{component:A,href:S.administration.jobs.triggers.editTrigger({jobId:n,triggerId:b.row.id}),size:"small",sx:{padding:"4px"},children:e.jsx(Se,{fontSize:"small"})}),e.jsx(F,{onClick:()=>{g({entityId1:`${n}`,entityId2:`${b.row.id}`})},size:"small",sx:{padding:"4px"},children:e.jsx(oe,{fontSize:"small"})})]})}];return e.jsxs(ee,{children:[e.jsxs(te,{children:[e.jsx(G,{children:"Triggers"}),e.jsx(ie,{apiRef:i,columns:w,rows:r,pagination:!0,pageSizeOptions:[5,10,25],initialState:{pagination:{paginationModel:{pageSize:10,page:0}}},disableRowSelectionOnClick:!0,rowHeight:52,sx:{mt:2,border:"1px solid #e0e0e0","& .MuiDataGrid-columnHeaders":{backgroundColor:"#f5f5f5",borderBottom:"1px solid #e0e0e0",fontSize:"0.875rem",fontWeight:500},"& .MuiDataGrid-columnHeader":{padding:"8px 12px"},"& .MuiDataGrid-cell":{padding:"8px 12px",fontSize:"0.875rem",borderBottom:"1px solid #f0f0f0",display:"flex",alignItems:"center"},"& .MuiDataGrid-cell:focus":{outline:"none"},"& .MuiDataGrid-cell:focus-within":{outline:"none"},"& .MuiDataGrid-row":{"&:hover":{backgroundColor:"#fafafa"}},"& .MuiDataGrid-row:last-child .MuiDataGrid-cell":{borderBottom:"none"},"& .MuiDataGrid-footerContainer":{borderTop:"1px solid #e0e0e0",backgroundColor:"#fafafa"},"& .MuiDataGrid-columnSeparator":{display:"none"}}})]}),e.jsx(U,{children:e.jsx(H,{style:{marginLeft:"auto"},component:A,href:S.administration.jobs.triggers.createTrigger(n),children:"Create new trigger"})})]})}function et({jobId:n,scrollToTriggers:i}){const r={title:`${n?"Edit":"Create"} | Jobs | Dashboard | ${je.name}`};return t.useEffect(()=>{if(i){const c=document.getElementById("triggers");c&&c.scrollIntoView({behavior:"auto"})}},[]),e.jsxs(t.Fragment,{children:[e.jsx(xe,{children:e.jsx("title",{children:r.title})}),e.jsx(z,{sx:{maxWidth:"var(--Content-maxWidth)",m:"var(--Content-margin)",p:"var(--Content-padding)",width:"var(--Content-width)"},children:e.jsxs(E,{spacing:4,children:[e.jsx(E,{spacing:3,children:e.jsx("div",{children:e.jsxs(I,{color:"text.primary",component:A,href:S.administration.jobs.index(),sx:{alignItems:"center",display:"inline-flex",gap:1},variant:"subtitle2",children:[e.jsx(he,{fontSize:"var(--icon-fontSize-md)"}),"Browse Jobs"]})})}),e.jsx(Re,{jobId:n}),e.jsx("div",{id:"triggers",children:e.jsx(C,{size:{xs:12},children:e.jsx(E,{direction:"column",children:n&&e.jsx(qe,{jobId:n})})})})]})})]})}export{et as Page};
//# sourceMappingURL=edit.DonYtpcP.js.map
