{"version": 3, "file": "blank-DJhpqkCH.js", "sources": ["../../../src/pages/dashboard/blank.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport Box from \"@mui/material/Box\";\nimport Button from \"@mui/material/Button\";\nimport Stack from \"@mui/material/Stack\";\nimport Typography from \"@mui/material/Typography\";\nimport { PlusIcon } from \"@phosphor-icons/react/dist/ssr/Plus\";\nimport { Helmet } from \"react-helmet-async\";\n\nimport type { Metadata } from \"@/types/metadata\";\nimport { appConfig } from \"@/config/app\";\n\nconst metadata = { title: `Blank | Dashboard | ${appConfig.name}` } satisfies Metadata;\n\nexport function Page(): React.JSX.Element {\n\treturn (\n\t\t<React.Fragment>\n\t\t\t<Helmet>\n\t\t\t\t<title>{metadata.title}</title>\n\t\t\t</Helmet>\n\t\t\t<Box\n\t\t\t\tsx={{\n\t\t\t\t\tmaxWidth: \"var(--Content-maxWidth)\",\n\t\t\t\t\tm: \"var(--Content-margin)\",\n\t\t\t\t\tp: \"var(--Content-padding)\",\n\t\t\t\t\twidth: \"var(--Content-width)\",\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<Stack spacing={4}>\n\t\t\t\t\t<Stack direction={{ xs: \"column\", sm: \"row\" }} spacing={3} sx={{ alignItems: \"flex-start\" }}>\n\t\t\t\t\t\t<Box sx={{ flex: \"1 1 auto\" }}>\n\t\t\t\t\t\t\t<Typography variant=\"h4\">Blank</Typography>\n\t\t\t\t\t\t</Box>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<Button startIcon={<PlusIcon />} variant=\"contained\">\n\t\t\t\t\t\t\t\tAction\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</Stack>\n\t\t\t\t\t<Box sx={{ border: \"1px dashed var(--mui-palette-divider)\", height: \"300px\", p: \"4px\" }} />\n\t\t\t\t</Stack>\n\t\t\t</Box>\n\t\t</React.Fragment>\n\t);\n}\n"], "names": ["metadata", "appConfig", "Page", "jsxs", "React.Fragment", "jsx", "<PERSON><PERSON><PERSON>", "Box", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "PlusIcon"], "mappings": "mPAWA,MAAMA,EAAW,CAAE,MAAO,uBAAuBC,EAAU,IAAI,EAAA,EAExD,SAASC,GAA0B,CACzC,OACCC,EAAAA,KAACC,WAAA,CACA,SAAA,CAAAC,EAAAA,IAACC,EAAA,CACA,SAAAD,MAAC,QAAA,CAAO,SAAAL,EAAS,MAAM,EACxB,EACAK,EAAAA,IAACE,EAAA,CACA,GAAI,CACH,SAAU,0BACV,EAAG,wBACH,EAAG,yBACH,MAAO,sBAAA,EAGR,SAAAJ,EAAAA,KAACK,EAAA,CAAM,QAAS,EACf,SAAA,CAAAL,EAAAA,KAACK,EAAA,CAAM,UAAW,CAAE,GAAI,SAAU,GAAI,KAAA,EAAS,QAAS,EAAG,GAAI,CAAE,WAAY,cAC5E,SAAA,CAAAH,EAAAA,IAACE,EAAA,CAAI,GAAI,CAAE,KAAM,UAAA,EAChB,SAAAF,EAAAA,IAACI,EAAA,CAAW,QAAQ,KAAK,SAAA,OAAA,CAAK,EAC/B,EACAJ,EAAAA,IAAC,MAAA,CACA,SAAAA,EAAAA,IAACK,EAAA,CAAO,UAAWL,EAAAA,IAACM,EAAA,CAAA,CAAS,EAAI,QAAQ,YAAY,SAAA,QAAA,CAErD,CAAA,CACD,CAAA,EACD,EACAN,EAAAA,IAACE,EAAA,CAAI,GAAI,CAAE,OAAQ,wCAAyC,OAAQ,QAAS,EAAG,MAAM,CAAG,CAAA,CAAA,CAC1F,CAAA,CAAA,CACD,EACD,CAEF"}