import{j as t,r as i,B as a,S as r,T as n,b as s}from"./mui-51Y1Yx8M.js";import{e}from"./Plus.es-C-SwutyH.js";import{H as o,a as x}from"./index-mpIrzq_n.js";import"./vendor-Csw2ODfV.js";import"./router-Bd8Y3ArC.js";import"./redux-BKAL-i9G.js";const d={title:`Blank | Dashboard | ${x.name}`};function g(){return t.jsxs(i.Fragment,{children:[t.jsx(o,{children:t.jsx("title",{children:d.title})}),t.jsx(a,{sx:{maxWidth:"var(--Content-maxWidth)",m:"var(--Content-margin)",p:"var(--Content-padding)",width:"var(--Content-width)"},children:t.jsxs(r,{spacing:4,children:[t.jsxs(r,{direction:{xs:"column",sm:"row"},spacing:3,sx:{alignItems:"flex-start"},children:[t.jsx(a,{sx:{flex:"1 1 auto"},children:t.jsx(n,{variant:"h4",children:"Blank"})}),t.jsx("div",{children:t.jsx(s,{startIcon:t.jsx(e,{}),variant:"contained",children:"Action"})})]}),t.jsx(a,{sx:{border:"1px dashed var(--mui-palette-divider)",height:"300px",p:"4px"}})]})})]})}export{g as Page};
//# sourceMappingURL=blank.DI-LJHBv.js.map
