var Cd=Object.defineProperty;var wd=(e,t,r)=>t in e?Cd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Nr=(e,t,r)=>wd(e,typeof t!="symbol"?t+"":t,r);import{r as $d,g as Xi,a as kd}from"./vendor-Csw2ODfV.js";function Rd(e,t){for(var r=0;r<t.length;r++){const o=t[r];if(typeof o!="string"&&!Array.isArray(o)){for(const n in o)if(n!=="default"&&!(n in e)){const s=Object.getOwnPropertyDescriptor(o,n);s&&Object.defineProperty(e,n,s.get?s:{enumerable:!0,get:()=>o[n]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var qs={exports:{}},Do={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ga;function Pd(){if(Ga)return Do;Ga=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function r(o,n,s){var i=null;if(s!==void 0&&(i=""+s),n.key!==void 0&&(i=""+n.key),"key"in n){s={};for(var a in n)a!=="key"&&(s[a]=n[a])}else s=n;return n=s.ref,{$$typeof:e,type:o,key:i,ref:n!==void 0?n:null,props:s}}return Do.Fragment=t,Do.jsx=r,Do.jsxs=r,Do}var Ka;function Td(){return Ka||(Ka=1,qs.exports=Pd()),qs.exports}var w=Td(),p=$d();const Yt=Xi(p),ln=Rd({__proto__:null,default:Yt},[p]);var Uc=kd();const En=Xi(Uc);function Ir(e,...t){const r=new URL(`https://mui.com/production-error/?code=${e}`);return t.forEach(o=>r.searchParams.append("args[]",o)),`Minified MUI error #${e}; visit ${r} for the full message.`}function cn(){return cn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)({}).hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},cn.apply(null,arguments)}function Id(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}function Ed(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),e.nonce!==void 0&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}var Md=(function(){function e(r){var o=this;this._insertTag=function(n){var s;o.tags.length===0?o.insertionPoint?s=o.insertionPoint.nextSibling:o.prepend?s=o.container.firstChild:s=o.before:s=o.tags[o.tags.length-1].nextSibling,o.container.insertBefore(n,s),o.tags.push(n)},this.isSpeedy=r.speedy===void 0?!0:r.speedy,this.tags=[],this.ctr=0,this.nonce=r.nonce,this.key=r.key,this.container=r.container,this.prepend=r.prepend,this.insertionPoint=r.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(o){o.forEach(this._insertTag)},t.insert=function(o){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(Ed(this));var n=this.tags[this.tags.length-1];if(this.isSpeedy){var s=Id(n);try{s.insertRule(o,s.cssRules.length)}catch{}}else n.appendChild(document.createTextNode(o));this.ctr++},t.flush=function(){this.tags.forEach(function(o){var n;return(n=o.parentNode)==null?void 0:n.removeChild(o)}),this.tags=[],this.ctr=0},e})(),Tt="-ms-",Jn="-moz-",Ke="-webkit-",Hc="comm",Qi="rule",Zi="decl",Ad="@import",Vc="@keyframes",Od="@layer",Ld=Math.abs,ps=String.fromCharCode,Bd=Object.assign;function Nd(e,t){return Pt(e,0)^45?(((t<<2^Pt(e,0))<<2^Pt(e,1))<<2^Pt(e,2))<<2^Pt(e,3):0}function _c(e){return e.trim()}function jd(e,t){return(e=t.exec(e))?e[0]:e}function qe(e,t,r){return e.replace(t,r)}function xi(e,t){return e.indexOf(t)}function Pt(e,t){return e.charCodeAt(t)|0}function un(e,t,r){return e.slice(t,r)}function pr(e){return e.length}function Ji(e){return e.length}function Mn(e,t){return t.push(e),e}function zd(e,t){return e.map(t).join("")}var fs=1,Ro=1,Gc=0,Nt=0,yt=0,Lo="";function ms(e,t,r,o,n,s,i){return{value:e,root:t,parent:r,type:o,props:n,children:s,line:fs,column:Ro,length:i,return:""}}function Wo(e,t){return Bd(ms("",null,null,"",null,null,0),e,{length:-e.length},t)}function Fd(){return yt}function Dd(){return yt=Nt>0?Pt(Lo,--Nt):0,Ro--,yt===10&&(Ro=1,fs--),yt}function Wt(){return yt=Nt<Gc?Pt(Lo,Nt++):0,Ro++,yt===10&&(Ro=1,fs++),yt}function hr(){return Pt(Lo,Nt)}function Gn(){return Nt}function bn(e,t){return un(Lo,e,t)}function dn(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Kc(e){return fs=Ro=1,Gc=pr(Lo=e),Nt=0,[]}function qc(e){return Lo="",e}function Kn(e){return _c(bn(Nt-1,Si(e===91?e+2:e===40?e+1:e)))}function Wd(e){for(;(yt=hr())&&yt<33;)Wt();return dn(e)>2||dn(yt)>3?"":" "}function Ud(e,t){for(;--t&&Wt()&&!(yt<48||yt>102||yt>57&&yt<65||yt>70&&yt<97););return bn(e,Gn()+(t<6&&hr()==32&&Wt()==32))}function Si(e){for(;Wt();)switch(yt){case e:return Nt;case 34:case 39:e!==34&&e!==39&&Si(yt);break;case 40:e===41&&Si(e);break;case 92:Wt();break}return Nt}function Hd(e,t){for(;Wt()&&e+yt!==57;)if(e+yt===84&&hr()===47)break;return"/*"+bn(t,Nt-1)+"*"+ps(e===47?e:Wt())}function Vd(e){for(;!dn(hr());)Wt();return bn(e,Nt)}function _d(e){return qc(qn("",null,null,null,[""],e=Kc(e),0,[0],e))}function qn(e,t,r,o,n,s,i,a,l){for(var c=0,u=0,d=i,f=0,m=0,h=0,b=1,y=1,S=1,$=0,C="",v=n,x=s,k=o,T=C;y;)switch(h=$,$=Wt()){case 40:if(h!=108&&Pt(T,d-1)==58){xi(T+=qe(Kn($),"&","&\f"),"&\f")!=-1&&(S=-1);break}case 34:case 39:case 91:T+=Kn($);break;case 9:case 10:case 13:case 32:T+=Wd(h);break;case 92:T+=Ud(Gn()-1,7);continue;case 47:switch(hr()){case 42:case 47:Mn(Gd(Hd(Wt(),Gn()),t,r),l);break;default:T+="/"}break;case 123*b:a[c++]=pr(T)*S;case 125*b:case 59:case 0:switch($){case 0:case 125:y=0;case 59+u:S==-1&&(T=qe(T,/\f/g,"")),m>0&&pr(T)-d&&Mn(m>32?Ya(T+";",o,r,d-1):Ya(qe(T," ","")+";",o,r,d-2),l);break;case 59:T+=";";default:if(Mn(k=qa(T,t,r,c,u,n,a,C,v=[],x=[],d),s),$===123)if(u===0)qn(T,t,k,k,v,s,d,a,x);else switch(f===99&&Pt(T,3)===110?100:f){case 100:case 108:case 109:case 115:qn(e,k,k,o&&Mn(qa(e,k,k,0,0,n,a,C,n,v=[],d),x),n,x,d,a,o?v:x);break;default:qn(T,k,k,k,[""],x,0,a,x)}}c=u=m=0,b=S=1,C=T="",d=i;break;case 58:d=1+pr(T),m=h;default:if(b<1){if($==123)--b;else if($==125&&b++==0&&Dd()==125)continue}switch(T+=ps($),$*b){case 38:S=u>0?1:(T+="\f",-1);break;case 44:a[c++]=(pr(T)-1)*S,S=1;break;case 64:hr()===45&&(T+=Kn(Wt())),f=hr(),u=d=pr(C=T+=Vd(Gn())),$++;break;case 45:h===45&&pr(T)==2&&(b=0)}}return s}function qa(e,t,r,o,n,s,i,a,l,c,u){for(var d=n-1,f=n===0?s:[""],m=Ji(f),h=0,b=0,y=0;h<o;++h)for(var S=0,$=un(e,d+1,d=Ld(b=i[h])),C=e;S<m;++S)(C=_c(b>0?f[S]+" "+$:qe($,/&\f/g,f[S])))&&(l[y++]=C);return ms(e,t,r,n===0?Qi:a,l,c,u)}function Gd(e,t,r){return ms(e,t,r,Hc,ps(Fd()),un(e,2,-2),0)}function Ya(e,t,r,o){return ms(e,t,r,Zi,un(e,0,o),un(e,o+1,-1),o)}function Co(e,t){for(var r="",o=Ji(e),n=0;n<o;n++)r+=t(e[n],n,e,t)||"";return r}function Kd(e,t,r,o){switch(e.type){case Od:if(e.children.length)break;case Ad:case Zi:return e.return=e.return||e.value;case Hc:return"";case Vc:return e.return=e.value+"{"+Co(e.children,o)+"}";case Qi:e.value=e.props.join(",")}return pr(r=Co(e.children,o))?e.return=e.value+"{"+r+"}":""}function qd(e){var t=Ji(e);return function(r,o,n,s){for(var i="",a=0;a<t;a++)i+=e[a](r,o,n,s)||"";return i}}function Yd(e){return function(t){t.root||(t=t.return)&&e(t)}}function Yc(e){var t=Object.create(null);return function(r){return t[r]===void 0&&(t[r]=e(r)),t[r]}}var Xd=function(t,r,o){for(var n=0,s=0;n=s,s=hr(),n===38&&s===12&&(r[o]=1),!dn(s);)Wt();return bn(t,Nt)},Qd=function(t,r){var o=-1,n=44;do switch(dn(n)){case 0:n===38&&hr()===12&&(r[o]=1),t[o]+=Xd(Nt-1,r,o);break;case 2:t[o]+=Kn(n);break;case 4:if(n===44){t[++o]=hr()===58?"&\f":"",r[o]=t[o].length;break}default:t[o]+=ps(n)}while(n=Wt());return t},Zd=function(t,r){return qc(Qd(Kc(t),r))},Xa=new WeakMap,Jd=function(t){if(!(t.type!=="rule"||!t.parent||t.length<1)){for(var r=t.value,o=t.parent,n=t.column===o.column&&t.line===o.line;o.type!=="rule";)if(o=o.parent,!o)return;if(!(t.props.length===1&&r.charCodeAt(0)!==58&&!Xa.get(o))&&!n){Xa.set(t,!0);for(var s=[],i=Zd(r,s),a=o.props,l=0,c=0;l<i.length;l++)for(var u=0;u<a.length;u++,c++)t.props[c]=s[l]?i[l].replace(/&\f/g,a[u]):a[u]+" "+i[l]}}},ep=function(t){if(t.type==="decl"){var r=t.value;r.charCodeAt(0)===108&&r.charCodeAt(2)===98&&(t.return="",t.value="")}};function Xc(e,t){switch(Nd(e,t)){case 5103:return Ke+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Ke+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return Ke+e+Jn+e+Tt+e+e;case 6828:case 4268:return Ke+e+Tt+e+e;case 6165:return Ke+e+Tt+"flex-"+e+e;case 5187:return Ke+e+qe(e,/(\w+).+(:[^]+)/,Ke+"box-$1$2"+Tt+"flex-$1$2")+e;case 5443:return Ke+e+Tt+"flex-item-"+qe(e,/flex-|-self/,"")+e;case 4675:return Ke+e+Tt+"flex-line-pack"+qe(e,/align-content|flex-|-self/,"")+e;case 5548:return Ke+e+Tt+qe(e,"shrink","negative")+e;case 5292:return Ke+e+Tt+qe(e,"basis","preferred-size")+e;case 6060:return Ke+"box-"+qe(e,"-grow","")+Ke+e+Tt+qe(e,"grow","positive")+e;case 4554:return Ke+qe(e,/([^-])(transform)/g,"$1"+Ke+"$2")+e;case 6187:return qe(qe(qe(e,/(zoom-|grab)/,Ke+"$1"),/(image-set)/,Ke+"$1"),e,"")+e;case 5495:case 3959:return qe(e,/(image-set\([^]*)/,Ke+"$1$`$1");case 4968:return qe(qe(e,/(.+:)(flex-)?(.*)/,Ke+"box-pack:$3"+Tt+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Ke+e+e;case 4095:case 3583:case 4068:case 2532:return qe(e,/(.+)-inline(.+)/,Ke+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(pr(e)-1-t>6)switch(Pt(e,t+1)){case 109:if(Pt(e,t+4)!==45)break;case 102:return qe(e,/(.+:)(.+)-([^]+)/,"$1"+Ke+"$2-$3$1"+Jn+(Pt(e,t+3)==108?"$3":"$2-$3"))+e;case 115:return~xi(e,"stretch")?Xc(qe(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(Pt(e,t+1)!==115)break;case 6444:switch(Pt(e,pr(e)-3-(~xi(e,"!important")&&10))){case 107:return qe(e,":",":"+Ke)+e;case 101:return qe(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Ke+(Pt(e,14)===45?"inline-":"")+"box$3$1"+Ke+"$2$3$1"+Tt+"$2box$3")+e}break;case 5936:switch(Pt(e,t+11)){case 114:return Ke+e+Tt+qe(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return Ke+e+Tt+qe(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return Ke+e+Tt+qe(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return Ke+e+Tt+e+e}return e}var tp=function(t,r,o,n){if(t.length>-1&&!t.return)switch(t.type){case Zi:t.return=Xc(t.value,t.length);break;case Vc:return Co([Wo(t,{value:qe(t.value,"@","@"+Ke)})],n);case Qi:if(t.length)return zd(t.props,function(s){switch(jd(s,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Co([Wo(t,{props:[qe(s,/:(read-\w+)/,":"+Jn+"$1")]})],n);case"::placeholder":return Co([Wo(t,{props:[qe(s,/:(plac\w+)/,":"+Ke+"input-$1")]}),Wo(t,{props:[qe(s,/:(plac\w+)/,":"+Jn+"$1")]}),Wo(t,{props:[qe(s,/:(plac\w+)/,Tt+"input-$1")]})],n)}return""})}},rp=[tp],op=function(t){var r=t.key;if(r==="css"){var o=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(o,function(b){var y=b.getAttribute("data-emotion");y.indexOf(" ")!==-1&&(document.head.appendChild(b),b.setAttribute("data-s",""))})}var n=t.stylisPlugins||rp,s={},i,a=[];i=t.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+r+' "]'),function(b){for(var y=b.getAttribute("data-emotion").split(" "),S=1;S<y.length;S++)s[y[S]]=!0;a.push(b)});var l,c=[Jd,ep];{var u,d=[Kd,Yd(function(b){u.insert(b)})],f=qd(c.concat(n,d)),m=function(y){return Co(_d(y),f)};l=function(y,S,$,C){u=$,m(y?y+"{"+S.styles+"}":S.styles),C&&(h.inserted[S.name]=!0)}}var h={key:r,sheet:new Md({key:r,container:i,nonce:t.nonce,speedy:t.speedy,prepend:t.prepend,insertionPoint:t.insertionPoint}),nonce:t.nonce,inserted:s,registered:{},insert:l};return h.sheet.hydrate(a),h},Ys={exports:{}},Ye={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qa;function np(){if(Qa)return Ye;Qa=1;var e=typeof Symbol=="function"&&Symbol.for,t=e?Symbol.for("react.element"):60103,r=e?Symbol.for("react.portal"):60106,o=e?Symbol.for("react.fragment"):60107,n=e?Symbol.for("react.strict_mode"):60108,s=e?Symbol.for("react.profiler"):60114,i=e?Symbol.for("react.provider"):60109,a=e?Symbol.for("react.context"):60110,l=e?Symbol.for("react.async_mode"):60111,c=e?Symbol.for("react.concurrent_mode"):60111,u=e?Symbol.for("react.forward_ref"):60112,d=e?Symbol.for("react.suspense"):60113,f=e?Symbol.for("react.suspense_list"):60120,m=e?Symbol.for("react.memo"):60115,h=e?Symbol.for("react.lazy"):60116,b=e?Symbol.for("react.block"):60121,y=e?Symbol.for("react.fundamental"):60117,S=e?Symbol.for("react.responder"):60118,$=e?Symbol.for("react.scope"):60119;function C(x){if(typeof x=="object"&&x!==null){var k=x.$$typeof;switch(k){case t:switch(x=x.type,x){case l:case c:case o:case s:case n:case d:return x;default:switch(x=x&&x.$$typeof,x){case a:case u:case h:case m:case i:return x;default:return k}}case r:return k}}}function v(x){return C(x)===c}return Ye.AsyncMode=l,Ye.ConcurrentMode=c,Ye.ContextConsumer=a,Ye.ContextProvider=i,Ye.Element=t,Ye.ForwardRef=u,Ye.Fragment=o,Ye.Lazy=h,Ye.Memo=m,Ye.Portal=r,Ye.Profiler=s,Ye.StrictMode=n,Ye.Suspense=d,Ye.isAsyncMode=function(x){return v(x)||C(x)===l},Ye.isConcurrentMode=v,Ye.isContextConsumer=function(x){return C(x)===a},Ye.isContextProvider=function(x){return C(x)===i},Ye.isElement=function(x){return typeof x=="object"&&x!==null&&x.$$typeof===t},Ye.isForwardRef=function(x){return C(x)===u},Ye.isFragment=function(x){return C(x)===o},Ye.isLazy=function(x){return C(x)===h},Ye.isMemo=function(x){return C(x)===m},Ye.isPortal=function(x){return C(x)===r},Ye.isProfiler=function(x){return C(x)===s},Ye.isStrictMode=function(x){return C(x)===n},Ye.isSuspense=function(x){return C(x)===d},Ye.isValidElementType=function(x){return typeof x=="string"||typeof x=="function"||x===o||x===c||x===s||x===n||x===d||x===f||typeof x=="object"&&x!==null&&(x.$$typeof===h||x.$$typeof===m||x.$$typeof===i||x.$$typeof===a||x.$$typeof===u||x.$$typeof===y||x.$$typeof===S||x.$$typeof===$||x.$$typeof===b)},Ye.typeOf=C,Ye}var Za;function sp(){return Za||(Za=1,Ys.exports=np()),Ys.exports}var Xs,Ja;function ip(){if(Ja)return Xs;Ja=1;var e=sp(),t={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},r={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},n={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};s[e.ForwardRef]=o,s[e.Memo]=n;function i(h){return e.isMemo(h)?n:s[h.$$typeof]||t}var a=Object.defineProperty,l=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,u=Object.getOwnPropertyDescriptor,d=Object.getPrototypeOf,f=Object.prototype;function m(h,b,y){if(typeof b!="string"){if(f){var S=d(b);S&&S!==f&&m(h,S,y)}var $=l(b);c&&($=$.concat(c(b)));for(var C=i(h),v=i(b),x=0;x<$.length;++x){var k=$[x];if(!r[k]&&!(y&&y[k])&&!(v&&v[k])&&!(C&&C[k])){var T=u(b,k);try{a(h,k,T)}catch{}}}}return h}return Xs=m,Xs}ip();var ap=!0;function Qc(e,t,r){var o="";return r.split(" ").forEach(function(n){e[n]!==void 0?t.push(e[n]+";"):n&&(o+=n+" ")}),o}var ea=function(t,r,o){var n=t.key+"-"+r.name;(o===!1||ap===!1)&&t.registered[n]===void 0&&(t.registered[n]=r.styles)},ta=function(t,r,o){ea(t,r,o);var n=t.key+"-"+r.name;if(t.inserted[r.name]===void 0){var s=r;do t.insert(r===s?"."+n:"",s,t.sheet,!0),s=s.next;while(s!==void 0)}};function lp(e){for(var t=0,r,o=0,n=e.length;n>=4;++o,n-=4)r=e.charCodeAt(o)&255|(e.charCodeAt(++o)&255)<<8|(e.charCodeAt(++o)&255)<<16|(e.charCodeAt(++o)&255)<<24,r=(r&65535)*1540483477+((r>>>16)*59797<<16),r^=r>>>24,t=(r&65535)*1540483477+((r>>>16)*59797<<16)^(t&65535)*1540483477+((t>>>16)*59797<<16);switch(n){case 3:t^=(e.charCodeAt(o+2)&255)<<16;case 2:t^=(e.charCodeAt(o+1)&255)<<8;case 1:t^=e.charCodeAt(o)&255,t=(t&65535)*1540483477+((t>>>16)*59797<<16)}return t^=t>>>13,t=(t&65535)*1540483477+((t>>>16)*59797<<16),((t^t>>>15)>>>0).toString(36)}var cp={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},up=/[A-Z]|^ms/g,dp=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Zc=function(t){return t.charCodeAt(1)===45},el=function(t){return t!=null&&typeof t!="boolean"},Qs=Yc(function(e){return Zc(e)?e:e.replace(up,"-$&").toLowerCase()}),tl=function(t,r){switch(t){case"animation":case"animationName":if(typeof r=="string")return r.replace(dp,function(o,n,s){return fr={name:n,styles:s,next:fr},n})}return cp[t]!==1&&!Zc(t)&&typeof r=="number"&&r!==0?r+"px":r};function pn(e,t,r){if(r==null)return"";var o=r;if(o.__emotion_styles!==void 0)return o;switch(typeof r){case"boolean":return"";case"object":{var n=r;if(n.anim===1)return fr={name:n.name,styles:n.styles,next:fr},n.name;var s=r;if(s.styles!==void 0){var i=s.next;if(i!==void 0)for(;i!==void 0;)fr={name:i.name,styles:i.styles,next:fr},i=i.next;var a=s.styles+";";return a}return pp(e,t,r)}case"function":{if(e!==void 0){var l=fr,c=r(e);return fr=l,pn(e,t,c)}break}}var u=r;if(t==null)return u;var d=t[u];return d!==void 0?d:u}function pp(e,t,r){var o="";if(Array.isArray(r))for(var n=0;n<r.length;n++)o+=pn(e,t,r[n])+";";else for(var s in r){var i=r[s];if(typeof i!="object"){var a=i;t!=null&&t[a]!==void 0?o+=s+"{"+t[a]+"}":el(a)&&(o+=Qs(s)+":"+tl(s,a)+";")}else if(Array.isArray(i)&&typeof i[0]=="string"&&(t==null||t[i[0]]===void 0))for(var l=0;l<i.length;l++)el(i[l])&&(o+=Qs(s)+":"+tl(s,i[l])+";");else{var c=pn(e,t,i);switch(s){case"animation":case"animationName":{o+=Qs(s)+":"+c+";";break}default:o+=s+"{"+c+"}"}}}return o}var rl=/label:\s*([^\s;{]+)\s*(;|$)/g,fr;function yn(e,t,r){if(e.length===1&&typeof e[0]=="object"&&e[0]!==null&&e[0].styles!==void 0)return e[0];var o=!0,n="";fr=void 0;var s=e[0];if(s==null||s.raw===void 0)o=!1,n+=pn(r,t,s);else{var i=s;n+=i[0]}for(var a=1;a<e.length;a++)if(n+=pn(r,t,e[a]),o){var l=s;n+=l[a]}rl.lastIndex=0;for(var c="",u;(u=rl.exec(n))!==null;)c+="-"+u[1];var d=lp(n)+c;return{name:d,styles:n,next:fr}}var fp=function(t){return t()},Jc=ln.useInsertionEffect?ln.useInsertionEffect:!1,eu=Jc||fp,ol=Jc||p.useLayoutEffect,tu=p.createContext(typeof HTMLElement<"u"?op({key:"css"}):null),i$=tu.Provider,ra=function(t){return p.forwardRef(function(r,o){var n=p.useContext(tu);return t(r,n,o)})},Bo=p.createContext({}),oa={}.hasOwnProperty,Ci="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",mp=function(t,r){var o={};for(var n in r)oa.call(r,n)&&(o[n]=r[n]);return o[Ci]=t,o},gp=function(t){var r=t.cache,o=t.serialized,n=t.isStringTag;return ea(r,o,n),eu(function(){return ta(r,o,n)}),null},hp=ra(function(e,t,r){var o=e.css;typeof o=="string"&&t.registered[o]!==void 0&&(o=t.registered[o]);var n=e[Ci],s=[o],i="";typeof e.className=="string"?i=Qc(t.registered,s,e.className):e.className!=null&&(i=e.className+" ");var a=yn(s,void 0,p.useContext(Bo));i+=t.key+"-"+a.name;var l={};for(var c in e)oa.call(e,c)&&c!=="css"&&c!==Ci&&(l[c]=e[c]);return l.className=i,r&&(l.ref=r),p.createElement(p.Fragment,null,p.createElement(gp,{cache:t,serialized:a,isStringTag:typeof n=="string"}),p.createElement(n,l))}),bp=hp,nl=function(t,r){var o=arguments;if(r==null||!oa.call(r,"css"))return p.createElement.apply(void 0,o);var n=o.length,s=new Array(n);s[0]=bp,s[1]=mp(t,r);for(var i=2;i<n;i++)s[i]=o[i];return p.createElement.apply(null,s)};(function(e){var t;t||(t=e.JSX||(e.JSX={}))})(nl||(nl={}));var yp=ra(function(e,t){var r=e.styles,o=yn([r],void 0,p.useContext(Bo)),n=p.useRef();return ol(function(){var s=t.key+"-global",i=new t.sheet.constructor({key:s,nonce:t.sheet.nonce,container:t.sheet.container,speedy:t.sheet.isSpeedy}),a=!1,l=document.querySelector('style[data-emotion="'+s+" "+o.name+'"]');return t.sheet.tags.length&&(i.before=t.sheet.tags[0]),l!==null&&(a=!0,l.setAttribute("data-emotion",s),i.hydrate([l])),n.current=[i,a],function(){i.flush()}},[t]),ol(function(){var s=n.current,i=s[0],a=s[1];if(a){s[1]=!1;return}if(o.next!==void 0&&ta(t,o.next,!0),i.tags.length){var l=i.tags[i.tags.length-1].nextElementSibling;i.before=l,i.flush()}t.insert("",o,i,!1)},[t,o.name]),null});function _r(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return yn(t)}function wr(){var e=_r.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}var vp=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|popover|popoverTarget|popoverTargetAction|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,xp=Yc(function(e){return vp.test(e)||e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)<91}),Sp=xp,Cp=function(t){return t!=="theme"},sl=function(t){return typeof t=="string"&&t.charCodeAt(0)>96?Sp:Cp},il=function(t,r,o){var n;if(r){var s=r.shouldForwardProp;n=t.__emotion_forwardProp&&s?function(i){return t.__emotion_forwardProp(i)&&s(i)}:s}return typeof n!="function"&&o&&(n=t.__emotion_forwardProp),n},wp=function(t){var r=t.cache,o=t.serialized,n=t.isStringTag;return ea(r,o,n),eu(function(){return ta(r,o,n)}),null},$p=function e(t,r){var o=t.__emotion_real===t,n=o&&t.__emotion_base||t,s,i;r!==void 0&&(s=r.label,i=r.target);var a=il(t,r,o),l=a||sl(n),c=!l("as");return function(){var u=arguments,d=o&&t.__emotion_styles!==void 0?t.__emotion_styles.slice(0):[];if(s!==void 0&&d.push("label:"+s+";"),u[0]==null||u[0].raw===void 0)d.push.apply(d,u);else{var f=u[0];d.push(f[0]);for(var m=u.length,h=1;h<m;h++)d.push(u[h],f[h])}var b=ra(function(y,S,$){var C=c&&y.as||n,v="",x=[],k=y;if(y.theme==null){k={};for(var T in y)k[T]=y[T];k.theme=p.useContext(Bo)}typeof y.className=="string"?v=Qc(S.registered,x,y.className):y.className!=null&&(v=y.className+" ");var I=yn(d.concat(x),S.registered,k);v+=S.key+"-"+I.name,i!==void 0&&(v+=" "+i);var A=c&&a===void 0?sl(C):l,M={};for(var L in y)c&&L==="as"||A(L)&&(M[L]=y[L]);return M.className=v,$&&(M.ref=$),p.createElement(p.Fragment,null,p.createElement(wp,{cache:S,serialized:I,isStringTag:typeof C=="string"}),p.createElement(C,M))});return b.displayName=s!==void 0?s:"Styled("+(typeof n=="string"?n:n.displayName||n.name||"Component")+")",b.defaultProps=t.defaultProps,b.__emotion_real=b,b.__emotion_base=n,b.__emotion_styles=d,b.__emotion_forwardProp=a,Object.defineProperty(b,"toString",{value:function(){return"."+i}}),b.withComponent=function(y,S){var $=e(y,cn({},r,S,{shouldForwardProp:il(b,S,!0)}));return $.apply(void 0,d)},b}},kp=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],wi=$p.bind(null);kp.forEach(function(e){wi[e]=wi(e)});var Zs={exports:{}},Js,al;function Rp(){if(al)return Js;al=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return Js=e,Js}var ei,ll;function Pp(){if(ll)return ei;ll=1;var e=Rp();function t(){}function r(){}return r.resetWarningCache=t,ei=function(){function o(i,a,l,c,u,d){if(d!==e){var f=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw f.name="Invariant Violation",f}}o.isRequired=o;function n(){return o}var s={array:o,bigint:o,bool:o,func:o,number:o,object:o,string:o,symbol:o,any:o,arrayOf:n,element:o,elementType:o,instanceOf:n,node:o,objectOf:n,oneOf:n,oneOfType:n,shape:n,exact:n,checkPropTypes:r,resetWarningCache:t};return s.PropTypes=s,s},ei}var cl;function Tp(){return cl||(cl=1,Zs.exports=Pp()()),Zs.exports}var Ip=Tp();const ti=Xi(Ip);function Ep(e){return e==null||Object.keys(e).length===0}function ru(e){const{styles:t,defaultTheme:r={}}=e,o=typeof t=="function"?n=>t(Ep(n)?r:n):t;return w.jsx(yp,{styles:o})}function na(e,t){return wi(e,t)}function ou(e,t){Array.isArray(e.__emotion_styles)&&(e.__emotion_styles=t(e.__emotion_styles))}const ul=[];function br(e){return ul[0]=e,yn(ul)}var ri={exports:{}},Je={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dl;function Mp(){if(dl)return Je;dl=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),n=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),i=Symbol.for("react.context"),a=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),c=Symbol.for("react.suspense_list"),u=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),f=Symbol.for("react.view_transition"),m=Symbol.for("react.client.reference");function h(b){if(typeof b=="object"&&b!==null){var y=b.$$typeof;switch(y){case e:switch(b=b.type,b){case r:case n:case o:case l:case c:case f:return b;default:switch(b=b&&b.$$typeof,b){case i:case a:case d:case u:return b;case s:return b;default:return y}}case t:return y}}}return Je.ContextConsumer=s,Je.ContextProvider=i,Je.Element=e,Je.ForwardRef=a,Je.Fragment=r,Je.Lazy=d,Je.Memo=u,Je.Portal=t,Je.Profiler=n,Je.StrictMode=o,Je.Suspense=l,Je.SuspenseList=c,Je.isContextConsumer=function(b){return h(b)===s},Je.isContextProvider=function(b){return h(b)===i},Je.isElement=function(b){return typeof b=="object"&&b!==null&&b.$$typeof===e},Je.isForwardRef=function(b){return h(b)===a},Je.isFragment=function(b){return h(b)===r},Je.isLazy=function(b){return h(b)===d},Je.isMemo=function(b){return h(b)===u},Je.isPortal=function(b){return h(b)===t},Je.isProfiler=function(b){return h(b)===n},Je.isStrictMode=function(b){return h(b)===o},Je.isSuspense=function(b){return h(b)===l},Je.isSuspenseList=function(b){return h(b)===c},Je.isValidElementType=function(b){return typeof b=="string"||typeof b=="function"||b===r||b===n||b===o||b===l||b===c||typeof b=="object"&&b!==null&&(b.$$typeof===d||b.$$typeof===u||b.$$typeof===i||b.$$typeof===s||b.$$typeof===a||b.$$typeof===m||b.getModuleId!==void 0)},Je.typeOf=h,Je}var pl;function Ap(){return pl||(pl=1,ri.exports=Mp()),ri.exports}var gs=Ap();function gr(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function nu(e){if(p.isValidElement(e)||gs.isValidElementType(e)||!gr(e))return e;const t={};return Object.keys(e).forEach(r=>{t[r]=nu(e[r])}),t}function $t(e,t,r={clone:!0}){const o=r.clone?{...e}:e;return gr(e)&&gr(t)&&Object.keys(t).forEach(n=>{p.isValidElement(t[n])||gs.isValidElementType(t[n])?o[n]=t[n]:gr(t[n])&&Object.prototype.hasOwnProperty.call(e,n)&&gr(e[n])?o[n]=$t(e[n],t[n],r):r.clone?o[n]=gr(t[n])?nu(t[n]):t[n]:o[n]=t[n]}),o}const Op=e=>{const t=Object.keys(e).map(r=>({key:r,val:e[r]}))||[];return t.sort((r,o)=>r.val-o.val),t.reduce((r,o)=>({...r,[o.key]:o.val}),{})};function Lp(e){const{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:r="px",step:o=5,...n}=e,s=Op(t),i=Object.keys(s);function a(f){return`@media (min-width:${typeof t[f]=="number"?t[f]:f}${r})`}function l(f){return`@media (max-width:${(typeof t[f]=="number"?t[f]:f)-o/100}${r})`}function c(f,m){const h=i.indexOf(m);return`@media (min-width:${typeof t[f]=="number"?t[f]:f}${r}) and (max-width:${(h!==-1&&typeof t[i[h]]=="number"?t[i[h]]:m)-o/100}${r})`}function u(f){return i.indexOf(f)+1<i.length?c(f,i[i.indexOf(f)+1]):a(f)}function d(f){const m=i.indexOf(f);return m===0?a(i[1]):m===i.length-1?l(i[m]):c(f,i[i.indexOf(f)+1]).replace("@media","@media not all and")}return{keys:i,values:s,up:a,down:l,between:c,only:u,not:d,unit:r,...n}}function fl(e,t){if(!e.containerQueries)return t;const r=Object.keys(t).filter(o=>o.startsWith("@container")).sort((o,n)=>{var i,a;const s=/min-width:\s*([0-9.]+)/;return+(((i=o.match(s))==null?void 0:i[1])||0)-+(((a=n.match(s))==null?void 0:a[1])||0)});return r.length?r.reduce((o,n)=>{const s=t[n];return delete o[n],o[n]=s,o},{...t}):t}function Bp(e,t){return t==="@"||t.startsWith("@")&&(e.some(r=>t.startsWith(`@${r}`))||!!t.match(/^@\d/))}function Np(e,t){const r=t.match(/^@([^/]+)?\/?(.+)?$/);if(!r)return null;const[,o,n]=r,s=Number.isNaN(+o)?o||0:+o;return e.containerQueries(n).up(s)}function jp(e){const t=(s,i)=>s.replace("@media",i?`@container ${i}`:"@container");function r(s,i){s.up=(...a)=>t(e.breakpoints.up(...a),i),s.down=(...a)=>t(e.breakpoints.down(...a),i),s.between=(...a)=>t(e.breakpoints.between(...a),i),s.only=(...a)=>t(e.breakpoints.only(...a),i),s.not=(...a)=>{const l=t(e.breakpoints.not(...a),i);return l.includes("not all and")?l.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):l}}const o={},n=s=>(r(o,s),o);return r(n),{...e,containerQueries:n}}const zp={borderRadius:4};function Zo(e,t){return t?$t(e,t,{clone:!1}):e}const hs={xs:0,sm:600,md:900,lg:1200,xl:1536},ml={keys:["xs","sm","md","lg","xl"],up:e=>`@media (min-width:${hs[e]}px)`},Fp={containerQueries:e=>({up:t=>{let r=typeof t=="number"?t:hs[t]||t;return typeof r=="number"&&(r=`${r}px`),e?`@container ${e} (min-width:${r})`:`@container (min-width:${r})`}})};function lr(e,t,r){const o=e.theme||{};if(Array.isArray(t)){const s=o.breakpoints||ml;return t.reduce((i,a,l)=>(i[s.up(s.keys[l])]=r(t[l]),i),{})}if(typeof t=="object"){const s=o.breakpoints||ml;return Object.keys(t).reduce((i,a)=>{if(Bp(s.keys,a)){const l=Np(o.containerQueries?o:Fp,a);l&&(i[l]=r(t[a],a))}else if(Object.keys(s.values||hs).includes(a)){const l=s.up(a);i[l]=r(t[a],a)}else{const l=a;i[l]=t[l]}return i},{})}return r(t)}function su(e={}){var r;return((r=e.keys)==null?void 0:r.reduce((o,n)=>{const s=e.up(n);return o[s]={},o},{}))||{}}function $i(e,t){return e.reduce((r,o)=>{const n=r[o];return(!n||Object.keys(n).length===0)&&delete r[o],r},t)}function Dp(e,...t){const r=su(e),o=[r,...t].reduce((n,s)=>$t(n,s),{});return $i(Object.keys(r),o)}function Wp(e,t){if(typeof e!="object")return{};const r={},o=Object.keys(t);return Array.isArray(e)?o.forEach((n,s)=>{s<e.length&&(r[n]=!0)}):o.forEach(n=>{e[n]!=null&&(r[n]=!0)}),r}function oi({values:e,breakpoints:t,base:r}){const o=r||Wp(e,t),n=Object.keys(o);if(n.length===0)return e;let s;return n.reduce((i,a,l)=>(Array.isArray(e)?(i[a]=e[l]!=null?e[l]:e[s],s=l):typeof e=="object"?(i[a]=e[a]!=null?e[a]:e[s],s=a):i[a]=e,i),{})}function B(e){if(typeof e!="string")throw new Error(Ir(7));return e.charAt(0).toUpperCase()+e.slice(1)}function mr(e,t,r=!0){if(!t||typeof t!="string")return null;if(e&&e.vars&&r){const o=`vars.${t}`.split(".").reduce((n,s)=>n&&n[s]?n[s]:null,e);if(o!=null)return o}return t.split(".").reduce((o,n)=>o&&o[n]!=null?o[n]:null,e)}function es(e,t,r,o=r){let n;return typeof e=="function"?n=e(r):Array.isArray(e)?n=e[r]||o:n=mr(e,r)||o,t&&(n=t(n,o,e)),n}function gt(e){const{prop:t,cssProperty:r=e.prop,themeKey:o,transform:n}=e,s=i=>{if(i[t]==null)return null;const a=i[t],l=i.theme,c=mr(l,o)||{};return lr(i,a,d=>{let f=es(c,n,d);return d===f&&typeof d=="string"&&(f=es(c,n,`${t}${d==="default"?"":B(d)}`,d)),r===!1?f:{[r]:f}})};return s.propTypes={},s.filterProps=[t],s}function Up(e){const t={};return r=>(t[r]===void 0&&(t[r]=e(r)),t[r])}const Hp={m:"margin",p:"padding"},Vp={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},gl={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},_p=Up(e=>{if(e.length>2)if(gl[e])e=gl[e];else return[e];const[t,r]=e.split(""),o=Hp[t],n=Vp[r]||"";return Array.isArray(n)?n.map(s=>o+s):[o+n]}),sa=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],ia=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...sa,...ia];function vn(e,t,r,o){const n=mr(e,t,!0)??r;return typeof n=="number"||typeof n=="string"?s=>typeof s=="string"?s:typeof n=="string"?n.startsWith("var(")&&s===0?0:n.startsWith("var(")&&s===1?n:`calc(${s} * ${n})`:n*s:Array.isArray(n)?s=>{if(typeof s=="string")return s;const i=Math.abs(s),a=n[i];return s>=0?a:typeof a=="number"?-a:typeof a=="string"&&a.startsWith("var(")?`calc(-1 * ${a})`:`-${a}`}:typeof n=="function"?n:()=>{}}function bs(e){return vn(e,"spacing",8)}function ro(e,t){return typeof t=="string"||t==null?t:e(t)}function Gp(e,t){return r=>e.reduce((o,n)=>(o[n]=ro(t,r),o),{})}function Kp(e,t,r,o){if(!t.includes(r))return null;const n=_p(r),s=Gp(n,o),i=e[r];return lr(e,i,s)}function iu(e,t){const r=bs(e.theme);return Object.keys(e).map(o=>Kp(e,t,o,r)).reduce(Zo,{})}function it(e){return iu(e,sa)}it.propTypes={};it.filterProps=sa;function at(e){return iu(e,ia)}at.propTypes={};at.filterProps=ia;function au(e=8,t=bs({spacing:e})){if(e.mui)return e;const r=(...o)=>(o.length===0?[1]:o).map(s=>{const i=t(s);return typeof i=="number"?`${i}px`:i}).join(" ");return r.mui=!0,r}function ys(...e){const t=e.reduce((o,n)=>(n.filterProps.forEach(s=>{o[s]=n}),o),{}),r=o=>Object.keys(o).reduce((n,s)=>t[s]?Zo(n,t[s](o)):n,{});return r.propTypes={},r.filterProps=e.reduce((o,n)=>o.concat(n.filterProps),[]),r}function Kt(e){return typeof e!="number"?e:`${e}px solid`}function or(e,t){return gt({prop:e,themeKey:"borders",transform:t})}const qp=or("border",Kt),Yp=or("borderTop",Kt),Xp=or("borderRight",Kt),Qp=or("borderBottom",Kt),Zp=or("borderLeft",Kt),Jp=or("borderColor"),ef=or("borderTopColor"),tf=or("borderRightColor"),rf=or("borderBottomColor"),of=or("borderLeftColor"),nf=or("outline",Kt),sf=or("outlineColor"),vs=e=>{if(e.borderRadius!==void 0&&e.borderRadius!==null){const t=vn(e.theme,"shape.borderRadius",4),r=o=>({borderRadius:ro(t,o)});return lr(e,e.borderRadius,r)}return null};vs.propTypes={};vs.filterProps=["borderRadius"];ys(qp,Yp,Xp,Qp,Zp,Jp,ef,tf,rf,of,vs,nf,sf);const xs=e=>{if(e.gap!==void 0&&e.gap!==null){const t=vn(e.theme,"spacing",8),r=o=>({gap:ro(t,o)});return lr(e,e.gap,r)}return null};xs.propTypes={};xs.filterProps=["gap"];const Ss=e=>{if(e.columnGap!==void 0&&e.columnGap!==null){const t=vn(e.theme,"spacing",8),r=o=>({columnGap:ro(t,o)});return lr(e,e.columnGap,r)}return null};Ss.propTypes={};Ss.filterProps=["columnGap"];const Cs=e=>{if(e.rowGap!==void 0&&e.rowGap!==null){const t=vn(e.theme,"spacing",8),r=o=>({rowGap:ro(t,o)});return lr(e,e.rowGap,r)}return null};Cs.propTypes={};Cs.filterProps=["rowGap"];const af=gt({prop:"gridColumn"}),lf=gt({prop:"gridRow"}),cf=gt({prop:"gridAutoFlow"}),uf=gt({prop:"gridAutoColumns"}),df=gt({prop:"gridAutoRows"}),pf=gt({prop:"gridTemplateColumns"}),ff=gt({prop:"gridTemplateRows"}),mf=gt({prop:"gridTemplateAreas"}),gf=gt({prop:"gridArea"});ys(xs,Ss,Cs,af,lf,cf,uf,df,pf,ff,mf,gf);function wo(e,t){return t==="grey"?t:e}const hf=gt({prop:"color",themeKey:"palette",transform:wo}),bf=gt({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:wo}),yf=gt({prop:"backgroundColor",themeKey:"palette",transform:wo});ys(hf,bf,yf);function Ft(e){return e<=1&&e!==0?`${e*100}%`:e}const vf=gt({prop:"width",transform:Ft}),aa=e=>{if(e.maxWidth!==void 0&&e.maxWidth!==null){const t=r=>{var n,s,i,a,l;const o=((i=(s=(n=e.theme)==null?void 0:n.breakpoints)==null?void 0:s.values)==null?void 0:i[r])||hs[r];return o?((l=(a=e.theme)==null?void 0:a.breakpoints)==null?void 0:l.unit)!=="px"?{maxWidth:`${o}${e.theme.breakpoints.unit}`}:{maxWidth:o}:{maxWidth:Ft(r)}};return lr(e,e.maxWidth,t)}return null};aa.filterProps=["maxWidth"];const xf=gt({prop:"minWidth",transform:Ft}),Sf=gt({prop:"height",transform:Ft}),Cf=gt({prop:"maxHeight",transform:Ft}),wf=gt({prop:"minHeight",transform:Ft});gt({prop:"size",cssProperty:"width",transform:Ft});gt({prop:"size",cssProperty:"height",transform:Ft});const $f=gt({prop:"boxSizing"});ys(vf,aa,xf,Sf,Cf,wf,$f);const xn={border:{themeKey:"borders",transform:Kt},borderTop:{themeKey:"borders",transform:Kt},borderRight:{themeKey:"borders",transform:Kt},borderBottom:{themeKey:"borders",transform:Kt},borderLeft:{themeKey:"borders",transform:Kt},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:Kt},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:vs},color:{themeKey:"palette",transform:wo},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:wo},backgroundColor:{themeKey:"palette",transform:wo},p:{style:at},pt:{style:at},pr:{style:at},pb:{style:at},pl:{style:at},px:{style:at},py:{style:at},padding:{style:at},paddingTop:{style:at},paddingRight:{style:at},paddingBottom:{style:at},paddingLeft:{style:at},paddingX:{style:at},paddingY:{style:at},paddingInline:{style:at},paddingInlineStart:{style:at},paddingInlineEnd:{style:at},paddingBlock:{style:at},paddingBlockStart:{style:at},paddingBlockEnd:{style:at},m:{style:it},mt:{style:it},mr:{style:it},mb:{style:it},ml:{style:it},mx:{style:it},my:{style:it},margin:{style:it},marginTop:{style:it},marginRight:{style:it},marginBottom:{style:it},marginLeft:{style:it},marginX:{style:it},marginY:{style:it},marginInline:{style:it},marginInlineStart:{style:it},marginInlineEnd:{style:it},marginBlock:{style:it},marginBlockStart:{style:it},marginBlockEnd:{style:it},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:xs},rowGap:{style:Cs},columnGap:{style:Ss},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Ft},maxWidth:{style:aa},minWidth:{transform:Ft},height:{transform:Ft},maxHeight:{transform:Ft},minHeight:{transform:Ft},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function kf(...e){const t=e.reduce((o,n)=>o.concat(Object.keys(n)),[]),r=new Set(t);return e.every(o=>r.size===Object.keys(o).length)}function Rf(e,t){return typeof e=="function"?e(t):e}function Pf(){function e(r,o,n,s){const i={[r]:o,theme:n},a=s[r];if(!a)return{[r]:o};const{cssProperty:l=r,themeKey:c,transform:u,style:d}=a;if(o==null)return null;if(c==="typography"&&o==="inherit")return{[r]:o};const f=mr(n,c)||{};return d?d(i):lr(i,o,h=>{let b=es(f,u,h);return h===b&&typeof h=="string"&&(b=es(f,u,`${r}${h==="default"?"":B(h)}`,h)),l===!1?b:{[l]:b}})}function t(r){const{sx:o,theme:n={},nested:s}=r||{};if(!o)return null;const i=n.unstable_sxConfig??xn;function a(l){let c=l;if(typeof l=="function")c=l(n);else if(typeof l!="object")return l;if(!c)return null;const u=su(n.breakpoints),d=Object.keys(u);let f=u;return Object.keys(c).forEach(m=>{const h=Rf(c[m],n);if(h!=null)if(typeof h=="object")if(i[m])f=Zo(f,e(m,h,n,i));else{const b=lr({theme:n},h,y=>({[m]:y}));kf(b,h)?f[m]=t({sx:h,theme:n,nested:!0}):f=Zo(f,b)}else f=Zo(f,e(m,h,n,i))}),!s&&n.modularCssLayers?{"@layer sx":fl(n,$i(d,f))}:fl(n,$i(d,f))}return Array.isArray(o)?o.map(a):a(o)}return t}const Ur=Pf();Ur.filterProps=["sx"];function Tf(e,t){var o;const r=this;if(r.vars){if(!((o=r.colorSchemes)!=null&&o[e])||typeof r.getColorSchemeSelector!="function")return{};let n=r.getColorSchemeSelector(e);return n==="&"?t:((n.includes("data-")||n.includes("."))&&(n=`*:where(${n.replace(/\s*&$/,"")}) &`),{[n]:t})}return r.palette.mode===e?t:{}}function No(e={},...t){const{breakpoints:r={},palette:o={},spacing:n,shape:s={},...i}=e,a=Lp(r),l=au(n);let c=$t({breakpoints:a,direction:"ltr",components:{},palette:{mode:"light",...o},spacing:l,shape:{...zp,...s}},i);return c=jp(c),c.applyStyles=Tf,c=t.reduce((u,d)=>$t(u,d),c),c.unstable_sxConfig={...xn,...i==null?void 0:i.unstable_sxConfig},c.unstable_sx=function(d){return Ur({sx:d,theme:this})},c}function If(e){return Object.keys(e).length===0}function ws(e=null){const t=p.useContext(Bo);return!t||If(t)?e:t}const Ef=No();function Sn(e=Ef){return ws(e)}function ni(e){const t=br(e);return e!==t&&t.styles?(t.styles.match(/^@layer\s+[^{]*$/)||(t.styles=`@layer global{${t.styles}}`),t):e}function lu({styles:e,themeId:t,defaultTheme:r={}}){const o=Sn(r),n=t&&o[t]||o;let s=typeof e=="function"?e(n):e;return n.modularCssLayers&&(Array.isArray(s)?s=s.map(i=>ni(typeof i=="function"?i(n):i)):s=ni(s)),w.jsx(ru,{styles:s})}const Mf=e=>{var o;const t={systemProps:{},otherProps:{}},r=((o=e==null?void 0:e.theme)==null?void 0:o.unstable_sxConfig)??xn;return Object.keys(e).forEach(n=>{r[n]?t.systemProps[n]=e[n]:t.otherProps[n]=e[n]}),t};function $s(e){const{sx:t,...r}=e,{systemProps:o,otherProps:n}=Mf(r);let s;return Array.isArray(t)?s=[o,...t]:typeof t=="function"?s=(...i)=>{const a=t(...i);return gr(a)?{...o,...a}:o}:s={...o,...t},{...n,sx:s}}const hl=e=>e,Af=()=>{let e=hl;return{configure(t){e=t},generate(t){return e(t)},reset(){e=hl}}},cu=Af();function uu(e){var t,r,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var n=e.length;for(t=0;t<n;t++)e[t]&&(r=uu(e[t]))&&(o&&(o+=" "),o+=r)}else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}function V(){for(var e,t,r=0,o="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=uu(e))&&(o&&(o+=" "),o+=t);return o}function Of(e={}){const{themeId:t,defaultTheme:r,defaultClassName:o="MuiBox-root",generateClassName:n}=e,s=na("div",{shouldForwardProp:a=>a!=="theme"&&a!=="sx"&&a!=="as"})(Ur);return p.forwardRef(function(l,c){const u=Sn(r),{className:d,component:f="div",...m}=$s(l);return w.jsx(s,{as:f,ref:c,className:V(d,n?n(o):o),theme:t&&u[t]||u,...m})})}const Lf={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function ce(e,t,r="Mui"){const o=Lf[t];return o?`${r}-${o}`:`${cu.generate(e)}-${t}`}function ue(e,t,r="Mui"){const o={};return t.forEach(n=>{o[n]=ce(e,n,r)}),o}function du(e){const{variants:t,...r}=e,o={variants:t,style:br(r),isProcessed:!0};return o.style===r||t&&t.forEach(n=>{typeof n.style!="function"&&(n.style=br(n.style))}),o}const Bf=No();function si(e){return e!=="ownerState"&&e!=="theme"&&e!=="sx"&&e!=="as"}function Zr(e,t){return t&&e&&typeof e=="object"&&e.styles&&!e.styles.startsWith("@layer")&&(e.styles=`@layer ${t}{${String(e.styles)}}`),e}function Nf(e){return e?(t,r)=>r[e]:null}function jf(e,t,r){e.theme=Ff(e.theme)?r:e.theme[t]||e.theme}function Yn(e,t,r){const o=typeof t=="function"?t(e):t;if(Array.isArray(o))return o.flatMap(n=>Yn(e,n,r));if(Array.isArray(o==null?void 0:o.variants)){let n;if(o.isProcessed)n=r?Zr(o.style,r):o.style;else{const{variants:s,...i}=o;n=r?Zr(br(i),r):i}return pu(e,o.variants,[n],r)}return o!=null&&o.isProcessed?r?Zr(br(o.style),r):o.style:r?Zr(br(o),r):o}function pu(e,t,r=[],o=void 0){var s;let n;e:for(let i=0;i<t.length;i+=1){const a=t[i];if(typeof a.props=="function"){if(n??(n={...e,...e.ownerState,ownerState:e.ownerState}),!a.props(n))continue}else for(const l in a.props)if(e[l]!==a.props[l]&&((s=e.ownerState)==null?void 0:s[l])!==a.props[l])continue e;typeof a.style=="function"?(n??(n={...e,...e.ownerState,ownerState:e.ownerState}),r.push(o?Zr(br(a.style(n)),o):a.style(n))):r.push(o?Zr(br(a.style),o):a.style)}return r}function fu(e={}){const{themeId:t,defaultTheme:r=Bf,rootShouldForwardProp:o=si,slotShouldForwardProp:n=si}=e;function s(a){jf(a,t,r)}return(a,l={})=>{ou(a,k=>k.filter(T=>T!==Ur));const{name:c,slot:u,skipVariantsResolver:d,skipSx:f,overridesResolver:m=Nf(Wf(u)),...h}=l,b=c&&c.startsWith("Mui")||u?"components":"custom",y=d!==void 0?d:u&&u!=="Root"&&u!=="root"||!1,S=f||!1;let $=si;u==="Root"||u==="root"?$=o:u?$=n:Df(a)&&($=void 0);const C=na(a,{shouldForwardProp:$,label:zf(),...h}),v=k=>{if(k.__emotion_real===k)return k;if(typeof k=="function")return function(I){return Yn(I,k,I.theme.modularCssLayers?b:void 0)};if(gr(k)){const T=du(k);return function(A){return T.variants?Yn(A,T,A.theme.modularCssLayers?b:void 0):A.theme.modularCssLayers?Zr(T.style,b):T.style}}return k},x=(...k)=>{const T=[],I=k.map(v),A=[];if(T.push(s),c&&m&&A.push(function(P){var F,z;const O=(z=(F=P.theme.components)==null?void 0:F[c])==null?void 0:z.styleOverrides;if(!O)return null;const E={};for(const N in O)E[N]=Yn(P,O[N],P.theme.modularCssLayers?"theme":void 0);return m(P,E)}),c&&!y&&A.push(function(P){var E,F;const R=P.theme,O=(F=(E=R==null?void 0:R.components)==null?void 0:E[c])==null?void 0:F.variants;return O?pu(P,O,[],P.theme.modularCssLayers?"theme":void 0):null}),S||A.push(Ur),Array.isArray(I[0])){const g=I.shift(),P=new Array(T.length).fill(""),R=new Array(A.length).fill("");let O;O=[...P,...g,...R],O.raw=[...P,...g.raw,...R],T.unshift(O)}const M=[...T,...I,...A],L=C(...M);return a.muiName&&(L.muiName=a.muiName),L};return C.withConfig&&(x.withConfig=C.withConfig),x}}function zf(e,t){return void 0}function Ff(e){for(const t in e)return!1;return!0}function Df(e){return typeof e=="string"&&e.charCodeAt(0)>96}function Wf(e){return e&&e.charAt(0).toLowerCase()+e.slice(1)}const la=fu();function Po(e,t,r=!1){const o={...t};for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)){const s=n;if(s==="components"||s==="slots")o[s]={...e[s],...o[s]};else if(s==="componentsProps"||s==="slotProps"){const i=e[s],a=t[s];if(!a)o[s]=i||{};else if(!i)o[s]=a;else{o[s]={...a};for(const l in i)if(Object.prototype.hasOwnProperty.call(i,l)){const c=l;o[s][c]=Po(i[c],a[c],r)}}}else s==="className"&&r&&t.className?o.className=V(e==null?void 0:e.className,t==null?void 0:t.className):s==="style"&&r&&t.style?o.style={...e==null?void 0:e.style,...t==null?void 0:t.style}:o[s]===void 0&&(o[s]=e[s])}return o}function mu(e){const{theme:t,name:r,props:o}=e;return!t||!t.components||!t.components[r]||!t.components[r].defaultProps?o:Po(t.components[r].defaultProps,o)}function ks({props:e,name:t,defaultTheme:r,themeId:o}){let n=Sn(r);return o&&(n=n[o]||n),mu({theme:n,name:t,props:e})}const It=typeof window<"u"?p.useLayoutEffect:p.useEffect;function Uf(e,t,r,o,n){const[s,i]=p.useState(()=>n&&r?r(e).matches:o?o(e).matches:t);return It(()=>{if(!r)return;const a=r(e),l=()=>{i(a.matches)};return l(),a.addEventListener("change",l),()=>{a.removeEventListener("change",l)}},[e,r]),s}const Hf={...ln},gu=Hf.useSyncExternalStore;function Vf(e,t,r,o,n){const s=p.useCallback(()=>t,[t]),i=p.useMemo(()=>{if(n&&r)return()=>r(e).matches;if(o!==null){const{matches:u}=o(e);return()=>u}return s},[s,e,o,n,r]),[a,l]=p.useMemo(()=>{if(r===null)return[s,()=>()=>{}];const u=r(e);return[()=>u.matches,d=>(u.addEventListener("change",d),()=>{u.removeEventListener("change",d)})]},[s,r,e]);return gu(l,a,i)}function hu(e={}){const{themeId:t}=e;return function(o,n={}){let s=ws();s&&t&&(s=s[t]||s);const i=typeof window<"u"&&typeof window.matchMedia<"u",{defaultMatches:a=!1,matchMedia:l=i?window.matchMedia:null,ssrMatchMedia:c=null,noSsr:u=!1}=mu({name:"MuiUseMediaQuery",props:n,theme:s});let d=typeof o=="function"?o(s):o;return d=d.replace(/^@media( ?)/m,""),d.includes("print")&&console.warn(["MUI: You have provided a `print` query to the `useMediaQuery` hook.","Using the print media query to modify print styles can lead to unexpected results.","Consider using the `displayPrint` field in the `sx` prop instead.","More information about `displayPrint` on our docs: https://mui.com/system/display/#display-in-print."].join(`
`)),(gu!==void 0?Vf:Uf)(d,a,l,c,u)}}hu();function ho(e,t=Number.MIN_SAFE_INTEGER,r=Number.MAX_SAFE_INTEGER){return Math.max(t,Math.min(e,r))}function ca(e,t=0,r=1){return ho(e,t,r)}function _f(e){e=e.slice(1);const t=new RegExp(`.{1,${e.length>=6?2:1}}`,"g");let r=e.match(t);return r&&r[0].length===1&&(r=r.map(o=>o+o)),r?`rgb${r.length===4?"a":""}(${r.map((o,n)=>n<3?parseInt(o,16):Math.round(parseInt(o,16)/255*1e3)/1e3).join(", ")})`:""}function Hr(e){if(e.type)return e;if(e.charAt(0)==="#")return Hr(_f(e));const t=e.indexOf("("),r=e.substring(0,t);if(!["rgb","rgba","hsl","hsla","color"].includes(r))throw new Error(Ir(9,e));let o=e.substring(t+1,e.length-1),n;if(r==="color"){if(o=o.split(" "),n=o.shift(),o.length===4&&o[3].charAt(0)==="/"&&(o[3]=o[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(n))throw new Error(Ir(10,n))}else o=o.split(",");return o=o.map(s=>parseFloat(s)),{type:r,values:o,colorSpace:n}}const Gf=e=>{const t=Hr(e);return t.values.slice(0,3).map((r,o)=>t.type.includes("hsl")&&o!==0?`${r}%`:r).join(" ")},qo=(e,t)=>{try{return Gf(e)}catch{return e}};function Rs(e){const{type:t,colorSpace:r}=e;let{values:o}=e;return t.includes("rgb")?o=o.map((n,s)=>s<3?parseInt(n,10):n):t.includes("hsl")&&(o[1]=`${o[1]}%`,o[2]=`${o[2]}%`),t.includes("color")?o=`${r} ${o.join(" ")}`:o=`${o.join(", ")}`,`${t}(${o})`}function bu(e){e=Hr(e);const{values:t}=e,r=t[0],o=t[1]/100,n=t[2]/100,s=o*Math.min(n,1-n),i=(c,u=(c+r/30)%12)=>n-s*Math.max(Math.min(u-3,9-u,1),-1);let a="rgb";const l=[Math.round(i(0)*255),Math.round(i(8)*255),Math.round(i(4)*255)];return e.type==="hsla"&&(a+="a",l.push(t[3])),Rs({type:a,values:l})}function ki(e){e=Hr(e);let t=e.type==="hsl"||e.type==="hsla"?Hr(bu(e)).values:e.values;return t=t.map(r=>(e.type!=="color"&&(r/=255),r<=.03928?r/12.92:((r+.055)/1.055)**2.4)),Number((.2126*t[0]+.7152*t[1]+.0722*t[2]).toFixed(3))}function Kf(e,t){const r=ki(e),o=ki(t);return(Math.max(r,o)+.05)/(Math.min(r,o)+.05)}function fn(e,t){return e=Hr(e),t=ca(t),(e.type==="rgb"||e.type==="hsl")&&(e.type+="a"),e.type==="color"?e.values[3]=`/${t}`:e.values[3]=t,Rs(e)}function qr(e,t,r){try{return fn(e,t)}catch{return e}}function Ps(e,t){if(e=Hr(e),t=ca(t),e.type.includes("hsl"))e.values[2]*=1-t;else if(e.type.includes("rgb")||e.type.includes("color"))for(let r=0;r<3;r+=1)e.values[r]*=1-t;return Rs(e)}function Xe(e,t,r){try{return Ps(e,t)}catch{return e}}function Ts(e,t){if(e=Hr(e),t=ca(t),e.type.includes("hsl"))e.values[2]+=(100-e.values[2])*t;else if(e.type.includes("rgb"))for(let r=0;r<3;r+=1)e.values[r]+=(255-e.values[r])*t;else if(e.type.includes("color"))for(let r=0;r<3;r+=1)e.values[r]+=(1-e.values[r])*t;return Rs(e)}function Qe(e,t,r){try{return Ts(e,t)}catch{return e}}function qf(e,t=.15){return ki(e)>.5?Ps(e,t):Ts(e,t)}function An(e,t,r){try{return qf(e,t)}catch{return e}}const yu=p.createContext(null);function ua(){return p.useContext(yu)}const Yf=typeof Symbol=="function"&&Symbol.for,Xf=Yf?Symbol.for("mui.nested"):"__THEME_NESTED__";function Qf(e,t){return typeof t=="function"?t(e):{...e,...t}}function Zf(e){const{children:t,theme:r}=e,o=ua(),n=p.useMemo(()=>{const s=o===null?{...r}:Qf(o,r);return s!=null&&(s[Xf]=o!==null),s},[r,o]);return w.jsx(yu.Provider,{value:n,children:t})}const vu=p.createContext();function Jf({value:e,...t}){return w.jsx(vu.Provider,{value:e??!0,...t})}const Mr=()=>p.useContext(vu)??!1,xu=p.createContext(void 0);function em({value:e,children:t}){return w.jsx(xu.Provider,{value:e,children:t})}function tm(e){const{theme:t,name:r,props:o}=e;if(!t||!t.components||!t.components[r])return o;const n=t.components[r];return n.defaultProps?Po(n.defaultProps,o,t.components.mergeClassNameAndStyle):!n.styleOverrides&&!n.variants?Po(n,o,t.components.mergeClassNameAndStyle):o}function rm({props:e,name:t}){const r=p.useContext(xu);return tm({props:e,name:t,theme:{components:r}})}let bl=0;function om(e){const[t,r]=p.useState(e),o=e||t;return p.useEffect(()=>{t==null&&(bl+=1,r(`mui-${bl}`))},[t]),o}const nm={...ln},yl=nm.useId;function xr(e){if(yl!==void 0){const t=yl();return e??t}return om(e)}function sm(e){const t=ws(),r=xr()||"",{modularCssLayers:o}=e;let n="mui.global, mui.components, mui.theme, mui.custom, mui.sx";return!o||t!==null?n="":typeof o=="string"?n=o.replace(/mui(?!\.)/g,n):n=`@layer ${n};`,It(()=>{var a,l;const s=document.querySelector("head");if(!s)return;const i=s.firstChild;if(n){if(i&&((a=i.hasAttribute)!=null&&a.call(i,"data-mui-layer-order"))&&i.getAttribute("data-mui-layer-order")===r)return;const c=document.createElement("style");c.setAttribute("data-mui-layer-order",r),c.textContent=n,s.prepend(c)}else(l=s.querySelector(`style[data-mui-layer-order="${r}"]`))==null||l.remove()},[n,r]),n?w.jsx(lu,{styles:n}):null}const vl={};function xl(e,t,r,o=!1){return p.useMemo(()=>{const n=e&&t[e]||t;if(typeof r=="function"){const s=r(n),i=e?{...t,[e]:s}:s;return o?()=>i:i}return e?{...t,[e]:r}:{...t,...r}},[e,t,r,o])}function Su(e){const{children:t,theme:r,themeId:o}=e,n=ws(vl),s=ua()||vl,i=xl(o,n,r),a=xl(o,s,r,!0),l=(o?i[o]:i).direction==="rtl",c=sm(i);return w.jsx(Zf,{theme:a,children:w.jsx(Bo.Provider,{value:i,children:w.jsx(Jf,{value:l,children:w.jsxs(em,{value:o?i[o].components:i.components,children:[c,t]})})})})}const Sl={theme:void 0};function im(e){let t,r;return function(n){let s=t;return(s===void 0||n.theme!==r)&&(Sl.theme=n.theme,s=du(e(Sl)),t=s,r=n.theme),s}}const da="mode",pa="color-scheme",am="data-color-scheme";function lm(e){const{defaultMode:t="system",defaultLightColorScheme:r="light",defaultDarkColorScheme:o="dark",modeStorageKey:n=da,colorSchemeStorageKey:s=pa,attribute:i=am,colorSchemeNode:a="document.documentElement",nonce:l}=e||{};let c="",u=i;if(i==="class"&&(u=".%s"),i==="data"&&(u="[data-%s]"),u.startsWith(".")){const f=u.substring(1);c+=`${a}.classList.remove('${f}'.replace('%s', light), '${f}'.replace('%s', dark));
      ${a}.classList.add('${f}'.replace('%s', colorScheme));`}const d=u.match(/\[([^[\]]+)\]/);if(d){const[f,m]=d[1].split("=");m||(c+=`${a}.removeAttribute('${f}'.replace('%s', light));
      ${a}.removeAttribute('${f}'.replace('%s', dark));`),c+=`
      ${a}.setAttribute('${f}'.replace('%s', colorScheme), ${m?`${m}.replace('%s', colorScheme)`:'""'});`}else c+=`${a}.setAttribute('${u}', colorScheme);`;return w.jsx("script",{suppressHydrationWarning:!0,nonce:typeof window>"u"?l:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${n}') || '${t}';
  const dark = localStorage.getItem('${s}-dark') || '${o}';
  const light = localStorage.getItem('${s}-light') || '${r}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${c}
  }
} catch(e){}})();`}},"mui-color-scheme-init")}function cm(){}const um=({key:e,storageWindow:t})=>(!t&&typeof window<"u"&&(t=window),{get(r){if(typeof window>"u")return;if(!t)return r;let o;try{o=t.localStorage.getItem(e)}catch{}return o||r},set:r=>{if(t)try{t.localStorage.setItem(e,r)}catch{}},subscribe:r=>{if(!t)return cm;const o=n=>{const s=n.newValue;n.key===e&&r(s)};return t.addEventListener("storage",o),()=>{t.removeEventListener("storage",o)}}});function ii(){}function Cl(e){if(typeof window<"u"&&typeof window.matchMedia=="function"&&e==="system")return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function Cu(e,t){if(e.mode==="light"||e.mode==="system"&&e.systemMode==="light")return t("light");if(e.mode==="dark"||e.mode==="system"&&e.systemMode==="dark")return t("dark")}function dm(e){return Cu(e,t=>{if(t==="light")return e.lightColorScheme;if(t==="dark")return e.darkColorScheme})}function pm(e){const{defaultMode:t="light",defaultLightColorScheme:r,defaultDarkColorScheme:o,supportedColorSchemes:n=[],modeStorageKey:s=da,colorSchemeStorageKey:i=pa,storageWindow:a=typeof window>"u"?void 0:window,storageManager:l=um,noSsr:c=!1}=e,u=n.join(","),d=n.length>1,f=p.useMemo(()=>l==null?void 0:l({key:s,storageWindow:a}),[l,s,a]),m=p.useMemo(()=>l==null?void 0:l({key:`${i}-light`,storageWindow:a}),[l,i,a]),h=p.useMemo(()=>l==null?void 0:l({key:`${i}-dark`,storageWindow:a}),[l,i,a]),[b,y]=p.useState(()=>{const I=(f==null?void 0:f.get(t))||t,A=(m==null?void 0:m.get(r))||r,M=(h==null?void 0:h.get(o))||o;return{mode:I,systemMode:Cl(I),lightColorScheme:A,darkColorScheme:M}}),[S,$]=p.useState(c||!d);p.useEffect(()=>{$(!0)},[]);const C=dm(b),v=p.useCallback(I=>{y(A=>{if(I===A.mode)return A;const M=I??t;return f==null||f.set(M),{...A,mode:M,systemMode:Cl(M)}})},[f,t]),x=p.useCallback(I=>{I?typeof I=="string"?I&&!u.includes(I)?console.error(`\`${I}\` does not exist in \`theme.colorSchemes\`.`):y(A=>{const M={...A};return Cu(A,L=>{L==="light"&&(m==null||m.set(I),M.lightColorScheme=I),L==="dark"&&(h==null||h.set(I),M.darkColorScheme=I)}),M}):y(A=>{const M={...A},L=I.light===null?r:I.light,g=I.dark===null?o:I.dark;return L&&(u.includes(L)?(M.lightColorScheme=L,m==null||m.set(L)):console.error(`\`${L}\` does not exist in \`theme.colorSchemes\`.`)),g&&(u.includes(g)?(M.darkColorScheme=g,h==null||h.set(g)):console.error(`\`${g}\` does not exist in \`theme.colorSchemes\`.`)),M}):y(A=>(m==null||m.set(r),h==null||h.set(o),{...A,lightColorScheme:r,darkColorScheme:o}))},[u,m,h,r,o]),k=p.useCallback(I=>{b.mode==="system"&&y(A=>{const M=I!=null&&I.matches?"dark":"light";return A.systemMode===M?A:{...A,systemMode:M}})},[b.mode]),T=p.useRef(k);return T.current=k,p.useEffect(()=>{if(typeof window.matchMedia!="function"||!d)return;const I=(...M)=>T.current(...M),A=window.matchMedia("(prefers-color-scheme: dark)");return A.addListener(I),I(A),()=>{A.removeListener(I)}},[d]),p.useEffect(()=>{if(d){const I=(f==null?void 0:f.subscribe(L=>{(!L||["light","dark","system"].includes(L))&&v(L||t)}))||ii,A=(m==null?void 0:m.subscribe(L=>{(!L||u.match(L))&&x({light:L})}))||ii,M=(h==null?void 0:h.subscribe(L=>{(!L||u.match(L))&&x({dark:L})}))||ii;return()=>{I(),A(),M()}}},[x,v,u,t,a,d,f,m,h]),{...b,mode:S?b.mode:void 0,systemMode:S?b.systemMode:void 0,colorScheme:S?C:void 0,setMode:v,setColorScheme:x}}const fm="*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function mm(e){const{themeId:t,theme:r={},modeStorageKey:o=da,colorSchemeStorageKey:n=pa,disableTransitionOnChange:s=!1,defaultColorScheme:i,resolveTheme:a}=e,l={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},c=p.createContext(void 0),u=()=>p.useContext(c)||l,d={},f={};function m(S){var Y,ae,xe,Te;const{children:$,theme:C,modeStorageKey:v=o,colorSchemeStorageKey:x=n,disableTransitionOnChange:k=s,storageManager:T,storageWindow:I=typeof window>"u"?void 0:window,documentNode:A=typeof document>"u"?void 0:document,colorSchemeNode:M=typeof document>"u"?void 0:document.documentElement,disableNestedContext:L=!1,disableStyleSheetGeneration:g=!1,defaultMode:P="system",forceThemeRerender:R=!1,noSsr:O}=S,E=p.useRef(!1),F=ua(),z=p.useContext(c),N=!!z&&!L,Q=p.useMemo(()=>C||(typeof r=="function"?r():r),[C]),_=Q[t],Z=_||Q,{colorSchemes:se=d,components:ye=f,cssVarPrefix:K}=Z,q=Object.keys(se).filter(X=>!!se[X]).join(","),ie=p.useMemo(()=>q.split(","),[q]),we=typeof i=="string"?i:i.light,he=typeof i=="string"?i:i.dark,ee=se[we]&&se[he]?P:((ae=(Y=se[Z.defaultColorScheme])==null?void 0:Y.palette)==null?void 0:ae.mode)||((xe=Z.palette)==null?void 0:xe.mode),{mode:Ce,setMode:oe,systemMode:ge,lightColorScheme:ve,darkColorScheme:Le,colorScheme:Me,setColorScheme:$e}=pm({supportedColorSchemes:ie,defaultLightColorScheme:we,defaultDarkColorScheme:he,modeStorageKey:v,colorSchemeStorageKey:x,defaultMode:ee,storageManager:T,storageWindow:I,noSsr:O});let J=Ce,Ae=Me;N&&(J=z.mode,Ae=z.colorScheme);let pe=Ae||Z.defaultColorScheme;Z.vars&&!R&&(pe=Z.defaultColorScheme);const Re=p.useMemo(()=>{var Pe;const X=((Pe=Z.generateThemeVars)==null?void 0:Pe.call(Z))||Z.vars,G={...Z,components:ye,colorSchemes:se,cssVarPrefix:K,vars:X};if(typeof G.generateSpacing=="function"&&(G.spacing=G.generateSpacing()),pe){const Ie=se[pe];Ie&&typeof Ie=="object"&&Object.keys(Ie).forEach(ke=>{Ie[ke]&&typeof Ie[ke]=="object"?G[ke]={...G[ke],...Ie[ke]}:G[ke]=Ie[ke]})}return a?a(G):G},[Z,pe,ye,se,K]),te=Z.colorSchemeSelector;It(()=>{if(Ae&&M&&te&&te!=="media"){const X=te;let G=te;if(X==="class"&&(G=".%s"),X==="data"&&(G="[data-%s]"),X!=null&&X.startsWith("data-")&&!X.includes("%s")&&(G=`[${X}="%s"]`),G.startsWith("."))M.classList.remove(...ie.map(Pe=>G.substring(1).replace("%s",Pe))),M.classList.add(G.substring(1).replace("%s",Ae));else{const Pe=G.replace("%s",Ae).match(/\[([^\]]+)\]/);if(Pe){const[Ie,ke]=Pe[1].split("=");ke||ie.forEach(dt=>{M.removeAttribute(Ie.replace(Ae,dt))}),M.setAttribute(Ie,ke?ke.replace(/"|'/g,""):"")}else M.setAttribute(G,Ae)}}},[Ae,te,M,ie]),p.useEffect(()=>{let X;if(k&&E.current&&A){const G=A.createElement("style");G.appendChild(A.createTextNode(fm)),A.head.appendChild(G),window.getComputedStyle(A.body),X=setTimeout(()=>{A.head.removeChild(G)},1)}return()=>{clearTimeout(X)}},[Ae,k,A]),p.useEffect(()=>(E.current=!0,()=>{E.current=!1}),[]);const Ge=p.useMemo(()=>({allColorSchemes:ie,colorScheme:Ae,darkColorScheme:Le,lightColorScheme:ve,mode:J,setColorScheme:$e,setMode:oe,systemMode:ge}),[ie,Ae,Le,ve,J,$e,oe,ge,Re.colorSchemeSelector]);let je=!0;(g||Z.cssVariables===!1||N&&(F==null?void 0:F.cssVarPrefix)===K)&&(je=!1);const H=w.jsxs(p.Fragment,{children:[w.jsx(Su,{themeId:_?t:void 0,theme:Re,children:$}),je&&w.jsx(ru,{styles:((Te=Re.generateStyleSheets)==null?void 0:Te.call(Re))||[]})]});return N?H:w.jsx(c.Provider,{value:Ge,children:H})}const h=typeof i=="string"?i:i.light,b=typeof i=="string"?i:i.dark;return{CssVarsProvider:m,useColorScheme:u,getInitColorSchemeScript:S=>lm({colorSchemeStorageKey:n,defaultLightColorScheme:h,defaultDarkColorScheme:b,modeStorageKey:o,...S})}}function gm(e=""){function t(...o){if(!o.length)return"";const n=o[0];return typeof n=="string"&&!n.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${e?`${e}-`:""}${n}${t(...o.slice(1))})`:`, ${n}`}return(o,...n)=>`var(--${e?`${e}-`:""}${o}${t(...n)})`}const wl=(e,t,r,o=[])=>{let n=e;t.forEach((s,i)=>{i===t.length-1?Array.isArray(n)?n[Number(s)]=r:n&&typeof n=="object"&&(n[s]=r):n&&typeof n=="object"&&(n[s]||(n[s]=o.includes(s)?[]:{}),n=n[s])})},hm=(e,t,r)=>{function o(n,s=[],i=[]){Object.entries(n).forEach(([a,l])=>{(!r||r&&!r([...s,a]))&&l!=null&&(typeof l=="object"&&Object.keys(l).length>0?o(l,[...s,a],Array.isArray(l)?[...i,a]:i):t([...s,a],l,i))})}o(e)},bm=(e,t)=>typeof t=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(o=>e.includes(o))||e[e.length-1].toLowerCase().includes("opacity")?t:`${t}px`:t;function ai(e,t){const{prefix:r,shouldSkipGeneratingVar:o}=t||{},n={},s={},i={};return hm(e,(a,l,c)=>{if((typeof l=="string"||typeof l=="number")&&(!o||!o(a,l))){const u=`--${r?`${r}-`:""}${a.join("-")}`,d=bm(a,l);Object.assign(n,{[u]:d}),wl(s,a,`var(${u})`,c),wl(i,a,`var(${u}, ${d})`,c)}},a=>a[0]==="vars"),{css:n,vars:s,varsWithDefaults:i}}function ym(e,t={}){const{getSelector:r=S,disableCssColorScheme:o,colorSchemeSelector:n,enableContrastVars:s}=t,{colorSchemes:i={},components:a,defaultColorScheme:l="light",...c}=e,{vars:u,css:d,varsWithDefaults:f}=ai(c,t);let m=f;const h={},{[l]:b,...y}=i;if(Object.entries(y||{}).forEach(([v,x])=>{const{vars:k,css:T,varsWithDefaults:I}=ai(x,t);m=$t(m,I),h[v]={css:T,vars:k}}),b){const{css:v,vars:x,varsWithDefaults:k}=ai(b,t);m=$t(m,k),h[l]={css:v,vars:x}}function S(v,x){var T,I;let k=n;if(n==="class"&&(k=".%s"),n==="data"&&(k="[data-%s]"),n!=null&&n.startsWith("data-")&&!n.includes("%s")&&(k=`[${n}="%s"]`),v){if(k==="media")return e.defaultColorScheme===v?":root":{[`@media (prefers-color-scheme: ${((I=(T=i[v])==null?void 0:T.palette)==null?void 0:I.mode)||v})`]:{":root":x}};if(k)return e.defaultColorScheme===v?`:root, ${k.replace("%s",String(v))}`:k.replace("%s",String(v))}return":root"}return{vars:m,generateThemeVars:()=>{let v={...u};return Object.entries(h).forEach(([,{vars:x}])=>{v=$t(v,x)}),v},generateStyleSheets:()=>{var A,M;const v=[],x=e.defaultColorScheme||"light";function k(L,g){Object.keys(g).length&&v.push(typeof L=="string"?{[L]:{...g}}:L)}k(r(void 0,{...d}),d);const{[x]:T,...I}=h;if(T){const{css:L}=T,g=(M=(A=i[x])==null?void 0:A.palette)==null?void 0:M.mode,P=!o&&g?{colorScheme:g,...L}:{...L};k(r(x,{...P}),P)}return Object.entries(I).forEach(([L,{css:g}])=>{var O,E;const P=(E=(O=i[L])==null?void 0:O.palette)==null?void 0:E.mode,R=!o&&P?{colorScheme:P,...g}:{...g};k(r(L,{...R}),R)}),s&&v.push({":root":{"--__l-threshold":"0.7","--__l":"clamp(0, (l / var(--__l-threshold) - 1) * -infinity, 1)","--__a":"clamp(0.87, (l / var(--__l-threshold) - 1) * -infinity, 1)"}}),v}}}function vm(e){return function(r){return e==="media"?`@media (prefers-color-scheme: ${r})`:e?e.startsWith("data-")&&!e.includes("%s")?`[${e}="${r}"] &`:e==="class"?`.${r} &`:e==="data"?`[data-${r}] &`:`${e.replace("%s",r)} &`:"&"}}function de(e,t,r=void 0){const o={};for(const n in e){const s=e[n];let i="",a=!0;for(let l=0;l<s.length;l+=1){const c=s[l];c&&(i+=(a===!0?"":" ")+t(c),a=!1,r&&r[c]&&(i+=" "+r[c]))}o[n]=i}return o}const xm=No(),Sm=la("div",{name:"MuiContainer",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[`maxWidth${B(String(r.maxWidth))}`],r.fixed&&t.fixed,r.disableGutters&&t.disableGutters]}}),Cm=e=>ks({props:e,name:"MuiContainer",defaultTheme:xm}),wm=(e,t)=>{const r=l=>ce(t,l),{classes:o,fixed:n,disableGutters:s,maxWidth:i}=e,a={root:["root",i&&`maxWidth${B(String(i))}`,n&&"fixed",s&&"disableGutters"]};return de(a,r,o)};function $m(e={}){const{createStyledComponent:t=Sm,useThemeProps:r=Cm,componentName:o="MuiContainer"}=e,n=t(({theme:i,ownerState:a})=>({width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",...!a.disableGutters&&{paddingLeft:i.spacing(2),paddingRight:i.spacing(2),[i.breakpoints.up("sm")]:{paddingLeft:i.spacing(3),paddingRight:i.spacing(3)}}}),({theme:i,ownerState:a})=>a.fixed&&Object.keys(i.breakpoints.values).reduce((l,c)=>{const u=c,d=i.breakpoints.values[u];return d!==0&&(l[i.breakpoints.up(u)]={maxWidth:`${d}${i.breakpoints.unit}`}),l},{}),({theme:i,ownerState:a})=>({...a.maxWidth==="xs"&&{[i.breakpoints.up("xs")]:{maxWidth:Math.max(i.breakpoints.values.xs,444)}},...a.maxWidth&&a.maxWidth!=="xs"&&{[i.breakpoints.up(a.maxWidth)]:{maxWidth:`${i.breakpoints.values[a.maxWidth]}${i.breakpoints.unit}`}}}));return p.forwardRef(function(a,l){const c=r(a),{className:u,component:d="div",disableGutters:f=!1,fixed:m=!1,maxWidth:h="lg",classes:b,...y}=c,S={...c,component:d,disableGutters:f,fixed:m,maxWidth:h},$=wm(S,o);return w.jsx(n,{as:d,ownerState:S,className:V($.root,u),ref:l,...y})})}function Jo(e,t){var r,o,n;return p.isValidElement(e)&&t.indexOf(e.type.muiName??((n=(o=(r=e.type)==null?void 0:r._payload)==null?void 0:o.value)==null?void 0:n.muiName))!==-1}const km=(e,t)=>e.filter(r=>t.includes(r)),jo=(e,t,r)=>{const o=e.keys[0];Array.isArray(t)?t.forEach((n,s)=>{r((i,a)=>{s<=e.keys.length-1&&(s===0?Object.assign(i,a):i[e.up(e.keys[s])]=a)},n)}):t&&typeof t=="object"?(Object.keys(t).length>e.keys.length?e.keys:km(e.keys,Object.keys(t))).forEach(s=>{if(e.keys.includes(s)){const i=t[s];i!==void 0&&r((a,l)=>{o===s?Object.assign(a,l):a[e.up(s)]=l},i)}}):(typeof t=="number"||typeof t=="string")&&r((n,s)=>{Object.assign(n,s)},t)};function ts(e){return`--Grid-${e}Spacing`}function Is(e){return`--Grid-parent-${e}Spacing`}const $l="--Grid-columns",$o="--Grid-parent-columns",Rm=({theme:e,ownerState:t})=>{const r={};return jo(e.breakpoints,t.size,(o,n)=>{let s={};n==="grow"&&(s={flexBasis:0,flexGrow:1,maxWidth:"100%"}),n==="auto"&&(s={flexBasis:"auto",flexGrow:0,flexShrink:0,maxWidth:"none",width:"auto"}),typeof n=="number"&&(s={flexGrow:0,flexBasis:"auto",width:`calc(100% * ${n} / var(${$o}) - (var(${$o}) - ${n}) * (var(${Is("column")}) / var(${$o})))`}),o(r,s)}),r},Pm=({theme:e,ownerState:t})=>{const r={};return jo(e.breakpoints,t.offset,(o,n)=>{let s={};n==="auto"&&(s={marginLeft:"auto"}),typeof n=="number"&&(s={marginLeft:n===0?"0px":`calc(100% * ${n} / var(${$o}) + var(${Is("column")}) * ${n} / var(${$o}))`}),o(r,s)}),r},Tm=({theme:e,ownerState:t})=>{if(!t.container)return{};const r={[$l]:12};return jo(e.breakpoints,t.columns,(o,n)=>{const s=n??12;o(r,{[$l]:s,"> *":{[$o]:s}})}),r},Im=({theme:e,ownerState:t})=>{if(!t.container)return{};const r={};return jo(e.breakpoints,t.rowSpacing,(o,n)=>{var i;const s=typeof n=="string"?n:(i=e.spacing)==null?void 0:i.call(e,n);o(r,{[ts("row")]:s,"> *":{[Is("row")]:s}})}),r},Em=({theme:e,ownerState:t})=>{if(!t.container)return{};const r={};return jo(e.breakpoints,t.columnSpacing,(o,n)=>{var i;const s=typeof n=="string"?n:(i=e.spacing)==null?void 0:i.call(e,n);o(r,{[ts("column")]:s,"> *":{[Is("column")]:s}})}),r},Mm=({theme:e,ownerState:t})=>{if(!t.container)return{};const r={};return jo(e.breakpoints,t.direction,(o,n)=>{o(r,{flexDirection:n})}),r},Am=({ownerState:e})=>({minWidth:0,boxSizing:"border-box",...e.container&&{display:"flex",flexWrap:"wrap",...e.wrap&&e.wrap!=="wrap"&&{flexWrap:e.wrap},gap:`var(${ts("row")}) var(${ts("column")})`}}),Om=e=>{const t=[];return Object.entries(e).forEach(([r,o])=>{o!==!1&&o!==void 0&&t.push(`grid-${r}-${String(o)}`)}),t},Lm=(e,t="xs")=>{function r(o){return o===void 0?!1:typeof o=="string"&&!Number.isNaN(Number(o))||typeof o=="number"&&o>0}if(r(e))return[`spacing-${t}-${String(e)}`];if(typeof e=="object"&&!Array.isArray(e)){const o=[];return Object.entries(e).forEach(([n,s])=>{r(s)&&o.push(`spacing-${n}-${String(s)}`)}),o}return[]},Bm=e=>e===void 0?[]:typeof e=="object"?Object.entries(e).map(([t,r])=>`direction-${t}-${r}`):[`direction-xs-${String(e)}`];function Nm(e,t){e.item!==void 0&&delete e.item,e.zeroMinWidth!==void 0&&delete e.zeroMinWidth,t.keys.forEach(r=>{e[r]!==void 0&&delete e[r]})}const jm=No(),zm=la("div",{name:"MuiGrid",slot:"Root"});function Fm(e){return ks({props:e,name:"MuiGrid",defaultTheme:jm})}function Dm(e={}){const{createStyledComponent:t=zm,useThemeProps:r=Fm,useTheme:o=Sn,componentName:n="MuiGrid"}=e,s=(c,u)=>{const{container:d,direction:f,spacing:m,wrap:h,size:b}=c,y={root:["root",d&&"container",h!=="wrap"&&`wrap-xs-${String(h)}`,...Bm(f),...Om(b),...d?Lm(m,u.breakpoints.keys[0]):[]]};return de(y,S=>ce(n,S),{})};function i(c,u,d=()=>!0){const f={};return c===null||(Array.isArray(c)?c.forEach((m,h)=>{m!==null&&d(m)&&u.keys[h]&&(f[u.keys[h]]=m)}):typeof c=="object"?Object.keys(c).forEach(m=>{const h=c[m];h!=null&&d(h)&&(f[m]=h)}):f[u.keys[0]]=c),f}const a=t(Tm,Em,Im,Rm,Mm,Am,Pm),l=p.forwardRef(function(u,d){const f=o(),m=r(u),h=$s(m);Nm(h,f.breakpoints);const{className:b,children:y,columns:S=12,container:$=!1,component:C="div",direction:v="row",wrap:x="wrap",size:k={},offset:T={},spacing:I=0,rowSpacing:A=I,columnSpacing:M=I,unstable_level:L=0,...g}=h,P=i(k,f.breakpoints,_=>_!==!1),R=i(T,f.breakpoints),O=u.columns??(L?void 0:S),E=u.spacing??(L?void 0:I),F=u.rowSpacing??u.spacing??(L?void 0:A),z=u.columnSpacing??u.spacing??(L?void 0:M),N={...h,level:L,columns:O,container:$,direction:v,wrap:x,spacing:E,rowSpacing:F,columnSpacing:z,size:P,offset:R},Q=s(N,f);return w.jsx(a,{ref:d,as:C,ownerState:N,className:V(Q.root,b),...g,children:p.Children.map(y,_=>{var Z;return p.isValidElement(_)&&Jo(_,["Grid"])&&$&&_.props.container?p.cloneElement(_,{unstable_level:((Z=_.props)==null?void 0:Z.unstable_level)??L+1}):_})})});return l.muiName="Grid",l}const Wm=No(),Um=la("div",{name:"MuiStack",slot:"Root"});function Hm(e){return ks({props:e,name:"MuiStack",defaultTheme:Wm})}function Vm(e,t){const r=p.Children.toArray(e).filter(Boolean);return r.reduce((o,n,s)=>(o.push(n),s<r.length-1&&o.push(p.cloneElement(t,{key:`separator-${s}`})),o),[])}const _m=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],Gm=({ownerState:e,theme:t})=>{let r={display:"flex",flexDirection:"column",...lr({theme:t},oi({values:e.direction,breakpoints:t.breakpoints.values}),o=>({flexDirection:o}))};if(e.spacing){const o=bs(t),n=Object.keys(t.breakpoints.values).reduce((l,c)=>((typeof e.spacing=="object"&&e.spacing[c]!=null||typeof e.direction=="object"&&e.direction[c]!=null)&&(l[c]=!0),l),{}),s=oi({values:e.direction,base:n}),i=oi({values:e.spacing,base:n});typeof s=="object"&&Object.keys(s).forEach((l,c,u)=>{if(!s[l]){const f=c>0?s[u[c-1]]:"column";s[l]=f}}),r=$t(r,lr({theme:t},i,(l,c)=>e.useFlexGap?{gap:ro(o,l)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${_m(c?s[c]:e.direction)}`]:ro(o,l)}}))}return r=Dp(t.breakpoints,r),r};function Km(e={}){const{createStyledComponent:t=Um,useThemeProps:r=Hm,componentName:o="MuiStack"}=e,n=()=>de({root:["root"]},l=>ce(o,l),{}),s=t(Gm);return p.forwardRef(function(l,c){const u=r(l),d=$s(u),{component:f="div",direction:m="column",spacing:h=0,divider:b,children:y,className:S,useFlexGap:$=!1,...C}=d,v={direction:m,spacing:h,useFlexGap:$},x=n();return w.jsx(s,{as:f,ownerState:v,ref:c,className:V(x.root,S),...C,children:b?Vm(y,b):y})})}const Zt="$$material",mn={black:"#000",white:"#fff"},qm={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},lo={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},co={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},Uo={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},uo={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},po={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},fo={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"};function wu(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:mn.white,default:mn.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const $u=wu();function ku(){return{text:{primary:mn.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:mn.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const Ri=ku();function kl(e,t,r,o){const n=o.light||o,s=o.dark||o*1.5;e[t]||(e.hasOwnProperty(r)?e[t]=e[r]:t==="light"?e.light=Ts(e.main,n):t==="dark"&&(e.dark=Ps(e.main,s)))}function Rl(e,t,r,o,n){const s=n.light||n,i=n.dark||n*1.5;t[r]||(t.hasOwnProperty(o)?t[r]=t[o]:r==="light"?t.light=`color-mix(in ${e}, ${t.main}, #fff ${(s*100).toFixed(0)}%)`:r==="dark"&&(t.dark=`color-mix(in ${e}, ${t.main}, #000 ${(i*100).toFixed(0)}%)`))}function Ym(e="light"){return e==="dark"?{main:uo[200],light:uo[50],dark:uo[400]}:{main:uo[700],light:uo[400],dark:uo[800]}}function Xm(e="light"){return e==="dark"?{main:lo[200],light:lo[50],dark:lo[400]}:{main:lo[500],light:lo[300],dark:lo[700]}}function Qm(e="light"){return e==="dark"?{main:co[500],light:co[300],dark:co[700]}:{main:co[700],light:co[400],dark:co[800]}}function Zm(e="light"){return e==="dark"?{main:po[400],light:po[300],dark:po[700]}:{main:po[700],light:po[500],dark:po[900]}}function Jm(e="light"){return e==="dark"?{main:fo[400],light:fo[300],dark:fo[700]}:{main:fo[800],light:fo[500],dark:fo[900]}}function eg(e="light"){return e==="dark"?{main:Uo[400],light:Uo[300],dark:Uo[700]}:{main:"#ed6c02",light:Uo[500],dark:Uo[900]}}function tg(e){return`oklch(from ${e} var(--__l) 0 h / var(--__a))`}function fa(e){const{mode:t="light",contrastThreshold:r=3,tonalOffset:o=.2,colorSpace:n,...s}=e,i=e.primary||Ym(t),a=e.secondary||Xm(t),l=e.error||Qm(t),c=e.info||Zm(t),u=e.success||Jm(t),d=e.warning||eg(t);function f(y){return n?tg(y):Kf(y,Ri.text.primary)>=r?Ri.text.primary:$u.text.primary}const m=({color:y,name:S,mainShade:$=500,lightShade:C=300,darkShade:v=700})=>{if(y={...y},!y.main&&y[$]&&(y.main=y[$]),!y.hasOwnProperty("main"))throw new Error(Ir(11,S?` (${S})`:"",$));if(typeof y.main!="string")throw new Error(Ir(12,S?` (${S})`:"",JSON.stringify(y.main)));return n?(Rl(n,y,"light",C,o),Rl(n,y,"dark",v,o)):(kl(y,"light",C,o),kl(y,"dark",v,o)),y.contrastText||(y.contrastText=f(y.main)),y};let h;return t==="light"?h=wu():t==="dark"&&(h=ku()),$t({common:{...mn},mode:t,primary:m({color:i,name:"primary"}),secondary:m({color:a,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:m({color:l,name:"error"}),warning:m({color:d,name:"warning"}),info:m({color:c,name:"info"}),success:m({color:u,name:"success"}),grey:qm,contrastThreshold:r,getContrastText:f,augmentColor:m,tonalOffset:o,...h},s)}function rg(e){const t={};return Object.entries(e).forEach(o=>{const[n,s]=o;typeof s=="object"&&(t[n]=`${s.fontStyle?`${s.fontStyle} `:""}${s.fontVariant?`${s.fontVariant} `:""}${s.fontWeight?`${s.fontWeight} `:""}${s.fontStretch?`${s.fontStretch} `:""}${s.fontSize||""}${s.lineHeight?`/${s.lineHeight} `:""}${s.fontFamily||""}`)}),t}function og(e,t){return{toolbar:{minHeight:56,[e.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[e.up("sm")]:{minHeight:64}},...t}}function ng(e){return Math.round(e*1e5)/1e5}const Pl={textTransform:"uppercase"},Tl='"Roboto", "Helvetica", "Arial", sans-serif';function Ru(e,t){const{fontFamily:r=Tl,fontSize:o=14,fontWeightLight:n=300,fontWeightRegular:s=400,fontWeightMedium:i=500,fontWeightBold:a=700,htmlFontSize:l=16,allVariants:c,pxToRem:u,...d}=typeof t=="function"?t(e):t,f=o/14,m=u||(y=>`${y/l*f}rem`),h=(y,S,$,C,v)=>({fontFamily:r,fontWeight:y,fontSize:m(S),lineHeight:$,...r===Tl?{letterSpacing:`${ng(C/S)}em`}:{},...v,...c}),b={h1:h(n,96,1.167,-1.5),h2:h(n,60,1.2,-.5),h3:h(s,48,1.167,0),h4:h(s,34,1.235,.25),h5:h(s,24,1.334,0),h6:h(i,20,1.6,.15),subtitle1:h(s,16,1.75,.15),subtitle2:h(i,14,1.57,.1),body1:h(s,16,1.5,.15),body2:h(s,14,1.43,.15),button:h(i,14,1.75,.4,Pl),caption:h(s,12,1.66,.4),overline:h(s,12,2.66,1,Pl),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return $t({htmlFontSize:l,pxToRem:m,fontFamily:r,fontSize:o,fontWeightLight:n,fontWeightRegular:s,fontWeightMedium:i,fontWeightBold:a,...b},d,{clone:!1})}const sg=.2,ig=.14,ag=.12;function rt(...e){return[`${e[0]}px ${e[1]}px ${e[2]}px ${e[3]}px rgba(0,0,0,${sg})`,`${e[4]}px ${e[5]}px ${e[6]}px ${e[7]}px rgba(0,0,0,${ig})`,`${e[8]}px ${e[9]}px ${e[10]}px ${e[11]}px rgba(0,0,0,${ag})`].join(",")}const lg=["none",rt(0,2,1,-1,0,1,1,0,0,1,3,0),rt(0,3,1,-2,0,2,2,0,0,1,5,0),rt(0,3,3,-2,0,3,4,0,0,1,8,0),rt(0,2,4,-1,0,4,5,0,0,1,10,0),rt(0,3,5,-1,0,5,8,0,0,1,14,0),rt(0,3,5,-1,0,6,10,0,0,1,18,0),rt(0,4,5,-2,0,7,10,1,0,2,16,1),rt(0,5,5,-3,0,8,10,1,0,3,14,2),rt(0,5,6,-3,0,9,12,1,0,3,16,2),rt(0,6,6,-3,0,10,14,1,0,4,18,3),rt(0,6,7,-4,0,11,15,1,0,4,20,3),rt(0,7,8,-4,0,12,17,2,0,5,22,4),rt(0,7,8,-4,0,13,19,2,0,5,24,4),rt(0,7,9,-4,0,14,21,2,0,5,26,4),rt(0,8,9,-5,0,15,22,2,0,6,28,5),rt(0,8,10,-5,0,16,24,2,0,6,30,5),rt(0,8,11,-5,0,17,26,2,0,6,32,5),rt(0,9,11,-5,0,18,28,2,0,7,34,6),rt(0,9,12,-6,0,19,29,2,0,7,36,6),rt(0,10,13,-6,0,20,31,3,0,8,38,7),rt(0,10,13,-6,0,21,33,3,0,8,40,7),rt(0,10,14,-6,0,22,35,3,0,8,42,7),rt(0,11,14,-7,0,23,36,3,0,9,44,8),rt(0,11,15,-7,0,24,38,3,0,9,46,8)],cg={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},ug={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Il(e){return`${Math.round(e)}ms`}function dg(e){if(!e)return 0;const t=e/36;return Math.min(Math.round((4+15*t**.25+t/5)*10),3e3)}function pg(e){const t={...cg,...e.easing},r={...ug,...e.duration};return{getAutoHeightDuration:dg,create:(n=["all"],s={})=>{const{duration:i=r.standard,easing:a=t.easeInOut,delay:l=0,...c}=s;return(Array.isArray(n)?n:[n]).map(u=>`${u} ${typeof i=="string"?i:Il(i)} ${a} ${typeof l=="string"?l:Il(l)}`).join(",")},...e,easing:t,duration:r}}const fg={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function mg(e){return gr(e)||typeof e>"u"||typeof e=="string"||typeof e=="boolean"||typeof e=="number"||Array.isArray(e)}function Pu(e={}){const t={...e};function r(o){const n=Object.entries(o);for(let s=0;s<n.length;s++){const[i,a]=n[s];!mg(a)||i.startsWith("unstable_")?delete o[i]:gr(a)&&(o[i]={...a},r(o[i]))}}return r(t),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(t,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function El(e){return typeof e=="number"?`${(e*100).toFixed(0)}%`:`calc((${e}) * 100%)`}const gg=e=>{if(!Number.isNaN(+e))return+e;const t=e.match(/\d*\.?\d+/g);if(!t)return 0;let r=0;for(let o=0;o<t.length;o+=1)r+=+t[o];return r};function hg(e){Object.assign(e,{alpha(t,r){const o=this||e;return o.colorSpace?`oklch(from ${t} l c h / ${typeof r=="string"?`calc(${r})`:r})`:o.vars?`rgba(${t.replace(/var\(--([^,\s)]+)(?:,[^)]+)?\)+/g,"var(--$1Channel)")} / ${typeof r=="string"?`calc(${r})`:r})`:fn(t,gg(r))},lighten(t,r){const o=this||e;return o.colorSpace?`color-mix(in ${o.colorSpace}, ${t}, #fff ${El(r)})`:Ts(t,r)},darken(t,r){const o=this||e;return o.colorSpace?`color-mix(in ${o.colorSpace}, ${t}, #000 ${El(r)})`:Ps(t,r)}})}function Pi(e={},...t){const{breakpoints:r,mixins:o={},spacing:n,palette:s={},transitions:i={},typography:a={},shape:l,colorSpace:c,...u}=e;if(e.vars&&e.generateThemeVars===void 0)throw new Error(Ir(20));const d=fa({...s,colorSpace:c}),f=No(e);let m=$t(f,{mixins:og(f.breakpoints,o),palette:d,shadows:lg.slice(),typography:Ru(d,a),transitions:pg(i),zIndex:{...fg}});return m=$t(m,u),m=t.reduce((h,b)=>$t(h,b),m),m.unstable_sxConfig={...xn,...u==null?void 0:u.unstable_sxConfig},m.unstable_sx=function(b){return Ur({sx:b,theme:this})},m.toRuntimeSource=Pu,hg(m),m}function Ti(e){let t;return e<1?t=5.11916*e**2:t=4.5*Math.log(e+1)+2,Math.round(t*10)/1e3}const bg=[...Array(25)].map((e,t)=>{if(t===0)return"none";const r=Ti(t);return`linear-gradient(rgba(255 255 255 / ${r}), rgba(255 255 255 / ${r}))`});function Tu(e){return{inputPlaceholder:e==="dark"?.5:.42,inputUnderline:e==="dark"?.7:.42,switchTrackDisabled:e==="dark"?.2:.12,switchTrack:e==="dark"?.3:.38}}function Iu(e){return e==="dark"?bg:[]}function yg(e){const{palette:t={mode:"light"},opacity:r,overlays:o,colorSpace:n,...s}=e,i=fa({...t,colorSpace:n});return{palette:i,opacity:{...Tu(i.mode),...r},overlays:o||Iu(i.mode),...s}}function vg(e){var t;return!!e[0].match(/(cssVarPrefix|colorSchemeSelector|modularCssLayers|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!e[0].match(/sxConfig$/)||e[0]==="palette"&&!!((t=e[1])!=null&&t.match(/(mode|contrastThreshold|tonalOffset)/))}const xg=e=>[...[...Array(25)].map((t,r)=>`--${e?`${e}-`:""}overlays-${r}`),`--${e?`${e}-`:""}palette-AppBar-darkBg`,`--${e?`${e}-`:""}palette-AppBar-darkColor`],Sg=e=>(t,r)=>{const o=e.rootSelector||":root",n=e.colorSchemeSelector;let s=n;if(n==="class"&&(s=".%s"),n==="data"&&(s="[data-%s]"),n!=null&&n.startsWith("data-")&&!n.includes("%s")&&(s=`[${n}="%s"]`),e.defaultColorScheme===t){if(t==="dark"){const i={};return xg(e.cssVarPrefix).forEach(a=>{i[a]=r[a],delete r[a]}),s==="media"?{[o]:r,"@media (prefers-color-scheme: dark)":{[o]:i}}:s?{[s.replace("%s",t)]:i,[`${o}, ${s.replace("%s",t)}`]:r}:{[o]:{...r,...i}}}if(s&&s!=="media")return`${o}, ${s.replace("%s",String(t))}`}else if(t){if(s==="media")return{[`@media (prefers-color-scheme: ${String(t)})`]:{[o]:r}};if(s)return s.replace("%s",String(t))}return o};function Cg(e,t){t.forEach(r=>{e[r]||(e[r]={})})}function D(e,t,r){!e[t]&&r&&(e[t]=r)}function Yo(e){return typeof e!="string"||!e.startsWith("hsl")?e:bu(e)}function Pr(e,t){`${t}Channel`in e||(e[`${t}Channel`]=qo(Yo(e[t])))}function wg(e){return typeof e=="number"?`${e}px`:typeof e=="string"||typeof e=="function"||Array.isArray(e)?e:"8px"}const dr=e=>{try{return e()}catch{}},$g=(e="mui")=>gm(e);function li(e,t,r,o,n){if(!r)return;r=r===!0?{}:r;const s=n==="dark"?"dark":"light";if(!o){t[n]=yg({...r,palette:{mode:s,...r==null?void 0:r.palette},colorSpace:e});return}const{palette:i,...a}=Pi({...o,palette:{mode:s,...r==null?void 0:r.palette},colorSpace:e});return t[n]={...r,palette:i,opacity:{...Tu(s),...r==null?void 0:r.opacity},overlays:(r==null?void 0:r.overlays)||Iu(s)},a}function kg(e={},...t){const{colorSchemes:r={light:!0},defaultColorScheme:o,disableCssColorScheme:n=!1,cssVarPrefix:s="mui",nativeColor:i=!1,shouldSkipGeneratingVar:a=vg,colorSchemeSelector:l=r.light&&r.dark?"media":void 0,rootSelector:c=":root",...u}=e,d=Object.keys(r)[0],f=o||(r.light&&d!=="light"?"light":d),m=$g(s),{[f]:h,light:b,dark:y,...S}=r,$={...S};let C=h;if((f==="dark"&&!("dark"in r)||f==="light"&&!("light"in r))&&(C=!0),!C)throw new Error(Ir(21,f));let v;i&&(v="oklch");const x=li(v,$,C,u,f);b&&!$.light&&li(v,$,b,void 0,"light"),y&&!$.dark&&li(v,$,y,void 0,"dark");let k={defaultColorScheme:f,...x,cssVarPrefix:s,colorSchemeSelector:l,rootSelector:c,getCssVar:m,colorSchemes:$,font:{...rg(x.typography),...x.font},spacing:wg(u.spacing)};Object.keys(k.colorSchemes).forEach(L=>{const g=k.colorSchemes[L].palette,P=O=>{const E=O.split("-"),F=E[1],z=E[2];return m(O,g[F][z])};g.mode==="light"&&(D(g.common,"background","#fff"),D(g.common,"onBackground","#000")),g.mode==="dark"&&(D(g.common,"background","#000"),D(g.common,"onBackground","#fff"));function R(O,E,F){if(v){let z;return O===qr&&(z=`transparent ${((1-F)*100).toFixed(0)}%`),O===Xe&&(z=`#000 ${(F*100).toFixed(0)}%`),O===Qe&&(z=`#fff ${(F*100).toFixed(0)}%`),`color-mix(in ${v}, ${E}, ${z})`}return O(E,F)}if(Cg(g,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),g.mode==="light"){D(g.Alert,"errorColor",R(Xe,g.error.light,.6)),D(g.Alert,"infoColor",R(Xe,g.info.light,.6)),D(g.Alert,"successColor",R(Xe,g.success.light,.6)),D(g.Alert,"warningColor",R(Xe,g.warning.light,.6)),D(g.Alert,"errorFilledBg",P("palette-error-main")),D(g.Alert,"infoFilledBg",P("palette-info-main")),D(g.Alert,"successFilledBg",P("palette-success-main")),D(g.Alert,"warningFilledBg",P("palette-warning-main")),D(g.Alert,"errorFilledColor",dr(()=>g.getContrastText(g.error.main))),D(g.Alert,"infoFilledColor",dr(()=>g.getContrastText(g.info.main))),D(g.Alert,"successFilledColor",dr(()=>g.getContrastText(g.success.main))),D(g.Alert,"warningFilledColor",dr(()=>g.getContrastText(g.warning.main))),D(g.Alert,"errorStandardBg",R(Qe,g.error.light,.9)),D(g.Alert,"infoStandardBg",R(Qe,g.info.light,.9)),D(g.Alert,"successStandardBg",R(Qe,g.success.light,.9)),D(g.Alert,"warningStandardBg",R(Qe,g.warning.light,.9)),D(g.Alert,"errorIconColor",P("palette-error-main")),D(g.Alert,"infoIconColor",P("palette-info-main")),D(g.Alert,"successIconColor",P("palette-success-main")),D(g.Alert,"warningIconColor",P("palette-warning-main")),D(g.AppBar,"defaultBg",P("palette-grey-100")),D(g.Avatar,"defaultBg",P("palette-grey-400")),D(g.Button,"inheritContainedBg",P("palette-grey-300")),D(g.Button,"inheritContainedHoverBg",P("palette-grey-A100")),D(g.Chip,"defaultBorder",P("palette-grey-400")),D(g.Chip,"defaultAvatarColor",P("palette-grey-700")),D(g.Chip,"defaultIconColor",P("palette-grey-700")),D(g.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),D(g.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),D(g.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),D(g.LinearProgress,"primaryBg",R(Qe,g.primary.main,.62)),D(g.LinearProgress,"secondaryBg",R(Qe,g.secondary.main,.62)),D(g.LinearProgress,"errorBg",R(Qe,g.error.main,.62)),D(g.LinearProgress,"infoBg",R(Qe,g.info.main,.62)),D(g.LinearProgress,"successBg",R(Qe,g.success.main,.62)),D(g.LinearProgress,"warningBg",R(Qe,g.warning.main,.62)),D(g.Skeleton,"bg",v?R(qr,g.text.primary,.11):`rgba(${P("palette-text-primaryChannel")} / 0.11)`),D(g.Slider,"primaryTrack",R(Qe,g.primary.main,.62)),D(g.Slider,"secondaryTrack",R(Qe,g.secondary.main,.62)),D(g.Slider,"errorTrack",R(Qe,g.error.main,.62)),D(g.Slider,"infoTrack",R(Qe,g.info.main,.62)),D(g.Slider,"successTrack",R(Qe,g.success.main,.62)),D(g.Slider,"warningTrack",R(Qe,g.warning.main,.62));const O=v?R(Xe,g.background.default,.6825):An(g.background.default,.8);D(g.SnackbarContent,"bg",O),D(g.SnackbarContent,"color",dr(()=>v?Ri.text.primary:g.getContrastText(O))),D(g.SpeedDialAction,"fabHoverBg",An(g.background.paper,.15)),D(g.StepConnector,"border",P("palette-grey-400")),D(g.StepContent,"border",P("palette-grey-400")),D(g.Switch,"defaultColor",P("palette-common-white")),D(g.Switch,"defaultDisabledColor",P("palette-grey-100")),D(g.Switch,"primaryDisabledColor",R(Qe,g.primary.main,.62)),D(g.Switch,"secondaryDisabledColor",R(Qe,g.secondary.main,.62)),D(g.Switch,"errorDisabledColor",R(Qe,g.error.main,.62)),D(g.Switch,"infoDisabledColor",R(Qe,g.info.main,.62)),D(g.Switch,"successDisabledColor",R(Qe,g.success.main,.62)),D(g.Switch,"warningDisabledColor",R(Qe,g.warning.main,.62)),D(g.TableCell,"border",R(Qe,R(qr,g.divider,1),.88)),D(g.Tooltip,"bg",R(qr,g.grey[700],.92))}if(g.mode==="dark"){D(g.Alert,"errorColor",R(Qe,g.error.light,.6)),D(g.Alert,"infoColor",R(Qe,g.info.light,.6)),D(g.Alert,"successColor",R(Qe,g.success.light,.6)),D(g.Alert,"warningColor",R(Qe,g.warning.light,.6)),D(g.Alert,"errorFilledBg",P("palette-error-dark")),D(g.Alert,"infoFilledBg",P("palette-info-dark")),D(g.Alert,"successFilledBg",P("palette-success-dark")),D(g.Alert,"warningFilledBg",P("palette-warning-dark")),D(g.Alert,"errorFilledColor",dr(()=>g.getContrastText(g.error.dark))),D(g.Alert,"infoFilledColor",dr(()=>g.getContrastText(g.info.dark))),D(g.Alert,"successFilledColor",dr(()=>g.getContrastText(g.success.dark))),D(g.Alert,"warningFilledColor",dr(()=>g.getContrastText(g.warning.dark))),D(g.Alert,"errorStandardBg",R(Xe,g.error.light,.9)),D(g.Alert,"infoStandardBg",R(Xe,g.info.light,.9)),D(g.Alert,"successStandardBg",R(Xe,g.success.light,.9)),D(g.Alert,"warningStandardBg",R(Xe,g.warning.light,.9)),D(g.Alert,"errorIconColor",P("palette-error-main")),D(g.Alert,"infoIconColor",P("palette-info-main")),D(g.Alert,"successIconColor",P("palette-success-main")),D(g.Alert,"warningIconColor",P("palette-warning-main")),D(g.AppBar,"defaultBg",P("palette-grey-900")),D(g.AppBar,"darkBg",P("palette-background-paper")),D(g.AppBar,"darkColor",P("palette-text-primary")),D(g.Avatar,"defaultBg",P("palette-grey-600")),D(g.Button,"inheritContainedBg",P("palette-grey-800")),D(g.Button,"inheritContainedHoverBg",P("palette-grey-700")),D(g.Chip,"defaultBorder",P("palette-grey-700")),D(g.Chip,"defaultAvatarColor",P("palette-grey-300")),D(g.Chip,"defaultIconColor",P("palette-grey-300")),D(g.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),D(g.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),D(g.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),D(g.LinearProgress,"primaryBg",R(Xe,g.primary.main,.5)),D(g.LinearProgress,"secondaryBg",R(Xe,g.secondary.main,.5)),D(g.LinearProgress,"errorBg",R(Xe,g.error.main,.5)),D(g.LinearProgress,"infoBg",R(Xe,g.info.main,.5)),D(g.LinearProgress,"successBg",R(Xe,g.success.main,.5)),D(g.LinearProgress,"warningBg",R(Xe,g.warning.main,.5)),D(g.Skeleton,"bg",v?R(qr,g.text.primary,.13):`rgba(${P("palette-text-primaryChannel")} / 0.13)`),D(g.Slider,"primaryTrack",R(Xe,g.primary.main,.5)),D(g.Slider,"secondaryTrack",R(Xe,g.secondary.main,.5)),D(g.Slider,"errorTrack",R(Xe,g.error.main,.5)),D(g.Slider,"infoTrack",R(Xe,g.info.main,.5)),D(g.Slider,"successTrack",R(Xe,g.success.main,.5)),D(g.Slider,"warningTrack",R(Xe,g.warning.main,.5));const O=v?R(Qe,g.background.default,.985):An(g.background.default,.98);D(g.SnackbarContent,"bg",O),D(g.SnackbarContent,"color",dr(()=>v?$u.text.primary:g.getContrastText(O))),D(g.SpeedDialAction,"fabHoverBg",An(g.background.paper,.15)),D(g.StepConnector,"border",P("palette-grey-600")),D(g.StepContent,"border",P("palette-grey-600")),D(g.Switch,"defaultColor",P("palette-grey-300")),D(g.Switch,"defaultDisabledColor",P("palette-grey-600")),D(g.Switch,"primaryDisabledColor",R(Xe,g.primary.main,.55)),D(g.Switch,"secondaryDisabledColor",R(Xe,g.secondary.main,.55)),D(g.Switch,"errorDisabledColor",R(Xe,g.error.main,.55)),D(g.Switch,"infoDisabledColor",R(Xe,g.info.main,.55)),D(g.Switch,"successDisabledColor",R(Xe,g.success.main,.55)),D(g.Switch,"warningDisabledColor",R(Xe,g.warning.main,.55)),D(g.TableCell,"border",R(Xe,R(qr,g.divider,1),.68)),D(g.Tooltip,"bg",R(qr,g.grey[700],.92))}Pr(g.background,"default"),Pr(g.background,"paper"),Pr(g.common,"background"),Pr(g.common,"onBackground"),Pr(g,"divider"),Object.keys(g).forEach(O=>{const E=g[O];O!=="tonalOffset"&&E&&typeof E=="object"&&(E.main&&D(g[O],"mainChannel",qo(Yo(E.main))),E.light&&D(g[O],"lightChannel",qo(Yo(E.light))),E.dark&&D(g[O],"darkChannel",qo(Yo(E.dark))),E.contrastText&&D(g[O],"contrastTextChannel",qo(Yo(E.contrastText))),O==="text"&&(Pr(g[O],"primary"),Pr(g[O],"secondary")),O==="action"&&(E.active&&Pr(g[O],"active"),E.selected&&Pr(g[O],"selected")))})}),k=t.reduce((L,g)=>$t(L,g),k);const T={prefix:s,disableCssColorScheme:n,shouldSkipGeneratingVar:a,getSelector:Sg(k),enableContrastVars:i},{vars:I,generateThemeVars:A,generateStyleSheets:M}=ym(k,T);return k.vars=I,Object.entries(k.colorSchemes[k.defaultColorScheme]).forEach(([L,g])=>{k[L]=g}),k.generateThemeVars=A,k.generateStyleSheets=M,k.generateSpacing=function(){return au(u.spacing,bs(this))},k.getColorSchemeSelector=vm(l),k.spacing=k.generateSpacing(),k.shouldSkipGeneratingVar=a,k.unstable_sxConfig={...xn,...u==null?void 0:u.unstable_sxConfig},k.unstable_sx=function(g){return Ur({sx:g,theme:this})},k.toRuntimeSource=Pu,k}function Ml(e,t,r){e.colorSchemes&&r&&(e.colorSchemes[t]={...r!==!0&&r,palette:fa({...r===!0?{}:r.palette,mode:t})})}function ma(e={},...t){const{palette:r,cssVariables:o=!1,colorSchemes:n=r?void 0:{light:!0},defaultColorScheme:s=r==null?void 0:r.mode,...i}=e,a=s||"light",l=n==null?void 0:n[a],c={...n,...r?{[a]:{...typeof l!="boolean"&&l,palette:r}}:void 0};if(o===!1){if(!("colorSchemes"in e))return Pi(e,...t);let u=r;"palette"in e||c[a]&&(c[a]!==!0?u=c[a].palette:a==="dark"&&(u={mode:"dark"}));const d=Pi({...e,palette:u},...t);return d.defaultColorScheme=a,d.colorSchemes=c,d.palette.mode==="light"&&(d.colorSchemes.light={...c.light!==!0&&c.light,palette:d.palette},Ml(d,"dark",c.dark)),d.palette.mode==="dark"&&(d.colorSchemes.dark={...c.dark!==!0&&c.dark,palette:d.palette},Ml(d,"light",c.light)),d}return!r&&!("light"in c)&&a==="light"&&(c.light=!0),kg({...i,colorSchemes:c,defaultColorScheme:a,...typeof o!="boolean"&&o},...t)}function Rg(e){return String(e).match(/[\d.\-+]*\s*(.*)/)[1]||""}function Pg(e){return parseFloat(e)}const Es=ma();function $r(){const e=Sn(Es);return e[Zt]||e}function a$({props:e,name:t}){return ks({props:e,name:t,defaultTheme:Es,themeId:Zt})}function Ms(e){return e!=="ownerState"&&e!=="theme"&&e!=="sx"&&e!=="as"}const Et=e=>Ms(e)&&e!=="classes",j=fu({themeId:Zt,defaultTheme:Es,rootShouldForwardProp:Et});function Tg({theme:e,...t}){const r=Zt in e?e[Zt]:void 0;return w.jsx(Su,{...t,themeId:r?Zt:void 0,theme:r||e})}const On={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:Ig,useColorScheme:l$}=mm({themeId:Zt,theme:()=>ma({cssVariables:!0}),colorSchemeStorageKey:On.colorSchemeStorageKey,modeStorageKey:On.modeStorageKey,defaultColorScheme:{light:On.defaultLightColorScheme,dark:On.defaultDarkColorScheme},resolveTheme:e=>{const t={...e,typography:Ru(e.palette,e.typography)};return t.unstable_sx=function(o){return Ur({sx:o,theme:this})},t}}),Eg=Ig;function c$({theme:e,...t}){const r=p.useMemo(()=>{if(typeof e=="function")return e;const o=Zt in e?e[Zt]:e;return"colorSchemes"in o?null:"vars"in o?e:{...e,vars:null}},[e]);return r?w.jsx(Tg,{theme:r,...t}):w.jsx(Eg,{theme:e,...t})}const Mg=ue("MuiBox",["root"]),Ag=ma(),u$=Of({themeId:Zt,defaultTheme:Ag,defaultClassName:Mg.root,generateClassName:cu.generate});function Al(...e){return e.reduce((t,r)=>r==null?t:function(...n){t.apply(this,n),r.apply(this,n)},()=>{})}function Og(e){return w.jsx(lu,{...e,defaultTheme:Es,themeId:Zt})}function ga(e){return function(r){return w.jsx(Og,{styles:typeof e=="function"?o=>e({theme:o,...r}):e})}}function Lg(){return $s}const re=im;function me(e){return rm(e)}function Bg(e){return ce("MuiSvgIcon",e)}ue("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const Ng=e=>{const{color:t,fontSize:r,classes:o}=e,n={root:["root",t!=="inherit"&&`color${B(t)}`,`fontSize${B(r)}`]};return de(n,Bg,o)},jg=j("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.color!=="inherit"&&t[`color${B(r.color)}`],t[`fontSize${B(r.fontSize)}`]]}})(re(({theme:e})=>{var t,r,o,n,s,i,a,l,c,u,d,f,m,h;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(n=(t=e.transitions)==null?void 0:t.create)==null?void 0:n.call(t,"fill",{duration:(o=(r=(e.vars??e).transitions)==null?void 0:r.duration)==null?void 0:o.shorter}),variants:[{props:b=>!b.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((i=(s=e.typography)==null?void 0:s.pxToRem)==null?void 0:i.call(s,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((l=(a=e.typography)==null?void 0:a.pxToRem)==null?void 0:l.call(a,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((u=(c=e.typography)==null?void 0:c.pxToRem)==null?void 0:u.call(c,35))||"2.1875rem"}},...Object.entries((e.vars??e).palette).filter(([,b])=>b&&b.main).map(([b])=>{var y,S;return{props:{color:b},style:{color:(S=(y=(e.vars??e).palette)==null?void 0:y[b])==null?void 0:S.main}}}),{props:{color:"action"},style:{color:(f=(d=(e.vars??e).palette)==null?void 0:d.action)==null?void 0:f.active}},{props:{color:"disabled"},style:{color:(h=(m=(e.vars??e).palette)==null?void 0:m.action)==null?void 0:h.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),Ii=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiSvgIcon"}),{children:n,className:s,color:i="inherit",component:a="svg",fontSize:l="medium",htmlColor:c,inheritViewBox:u=!1,titleAccess:d,viewBox:f="0 0 24 24",...m}=o,h=p.isValidElement(n)&&n.type==="svg",b={...o,color:i,component:a,fontSize:l,instanceFontSize:t.fontSize,inheritViewBox:u,viewBox:f,hasSvgAsChild:h},y={};u||(y.viewBox=f);const S=Ng(b);return w.jsxs(jg,{as:a,className:V(S.root,s),focusable:"false",color:c,"aria-hidden":d?void 0:!0,role:d?"img":void 0,ref:r,...y,...m,...h&&n.props,ownerState:b,children:[h?n.props.children:n,d?w.jsx("title",{children:d}):null]})});Ii.muiName="SvgIcon";function ur(e,t){function r(o,n){return w.jsx(Ii,{"data-testid":void 0,ref:n,...o,children:e})}return r.muiName=Ii.muiName,p.memo(p.forwardRef(r))}function Cn(e,t=166){let r;function o(...n){const s=()=>{e.apply(this,n)};clearTimeout(r),r=setTimeout(s,t)}return o.clear=()=>{clearTimeout(r)},o}function mt(e){return e&&e.ownerDocument||document}function Ut(e){return mt(e).defaultView||window}function Ei(e,t){typeof e=="function"?e(t):e&&(e.current=t)}function Wr(e){const{controlled:t,default:r,name:o,state:n="value"}=e,{current:s}=p.useRef(t!==void 0),[i,a]=p.useState(r),l=s?t:i,c=p.useCallback(u=>{s||a(u)},[]);return[l,c]}function vt(e){const t=p.useRef(e);return It(()=>{t.current=e}),p.useRef((...r)=>(0,t.current)(...r)).current}function ot(...e){const t=p.useRef(void 0),r=p.useCallback(o=>{const n=e.map(s=>{if(s==null)return null;if(typeof s=="function"){const i=s,a=i(o);return typeof a=="function"?a:()=>{i(null)}}return s.current=o,()=>{s.current=null}});return()=>{n.forEach(s=>s==null?void 0:s())}},e);return p.useMemo(()=>e.every(o=>o==null)?null:o=>{t.current&&(t.current(),t.current=void 0),o!=null&&(t.current=r(o))},e)}function zg(e,t){const r=e.charCodeAt(2);return e[0]==="o"&&e[1]==="n"&&r>=65&&r<=90&&typeof t=="function"}function ha(e,t){if(!e)return t;function r(i,a){const l={};return Object.keys(a).forEach(c=>{zg(c,a[c])&&typeof i[c]=="function"&&(l[c]=(...u)=>{i[c](...u),a[c](...u)})}),l}if(typeof e=="function"||typeof t=="function")return i=>{const a=typeof t=="function"?t(i):t,l=typeof e=="function"?e({...i,...a}):e,c=V(i==null?void 0:i.className,a==null?void 0:a.className,l==null?void 0:l.className),u=r(l,a);return{...a,...l,...u,...!!c&&{className:c},...(a==null?void 0:a.style)&&(l==null?void 0:l.style)&&{style:{...a.style,...l.style}},...(a==null?void 0:a.sx)&&(l==null?void 0:l.sx)&&{sx:[...Array.isArray(a.sx)?a.sx:[a.sx],...Array.isArray(l.sx)?l.sx:[l.sx]]}}};const o=t,n=r(e,o),s=V(o==null?void 0:o.className,e==null?void 0:e.className);return{...t,...e,...n,...!!s&&{className:s},...(o==null?void 0:o.style)&&(e==null?void 0:e.style)&&{style:{...o.style,...e.style}},...(o==null?void 0:o.sx)&&(e==null?void 0:e.sx)&&{sx:[...Array.isArray(o.sx)?o.sx:[o.sx],...Array.isArray(e.sx)?e.sx:[e.sx]]}}}function Vr(e){try{return e.matches(":focus-visible")}catch{}return!1}const Ol={};function Eu(e,t){const r=p.useRef(Ol);return r.current===Ol&&(r.current=e(t)),r}class rs{constructor(){Nr(this,"mountEffect",()=>{this.shouldMount&&!this.didMount&&this.ref.current!==null&&(this.didMount=!0,this.mounted.resolve())});this.ref={current:null},this.mounted=null,this.didMount=!1,this.shouldMount=!1,this.setShouldMount=null}static create(){return new rs}static use(){const t=Eu(rs.create).current,[r,o]=p.useState(!1);return t.shouldMount=r,t.setShouldMount=o,p.useEffect(t.mountEffect,[r]),t}mount(){return this.mounted||(this.mounted=Dg(),this.shouldMount=!0,this.setShouldMount(this.shouldMount)),this.mounted}start(...t){this.mount().then(()=>{var r;return(r=this.ref.current)==null?void 0:r.start(...t)})}stop(...t){this.mount().then(()=>{var r;return(r=this.ref.current)==null?void 0:r.stop(...t)})}pulsate(...t){this.mount().then(()=>{var r;return(r=this.ref.current)==null?void 0:r.pulsate(...t)})}}function Fg(){return rs.use()}function Dg(){let e,t;const r=new Promise((o,n)=>{e=o,t=n});return r.resolve=e,r.reject=t,r}function ba(e,t){if(e==null)return{};var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.indexOf(o)!==-1)continue;r[o]=e[o]}return r}function Mi(e,t){return Mi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,o){return r.__proto__=o,r},Mi(e,t)}function ya(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Mi(e,t)}function Wg(e,t){return e.classList?!!t&&e.classList.contains(t):(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")!==-1}function Ug(e,t){e.classList?e.classList.add(t):Wg(e,t)||(typeof e.className=="string"?e.className=e.className+" "+t:e.setAttribute("class",(e.className&&e.className.baseVal||"")+" "+t))}function Ll(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}function Hg(e,t){e.classList?e.classList.remove(t):typeof e.className=="string"?e.className=Ll(e.className,t):e.setAttribute("class",Ll(e.className&&e.className.baseVal||"",t))}const Bl={disabled:!1},os=Yt.createContext(null);var Mu=function(t){return t.scrollTop},Xo="unmounted",Xr="exited",Qr="entering",bo="entered",Ai="exiting",nr=(function(e){ya(t,e);function t(o,n){var s;s=e.call(this,o,n)||this;var i=n,a=i&&!i.isMounting?o.enter:o.appear,l;return s.appearStatus=null,o.in?a?(l=Xr,s.appearStatus=Qr):l=bo:o.unmountOnExit||o.mountOnEnter?l=Xo:l=Xr,s.state={status:l},s.nextCallback=null,s}t.getDerivedStateFromProps=function(n,s){var i=n.in;return i&&s.status===Xo?{status:Xr}:null};var r=t.prototype;return r.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},r.componentDidUpdate=function(n){var s=null;if(n!==this.props){var i=this.state.status;this.props.in?i!==Qr&&i!==bo&&(s=Qr):(i===Qr||i===bo)&&(s=Ai)}this.updateStatus(!1,s)},r.componentWillUnmount=function(){this.cancelNextCallback()},r.getTimeouts=function(){var n=this.props.timeout,s,i,a;return s=i=a=n,n!=null&&typeof n!="number"&&(s=n.exit,i=n.enter,a=n.appear!==void 0?n.appear:i),{exit:s,enter:i,appear:a}},r.updateStatus=function(n,s){if(n===void 0&&(n=!1),s!==null)if(this.cancelNextCallback(),s===Qr){if(this.props.unmountOnExit||this.props.mountOnEnter){var i=this.props.nodeRef?this.props.nodeRef.current:En.findDOMNode(this);i&&Mu(i)}this.performEnter(n)}else this.performExit();else this.props.unmountOnExit&&this.state.status===Xr&&this.setState({status:Xo})},r.performEnter=function(n){var s=this,i=this.props.enter,a=this.context?this.context.isMounting:n,l=this.props.nodeRef?[a]:[En.findDOMNode(this),a],c=l[0],u=l[1],d=this.getTimeouts(),f=a?d.appear:d.enter;if(!n&&!i||Bl.disabled){this.safeSetState({status:bo},function(){s.props.onEntered(c)});return}this.props.onEnter(c,u),this.safeSetState({status:Qr},function(){s.props.onEntering(c,u),s.onTransitionEnd(f,function(){s.safeSetState({status:bo},function(){s.props.onEntered(c,u)})})})},r.performExit=function(){var n=this,s=this.props.exit,i=this.getTimeouts(),a=this.props.nodeRef?void 0:En.findDOMNode(this);if(!s||Bl.disabled){this.safeSetState({status:Xr},function(){n.props.onExited(a)});return}this.props.onExit(a),this.safeSetState({status:Ai},function(){n.props.onExiting(a),n.onTransitionEnd(i.exit,function(){n.safeSetState({status:Xr},function(){n.props.onExited(a)})})})},r.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},r.safeSetState=function(n,s){s=this.setNextCallback(s),this.setState(n,s)},r.setNextCallback=function(n){var s=this,i=!0;return this.nextCallback=function(a){i&&(i=!1,s.nextCallback=null,n(a))},this.nextCallback.cancel=function(){i=!1},this.nextCallback},r.onTransitionEnd=function(n,s){this.setNextCallback(s);var i=this.props.nodeRef?this.props.nodeRef.current:En.findDOMNode(this),a=n==null&&!this.props.addEndListener;if(!i||a){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var l=this.props.nodeRef?[this.nextCallback]:[i,this.nextCallback],c=l[0],u=l[1];this.props.addEndListener(c,u)}n!=null&&setTimeout(this.nextCallback,n)},r.render=function(){var n=this.state.status;if(n===Xo)return null;var s=this.props,i=s.children;s.in,s.mountOnEnter,s.unmountOnExit,s.appear,s.enter,s.exit,s.timeout,s.addEndListener,s.onEnter,s.onEntering,s.onEntered,s.onExit,s.onExiting,s.onExited,s.nodeRef;var a=ba(s,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return Yt.createElement(os.Provider,{value:null},typeof i=="function"?i(n,a):Yt.cloneElement(Yt.Children.only(i),a))},t})(Yt.Component);nr.contextType=os;nr.propTypes={};function mo(){}nr.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:mo,onEntering:mo,onEntered:mo,onExit:mo,onExiting:mo,onExited:mo};nr.UNMOUNTED=Xo;nr.EXITED=Xr;nr.ENTERING=Qr;nr.ENTERED=bo;nr.EXITING=Ai;var Vg=function(t,r){return t&&r&&r.split(" ").forEach(function(o){return Ug(t,o)})},ci=function(t,r){return t&&r&&r.split(" ").forEach(function(o){return Hg(t,o)})},Au=(function(e){ya(t,e);function t(){for(var o,n=arguments.length,s=new Array(n),i=0;i<n;i++)s[i]=arguments[i];return o=e.call.apply(e,[this].concat(s))||this,o.appliedClasses={appear:{},enter:{},exit:{}},o.onEnter=function(a,l){var c=o.resolveArguments(a,l),u=c[0],d=c[1];o.removeClasses(u,"exit"),o.addClass(u,d?"appear":"enter","base"),o.props.onEnter&&o.props.onEnter(a,l)},o.onEntering=function(a,l){var c=o.resolveArguments(a,l),u=c[0],d=c[1],f=d?"appear":"enter";o.addClass(u,f,"active"),o.props.onEntering&&o.props.onEntering(a,l)},o.onEntered=function(a,l){var c=o.resolveArguments(a,l),u=c[0],d=c[1],f=d?"appear":"enter";o.removeClasses(u,f),o.addClass(u,f,"done"),o.props.onEntered&&o.props.onEntered(a,l)},o.onExit=function(a){var l=o.resolveArguments(a),c=l[0];o.removeClasses(c,"appear"),o.removeClasses(c,"enter"),o.addClass(c,"exit","base"),o.props.onExit&&o.props.onExit(a)},o.onExiting=function(a){var l=o.resolveArguments(a),c=l[0];o.addClass(c,"exit","active"),o.props.onExiting&&o.props.onExiting(a)},o.onExited=function(a){var l=o.resolveArguments(a),c=l[0];o.removeClasses(c,"exit"),o.addClass(c,"exit","done"),o.props.onExited&&o.props.onExited(a)},o.resolveArguments=function(a,l){return o.props.nodeRef?[o.props.nodeRef.current,a]:[a,l]},o.getClassNames=function(a){var l=o.props.classNames,c=typeof l=="string",u=c&&l?l+"-":"",d=c?""+u+a:l[a],f=c?d+"-active":l[a+"Active"],m=c?d+"-done":l[a+"Done"];return{baseClassName:d,activeClassName:f,doneClassName:m}},o}var r=t.prototype;return r.addClass=function(n,s,i){var a=this.getClassNames(s)[i+"ClassName"],l=this.getClassNames("enter"),c=l.doneClassName;s==="appear"&&i==="done"&&c&&(a+=" "+c),i==="active"&&n&&Mu(n),a&&(this.appliedClasses[s][i]=a,Vg(n,a))},r.removeClasses=function(n,s){var i=this.appliedClasses[s],a=i.base,l=i.active,c=i.done;this.appliedClasses[s]={},a&&ci(n,a),l&&ci(n,l),c&&ci(n,c)},r.render=function(){var n=this.props;n.classNames;var s=ba(n,["classNames"]);return Yt.createElement(nr,cn({},s,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},t})(Yt.Component);Au.defaultProps={classNames:""};Au.propTypes={};function _g(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function va(e,t){var r=function(s){return t&&p.isValidElement(s)?t(s):s},o=Object.create(null);return e&&p.Children.map(e,function(n){return n}).forEach(function(n){o[n.key]=r(n)}),o}function Gg(e,t){e=e||{},t=t||{};function r(u){return u in t?t[u]:e[u]}var o=Object.create(null),n=[];for(var s in e)s in t?n.length&&(o[s]=n,n=[]):n.push(s);var i,a={};for(var l in t){if(o[l])for(i=0;i<o[l].length;i++){var c=o[l][i];a[o[l][i]]=r(c)}a[l]=r(l)}for(i=0;i<n.length;i++)a[n[i]]=r(n[i]);return a}function Jr(e,t,r){return r[t]!=null?r[t]:e.props[t]}function Kg(e,t){return va(e.children,function(r){return p.cloneElement(r,{onExited:t.bind(null,r),in:!0,appear:Jr(r,"appear",e),enter:Jr(r,"enter",e),exit:Jr(r,"exit",e)})})}function qg(e,t,r){var o=va(e.children),n=Gg(t,o);return Object.keys(n).forEach(function(s){var i=n[s];if(p.isValidElement(i)){var a=s in t,l=s in o,c=t[s],u=p.isValidElement(c)&&!c.props.in;l&&(!a||u)?n[s]=p.cloneElement(i,{onExited:r.bind(null,i),in:!0,exit:Jr(i,"exit",e),enter:Jr(i,"enter",e)}):!l&&a&&!u?n[s]=p.cloneElement(i,{in:!1}):l&&a&&p.isValidElement(c)&&(n[s]=p.cloneElement(i,{onExited:r.bind(null,i),in:c.props.in,exit:Jr(i,"exit",e),enter:Jr(i,"enter",e)}))}}),n}var Yg=Object.values||function(e){return Object.keys(e).map(function(t){return e[t]})},Xg={component:"div",childFactory:function(t){return t}},xa=(function(e){ya(t,e);function t(o,n){var s;s=e.call(this,o,n)||this;var i=s.handleExited.bind(_g(s));return s.state={contextValue:{isMounting:!0},handleExited:i,firstRender:!0},s}var r=t.prototype;return r.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},r.componentWillUnmount=function(){this.mounted=!1},t.getDerivedStateFromProps=function(n,s){var i=s.children,a=s.handleExited,l=s.firstRender;return{children:l?Kg(n,a):qg(n,i,a),firstRender:!1}},r.handleExited=function(n,s){var i=va(this.props.children);n.key in i||(n.props.onExited&&n.props.onExited(s),this.mounted&&this.setState(function(a){var l=cn({},a.children);return delete l[n.key],{children:l}}))},r.render=function(){var n=this.props,s=n.component,i=n.childFactory,a=ba(n,["component","childFactory"]),l=this.state.contextValue,c=Yg(this.state.children).map(i);return delete a.appear,delete a.enter,delete a.exit,s===null?Yt.createElement(os.Provider,{value:l},c):Yt.createElement(os.Provider,{value:l},Yt.createElement(s,a,c))},t})(Yt.Component);xa.propTypes={};xa.defaultProps=Xg;const Qg=[];function Zg(e){p.useEffect(e,Qg)}let Ou=class Lu{constructor(){Nr(this,"currentId",null);Nr(this,"clear",()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)});Nr(this,"disposeEffect",()=>this.clear)}static create(){return new Lu}start(t,r){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,r()},t)}};function vo(){const e=Eu(Ou.create).current;return Zg(e.disposeEffect),e}function Jg(e){const{className:t,classes:r,pulsate:o=!1,rippleX:n,rippleY:s,rippleSize:i,in:a,onExited:l,timeout:c}=e,[u,d]=p.useState(!1),f=V(t,r.ripple,r.rippleVisible,o&&r.ripplePulsate),m={width:i,height:i,top:-(i/2)+s,left:-(i/2)+n},h=V(r.child,u&&r.childLeaving,o&&r.childPulsate);return!a&&!u&&d(!0),p.useEffect(()=>{if(!a&&l!=null){const b=setTimeout(l,c);return()=>{clearTimeout(b)}}},[l,a,c]),w.jsx("span",{className:f,style:m,children:w.jsx("span",{className:h})})}const Gt=ue("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),Oi=550,eh=80,th=wr`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`,rh=wr`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`,oh=wr`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`,nh=j("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),sh=j(Jg,{name:"MuiTouchRipple",slot:"Ripple"})`
  opacity: 0;
  position: absolute;

  &.${Gt.rippleVisible} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${th};
    animation-duration: ${Oi}ms;
    animation-timing-function: ${({theme:e})=>e.transitions.easing.easeInOut};
  }

  &.${Gt.ripplePulsate} {
    animation-duration: ${({theme:e})=>e.transitions.duration.shorter}ms;
  }

  & .${Gt.child} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${Gt.childLeaving} {
    opacity: 0;
    animation-name: ${rh};
    animation-duration: ${Oi}ms;
    animation-timing-function: ${({theme:e})=>e.transitions.easing.easeInOut};
  }

  & .${Gt.childPulsate} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${oh};
    animation-duration: 2500ms;
    animation-timing-function: ${({theme:e})=>e.transitions.easing.easeInOut};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`,ih=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiTouchRipple"}),{center:n=!1,classes:s={},className:i,...a}=o,[l,c]=p.useState([]),u=p.useRef(0),d=p.useRef(null);p.useEffect(()=>{d.current&&(d.current(),d.current=null)},[l]);const f=p.useRef(!1),m=vo(),h=p.useRef(null),b=p.useRef(null),y=p.useCallback(v=>{const{pulsate:x,rippleX:k,rippleY:T,rippleSize:I,cb:A}=v;c(M=>[...M,w.jsx(sh,{classes:{ripple:V(s.ripple,Gt.ripple),rippleVisible:V(s.rippleVisible,Gt.rippleVisible),ripplePulsate:V(s.ripplePulsate,Gt.ripplePulsate),child:V(s.child,Gt.child),childLeaving:V(s.childLeaving,Gt.childLeaving),childPulsate:V(s.childPulsate,Gt.childPulsate)},timeout:Oi,pulsate:x,rippleX:k,rippleY:T,rippleSize:I},u.current)]),u.current+=1,d.current=A},[s]),S=p.useCallback((v={},x={},k=()=>{})=>{const{pulsate:T=!1,center:I=n||x.pulsate,fakeElement:A=!1}=x;if((v==null?void 0:v.type)==="mousedown"&&f.current){f.current=!1;return}(v==null?void 0:v.type)==="touchstart"&&(f.current=!0);const M=A?null:b.current,L=M?M.getBoundingClientRect():{width:0,height:0,left:0,top:0};let g,P,R;if(I||v===void 0||v.clientX===0&&v.clientY===0||!v.clientX&&!v.touches)g=Math.round(L.width/2),P=Math.round(L.height/2);else{const{clientX:O,clientY:E}=v.touches&&v.touches.length>0?v.touches[0]:v;g=Math.round(O-L.left),P=Math.round(E-L.top)}if(I)R=Math.sqrt((2*L.width**2+L.height**2)/3),R%2===0&&(R+=1);else{const O=Math.max(Math.abs((M?M.clientWidth:0)-g),g)*2+2,E=Math.max(Math.abs((M?M.clientHeight:0)-P),P)*2+2;R=Math.sqrt(O**2+E**2)}v!=null&&v.touches?h.current===null&&(h.current=()=>{y({pulsate:T,rippleX:g,rippleY:P,rippleSize:R,cb:k})},m.start(eh,()=>{h.current&&(h.current(),h.current=null)})):y({pulsate:T,rippleX:g,rippleY:P,rippleSize:R,cb:k})},[n,y,m]),$=p.useCallback(()=>{S({},{pulsate:!0})},[S]),C=p.useCallback((v,x)=>{if(m.clear(),(v==null?void 0:v.type)==="touchend"&&h.current){h.current(),h.current=null,m.start(0,()=>{C(v,x)});return}h.current=null,c(k=>k.length>0?k.slice(1):k),d.current=x},[m]);return p.useImperativeHandle(r,()=>({pulsate:$,start:S,stop:C}),[$,S,C]),w.jsx(nh,{className:V(Gt.root,s.root,i),ref:b,...a,children:w.jsx(xa,{component:null,exit:!0,children:l})})});function ah(e){return ce("MuiButtonBase",e)}const lh=ue("MuiButtonBase",["root","disabled","focusVisible"]),ch=e=>{const{disabled:t,focusVisible:r,focusVisibleClassName:o,classes:n}=e,i=de({root:["root",t&&"disabled",r&&"focusVisible"]},ah,n);return r&&o&&(i.root+=` ${o}`),i},uh=j("button",{name:"MuiButtonBase",slot:"Root"})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${lh.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),Sr=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiButtonBase"}),{action:n,centerRipple:s=!1,children:i,className:a,component:l="button",disabled:c=!1,disableRipple:u=!1,disableTouchRipple:d=!1,focusRipple:f=!1,focusVisibleClassName:m,LinkComponent:h="a",onBlur:b,onClick:y,onContextMenu:S,onDragLeave:$,onFocus:C,onFocusVisible:v,onKeyDown:x,onKeyUp:k,onMouseDown:T,onMouseLeave:I,onMouseUp:A,onTouchEnd:M,onTouchMove:L,onTouchStart:g,tabIndex:P=0,TouchRippleProps:R,touchRippleRef:O,type:E,...F}=o,z=p.useRef(null),N=Fg(),Q=ot(N.ref,O),[_,Z]=p.useState(!1);c&&_&&Z(!1),p.useImperativeHandle(n,()=>({focusVisible:()=>{Z(!0),z.current.focus()}}),[]);const se=N.shouldMount&&!u&&!c;p.useEffect(()=>{_&&f&&!u&&N.pulsate()},[u,f,_,N]);const ye=Tr(N,"start",T,d),K=Tr(N,"stop",S,d),q=Tr(N,"stop",$,d),ie=Tr(N,"stop",A,d),we=Tr(N,"stop",te=>{_&&te.preventDefault(),I&&I(te)},d),he=Tr(N,"start",g,d),ee=Tr(N,"stop",M,d),Ce=Tr(N,"stop",L,d),oe=Tr(N,"stop",te=>{Vr(te.target)||Z(!1),b&&b(te)},!1),ge=vt(te=>{z.current||(z.current=te.currentTarget),Vr(te.target)&&(Z(!0),v&&v(te)),C&&C(te)}),ve=()=>{const te=z.current;return l&&l!=="button"&&!(te.tagName==="A"&&te.href)},Le=vt(te=>{f&&!te.repeat&&_&&te.key===" "&&N.stop(te,()=>{N.start(te)}),te.target===te.currentTarget&&ve()&&te.key===" "&&te.preventDefault(),x&&x(te),te.target===te.currentTarget&&ve()&&te.key==="Enter"&&!c&&(te.preventDefault(),y&&y(te))}),Me=vt(te=>{f&&te.key===" "&&_&&!te.defaultPrevented&&N.stop(te,()=>{N.pulsate(te)}),k&&k(te),y&&te.target===te.currentTarget&&ve()&&te.key===" "&&!te.defaultPrevented&&y(te)});let $e=l;$e==="button"&&(F.href||F.to)&&($e=h);const J={};$e==="button"?(J.type=E===void 0?"button":E,J.disabled=c):(!F.href&&!F.to&&(J.role="button"),c&&(J["aria-disabled"]=c));const Ae=ot(r,z),pe={...o,centerRipple:s,component:l,disabled:c,disableRipple:u,disableTouchRipple:d,focusRipple:f,tabIndex:P,focusVisible:_},Re=ch(pe);return w.jsxs(uh,{as:$e,className:V(Re.root,a),ownerState:pe,onBlur:oe,onClick:y,onContextMenu:K,onFocus:ge,onKeyDown:Le,onKeyUp:Me,onMouseDown:ye,onMouseLeave:we,onMouseUp:ie,onDragLeave:q,onTouchEnd:ee,onTouchMove:Ce,onTouchStart:he,ref:Ae,tabIndex:c?-1:P,type:E,...J,...F,children:[i,se?w.jsx(ih,{ref:Q,center:s,...R}):null]})});function Tr(e,t,r,o=!1){return vt(n=>(r&&r(n),o||e[t](n),!0))}function dh(e){return typeof e.main=="string"}function ph(e,t=[]){if(!dh(e))return!1;for(const r of t)if(!e.hasOwnProperty(r)||typeof e[r]!="string")return!1;return!0}function et(e=[]){return([,t])=>t&&ph(t,e)}function fh(e){return ce("MuiCircularProgress",e)}ue("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);const jr=44,Li=wr`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,Bi=wr`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,mh=typeof Li!="string"?_r`
        animation: ${Li} 1.4s linear infinite;
      `:null,gh=typeof Bi!="string"?_r`
        animation: ${Bi} 1.4s ease-in-out infinite;
      `:null,hh=e=>{const{classes:t,variant:r,color:o,disableShrink:n}=e,s={root:["root",r,`color${B(o)}`],svg:["svg"],circle:["circle",`circle${B(r)}`,n&&"circleDisableShrink"]};return de(s,fh,t)},bh=j("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[r.variant],t[`color${B(r.color)}`]]}})(re(({theme:e})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("transform")}},{props:{variant:"indeterminate"},style:mh||{animation:`${Li} 1.4s linear infinite`}},...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}}))]}))),yh=j("svg",{name:"MuiCircularProgress",slot:"Svg"})({display:"block"}),vh=j("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.circle,t[`circle${B(r.variant)}`],r.disableShrink&&t.circleDisableShrink]}})(re(({theme:e})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:t})=>t.variant==="indeterminate"&&!t.disableShrink,style:gh||{animation:`${Bi} 1.4s ease-in-out infinite`}}]}))),Bu=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiCircularProgress"}),{className:n,color:s="primary",disableShrink:i=!1,size:a=40,style:l,thickness:c=3.6,value:u=0,variant:d="indeterminate",...f}=o,m={...o,color:s,disableShrink:i,size:a,thickness:c,value:u,variant:d},h=hh(m),b={},y={},S={};if(d==="determinate"){const $=2*Math.PI*((jr-c)/2);b.strokeDasharray=$.toFixed(3),S["aria-valuenow"]=Math.round(u),b.strokeDashoffset=`${((100-u)/100*$).toFixed(3)}px`,y.transform="rotate(-90deg)"}return w.jsx(bh,{className:V(h.root,n),style:{width:a,height:a,...y,...l},ownerState:m,ref:r,role:"progressbar",...S,...f,children:w.jsx(yh,{className:h.svg,ownerState:m,viewBox:`${jr/2} ${jr/2} ${jr} ${jr}`,children:w.jsx(vh,{className:h.circle,style:b,ownerState:m,cx:jr,cy:jr,r:(jr-c)/2,fill:"none",strokeWidth:c})})})});function xh(e){return ce("MuiButton",e)}const Yr=ue("MuiButton",["root","text","textInherit","textPrimary","textSecondary","textSuccess","textError","textInfo","textWarning","outlined","outlinedInherit","outlinedPrimary","outlinedSecondary","outlinedSuccess","outlinedError","outlinedInfo","outlinedWarning","contained","containedInherit","containedPrimary","containedSecondary","containedSuccess","containedError","containedInfo","containedWarning","disableElevation","focusVisible","disabled","colorInherit","colorPrimary","colorSecondary","colorSuccess","colorError","colorInfo","colorWarning","textSizeSmall","textSizeMedium","textSizeLarge","outlinedSizeSmall","outlinedSizeMedium","outlinedSizeLarge","containedSizeSmall","containedSizeMedium","containedSizeLarge","sizeMedium","sizeSmall","sizeLarge","fullWidth","startIcon","endIcon","icon","iconSizeSmall","iconSizeMedium","iconSizeLarge","loading","loadingWrapper","loadingIconPlaceholder","loadingIndicator","loadingPositionCenter","loadingPositionStart","loadingPositionEnd"]),Sh=p.createContext({}),Ch=p.createContext(void 0),wh=e=>{const{color:t,disableElevation:r,fullWidth:o,size:n,variant:s,loading:i,loadingPosition:a,classes:l}=e,c={root:["root",i&&"loading",s,`${s}${B(t)}`,`size${B(n)}`,`${s}Size${B(n)}`,`color${B(t)}`,r&&"disableElevation",o&&"fullWidth",i&&`loadingPosition${B(a)}`],startIcon:["icon","startIcon",`iconSize${B(n)}`],endIcon:["icon","endIcon",`iconSize${B(n)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]},u=de(c,xh,l);return{...l,...u}},Nu=[{props:{size:"small"},style:{"& > *:nth-of-type(1)":{fontSize:18}}},{props:{size:"medium"},style:{"& > *:nth-of-type(1)":{fontSize:20}}},{props:{size:"large"},style:{"& > *:nth-of-type(1)":{fontSize:22}}}],$h=j(Sr,{shouldForwardProp:e=>Et(e)||e==="classes",name:"MuiButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[r.variant],t[`${r.variant}${B(r.color)}`],t[`size${B(r.size)}`],t[`${r.variant}Size${B(r.size)}`],r.color==="inherit"&&t.colorInherit,r.disableElevation&&t.disableElevation,r.fullWidth&&t.fullWidth,r.loading&&t.loading]}})(re(({theme:e})=>{const t=e.palette.mode==="light"?e.palette.grey[300]:e.palette.grey[800],r=e.palette.mode==="light"?e.palette.grey.A100:e.palette.grey[700];return{...e.typography.button,minWidth:64,padding:"6px 16px",border:0,borderRadius:(e.vars||e).shape.borderRadius,transition:e.transitions.create(["background-color","box-shadow","border-color","color"],{duration:e.transitions.duration.short}),"&:hover":{textDecoration:"none"},[`&.${Yr.disabled}`]:{color:(e.vars||e).palette.action.disabled},variants:[{props:{variant:"contained"},style:{color:"var(--variant-containedColor)",backgroundColor:"var(--variant-containedBg)",boxShadow:(e.vars||e).shadows[2],"&:hover":{boxShadow:(e.vars||e).shadows[4],"@media (hover: none)":{boxShadow:(e.vars||e).shadows[2]}},"&:active":{boxShadow:(e.vars||e).shadows[8]},[`&.${Yr.focusVisible}`]:{boxShadow:(e.vars||e).shadows[6]},[`&.${Yr.disabled}`]:{color:(e.vars||e).palette.action.disabled,boxShadow:(e.vars||e).shadows[0],backgroundColor:(e.vars||e).palette.action.disabledBackground}}},{props:{variant:"outlined"},style:{padding:"5px 15px",border:"1px solid currentColor",borderColor:"var(--variant-outlinedBorder, currentColor)",backgroundColor:"var(--variant-outlinedBg)",color:"var(--variant-outlinedColor)",[`&.${Yr.disabled}`]:{border:`1px solid ${(e.vars||e).palette.action.disabledBackground}`}}},{props:{variant:"text"},style:{padding:"6px 8px",color:"var(--variant-textColor)",backgroundColor:"var(--variant-textBg)"}},...Object.entries(e.palette).filter(et()).map(([o])=>({props:{color:o},style:{"--variant-textColor":(e.vars||e).palette[o].main,"--variant-outlinedColor":(e.vars||e).palette[o].main,"--variant-outlinedBorder":e.alpha((e.vars||e).palette[o].main,.5),"--variant-containedColor":(e.vars||e).palette[o].contrastText,"--variant-containedBg":(e.vars||e).palette[o].main,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":(e.vars||e).palette[o].dark,"--variant-textBg":e.alpha((e.vars||e).palette[o].main,(e.vars||e).palette.action.hoverOpacity),"--variant-outlinedBorder":(e.vars||e).palette[o].main,"--variant-outlinedBg":e.alpha((e.vars||e).palette[o].main,(e.vars||e).palette.action.hoverOpacity)}}}})),{props:{color:"inherit"},style:{color:"inherit",borderColor:"currentColor","--variant-containedBg":e.vars?e.vars.palette.Button.inheritContainedBg:t,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":e.vars?e.vars.palette.Button.inheritContainedHoverBg:r,"--variant-textBg":e.alpha((e.vars||e).palette.text.primary,(e.vars||e).palette.action.hoverOpacity),"--variant-outlinedBg":e.alpha((e.vars||e).palette.text.primary,(e.vars||e).palette.action.hoverOpacity)}}}},{props:{size:"small",variant:"text"},style:{padding:"4px 5px",fontSize:e.typography.pxToRem(13)}},{props:{size:"large",variant:"text"},style:{padding:"8px 11px",fontSize:e.typography.pxToRem(15)}},{props:{size:"small",variant:"outlined"},style:{padding:"3px 9px",fontSize:e.typography.pxToRem(13)}},{props:{size:"large",variant:"outlined"},style:{padding:"7px 21px",fontSize:e.typography.pxToRem(15)}},{props:{size:"small",variant:"contained"},style:{padding:"4px 10px",fontSize:e.typography.pxToRem(13)}},{props:{size:"large",variant:"contained"},style:{padding:"8px 22px",fontSize:e.typography.pxToRem(15)}},{props:{disableElevation:!0},style:{boxShadow:"none","&:hover":{boxShadow:"none"},[`&.${Yr.focusVisible}`]:{boxShadow:"none"},"&:active":{boxShadow:"none"},[`&.${Yr.disabled}`]:{boxShadow:"none"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{loadingPosition:"center"},style:{transition:e.transitions.create(["background-color","box-shadow","border-color"],{duration:e.transitions.duration.short}),[`&.${Yr.loading}`]:{color:"transparent"}}}]}})),kh=j("span",{name:"MuiButton",slot:"StartIcon",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.startIcon,r.loading&&t.startIconLoadingStart,t[`iconSize${B(r.size)}`]]}})(({theme:e})=>({display:"inherit",marginRight:8,marginLeft:-4,variants:[{props:{size:"small"},style:{marginLeft:-2}},{props:{loadingPosition:"start",loading:!0},style:{transition:e.transitions.create(["opacity"],{duration:e.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"start",loading:!0,fullWidth:!0},style:{marginRight:-8}},...Nu]})),Rh=j("span",{name:"MuiButton",slot:"EndIcon",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.endIcon,r.loading&&t.endIconLoadingEnd,t[`iconSize${B(r.size)}`]]}})(({theme:e})=>({display:"inherit",marginRight:-4,marginLeft:8,variants:[{props:{size:"small"},style:{marginRight:-2}},{props:{loadingPosition:"end",loading:!0},style:{transition:e.transitions.create(["opacity"],{duration:e.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"end",loading:!0,fullWidth:!0},style:{marginLeft:-8}},...Nu]})),Ph=j("span",{name:"MuiButton",slot:"LoadingIndicator"})(({theme:e})=>({display:"none",position:"absolute",visibility:"visible",variants:[{props:{loading:!0},style:{display:"flex"}},{props:{loadingPosition:"start"},style:{left:14}},{props:{loadingPosition:"start",size:"small"},style:{left:10}},{props:{variant:"text",loadingPosition:"start"},style:{left:6}},{props:{loadingPosition:"center"},style:{left:"50%",transform:"translate(-50%)",color:(e.vars||e).palette.action.disabled}},{props:{loadingPosition:"end"},style:{right:14}},{props:{loadingPosition:"end",size:"small"},style:{right:10}},{props:{variant:"text",loadingPosition:"end"},style:{right:6}},{props:{loadingPosition:"start",fullWidth:!0},style:{position:"relative",left:-10}},{props:{loadingPosition:"end",fullWidth:!0},style:{position:"relative",right:-10}}]})),Nl=j("span",{name:"MuiButton",slot:"LoadingIconPlaceholder"})({display:"inline-block",width:"1em",height:"1em"}),d$=p.forwardRef(function(t,r){const o=p.useContext(Sh),n=p.useContext(Ch),s=Po(o,t),i=me({props:s,name:"MuiButton"}),{children:a,color:l="primary",component:c="button",className:u,disabled:d=!1,disableElevation:f=!1,disableFocusRipple:m=!1,endIcon:h,focusVisibleClassName:b,fullWidth:y=!1,id:S,loading:$=null,loadingIndicator:C,loadingPosition:v="center",size:x="medium",startIcon:k,type:T,variant:I="text",...A}=i,M=xr(S),L=C??w.jsx(Bu,{"aria-labelledby":M,color:"inherit",size:16}),g={...i,color:l,component:c,disabled:d,disableElevation:f,disableFocusRipple:m,fullWidth:y,loading:$,loadingIndicator:L,loadingPosition:v,size:x,type:T,variant:I},P=wh(g),R=(k||$&&v==="start")&&w.jsx(kh,{className:P.startIcon,ownerState:g,children:k||w.jsx(Nl,{className:P.loadingIconPlaceholder,ownerState:g})}),O=(h||$&&v==="end")&&w.jsx(Rh,{className:P.endIcon,ownerState:g,children:h||w.jsx(Nl,{className:P.loadingIconPlaceholder,ownerState:g})}),E=n||"",F=typeof $=="boolean"?w.jsx("span",{className:P.loadingWrapper,style:{display:"contents"},children:$&&w.jsx(Ph,{className:P.loadingIndicator,ownerState:g,children:L})}):null;return w.jsxs($h,{ownerState:g,className:V(o.className,P.root,u,E),component:c,disabled:d||$,focusRipple:!m,focusVisibleClassName:V(P.focusVisible,b),ref:r,type:T,id:$?M:S,...A,classes:P,children:[R,v!=="end"&&F,a,v==="end"&&F,O]})}),p$=$m({createStyledComponent:j("div",{name:"MuiContainer",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[`maxWidth${B(String(r.maxWidth))}`],r.fixed&&t.fixed,r.disableGutters&&t.disableGutters]}}),useThemeProps:e=>me({props:e,name:"MuiContainer"})}),f$=Km({createStyledComponent:j("div",{name:"MuiStack",slot:"Root"}),useThemeProps:e=>me({props:e,name:"MuiStack"})});function Th(e){return ce("MuiTypography",e)}const ns=ue("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]),Ih={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},Eh=Lg(),Mh=e=>{const{align:t,gutterBottom:r,noWrap:o,paragraph:n,variant:s,classes:i}=e,a={root:["root",s,e.align!=="inherit"&&`align${B(t)}`,r&&"gutterBottom",o&&"noWrap",n&&"paragraph"]};return de(a,Th,i)},Ah=j("span",{name:"MuiTypography",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.variant&&t[r.variant],r.align!=="inherit"&&t[`align${B(r.align)}`],r.noWrap&&t.noWrap,r.gutterBottom&&t.gutterBottom,r.paragraph&&t.paragraph]}})(re(({theme:e})=>{var t;return{margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(e.typography).filter(([r,o])=>r!=="inherit"&&o&&typeof o=="object").map(([r,o])=>({props:{variant:r},style:o})),...Object.entries(e.palette).filter(et()).map(([r])=>({props:{color:r},style:{color:(e.vars||e).palette[r].main}})),...Object.entries(((t=e.palette)==null?void 0:t.text)||{}).filter(([,r])=>typeof r=="string").map(([r])=>({props:{color:`text${B(r)}`},style:{color:(e.vars||e).palette.text[r]}})),{props:({ownerState:r})=>r.align!=="inherit",style:{textAlign:"var(--Typography-textAlign)"}},{props:({ownerState:r})=>r.noWrap,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:({ownerState:r})=>r.gutterBottom,style:{marginBottom:"0.35em"}},{props:({ownerState:r})=>r.paragraph,style:{marginBottom:16}}]}})),jl={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},Xt=p.forwardRef(function(t,r){const{color:o,...n}=me({props:t,name:"MuiTypography"}),s=!Ih[o],i=Eh({...n,...s&&{color:o}}),{align:a="inherit",className:l,component:c,gutterBottom:u=!1,noWrap:d=!1,paragraph:f=!1,variant:m="body1",variantMapping:h=jl,...b}=i,y={...i,align:a,color:o,className:l,component:c,gutterBottom:u,noWrap:d,paragraph:f,variant:m,variantMapping:h},S=c||(f?"p":h[m]||jl[m])||"span",$=Mh(y);return w.jsx(Ah,{as:S,ref:r,className:V($.root,l),...b,ownerState:y,style:{...a!=="inherit"&&{"--Typography-textAlign":a},...b.style}})}),Oh=ur(w.jsx("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}));function Lh(e){return ce("MuiAvatar",e)}const m$=ue("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);function yr(e){return typeof e=="string"}function ju(e,t,r){return e===void 0||yr(e)?t:{...t,ownerState:{...t.ownerState,...r}}}function zu(e,t,r){return typeof e=="function"?e(t,r):e}function en(e,t=[]){if(e===void 0)return{};const r={};return Object.keys(e).filter(o=>o.match(/^on[A-Z]/)&&typeof e[o]=="function"&&!t.includes(o)).forEach(o=>{r[o]=e[o]}),r}function zl(e){if(e===void 0)return{};const t={};return Object.keys(e).filter(r=>!(r.match(/^on[A-Z]/)&&typeof e[r]=="function")).forEach(r=>{t[r]=e[r]}),t}function Fu(e){const{getSlotProps:t,additionalProps:r,externalSlotProps:o,externalForwardedProps:n,className:s}=e;if(!t){const m=V(r==null?void 0:r.className,s,n==null?void 0:n.className,o==null?void 0:o.className),h={...r==null?void 0:r.style,...n==null?void 0:n.style,...o==null?void 0:o.style},b={...r,...n,...o};return m.length>0&&(b.className=m),Object.keys(h).length>0&&(b.style=h),{props:b,internalRef:void 0}}const i=en({...n,...o}),a=zl(o),l=zl(n),c=t(i),u=V(c==null?void 0:c.className,r==null?void 0:r.className,s,n==null?void 0:n.className,o==null?void 0:o.className),d={...c==null?void 0:c.style,...r==null?void 0:r.style,...n==null?void 0:n.style,...o==null?void 0:o.style},f={...c,...r,...l,...a};return u.length>0&&(f.className=u),Object.keys(d).length>0&&(f.style=d),{props:f,internalRef:c.ref}}function le(e,t){const{className:r,elementType:o,ownerState:n,externalForwardedProps:s,internalForwardedProps:i,shouldForwardComponentProp:a=!1,...l}=t,{component:c,slots:u={[e]:void 0},slotProps:d={[e]:void 0},...f}=s,m=u[e]||o,h=zu(d[e],n),{props:{component:b,...y},internalRef:S}=Fu({className:r,...l,externalForwardedProps:e==="root"?f:void 0,externalSlotProps:h}),$=ot(S,h==null?void 0:h.ref,t.ref),C=e==="root"?b||c:b,v=ju(m,{...e==="root"&&!c&&!u[e]&&i,...e!=="root"&&!u[e]&&i,...y,...C&&!a&&{as:C},...C&&a&&{component:C},ref:$},n);return[m,v]}const Bh=e=>{const{classes:t,variant:r,colorDefault:o}=e;return de({root:["root",r,o&&"colorDefault"],img:["img"],fallback:["fallback"]},Lh,t)},Nh=j("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})(re(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),jh=j("img",{name:"MuiAvatar",slot:"Img"})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),zh=j(Oh,{name:"MuiAvatar",slot:"Fallback"})({width:"75%",height:"75%"});function Fh({crossOrigin:e,referrerPolicy:t,src:r,srcSet:o}){const[n,s]=p.useState(!1);return p.useEffect(()=>{if(!r&&!o)return;s(!1);let i=!0;const a=new Image;return a.onload=()=>{i&&s("loaded")},a.onerror=()=>{i&&s("error")},a.crossOrigin=e,a.referrerPolicy=t,a.src=r,o&&(a.srcset=o),()=>{i=!1}},[e,t,r,o]),n}const g$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiAvatar"}),{alt:n,children:s,className:i,component:a="div",slots:l={},slotProps:c={},imgProps:u,sizes:d,src:f,srcSet:m,variant:h="circular",...b}=o;let y=null;const S={...o,component:a,variant:h},$=Fh({...u,...typeof c.img=="function"?c.img(S):c.img,src:f,srcSet:m}),C=f||m,v=C&&$!=="error";S.colorDefault=!v,delete S.ownerState;const x=Bh(S),[k,T]=le("root",{ref:r,className:V(x.root,i),elementType:Nh,externalForwardedProps:{slots:l,slotProps:c,component:a,...b},ownerState:S}),[I,A]=le("img",{className:x.img,elementType:jh,externalForwardedProps:{slots:l,slotProps:{img:{...u,...c.img}}},additionalProps:{alt:n,src:f,srcSet:m,sizes:d},ownerState:S}),[M,L]=le("fallback",{className:x.fallback,elementType:zh,externalForwardedProps:{slots:l,slotProps:c},shouldForwardComponentProp:!0,ownerState:S});return v?y=w.jsx(I,{...A}):s||s===0?y=s:C&&n?y=n[0]:y=w.jsx(M,{...L}),w.jsx(k,{...T,children:y})}),Sa=e=>{const t=p.useRef({});return p.useEffect(()=>{t.current=e}),t.current};function Dh(e){const{badgeContent:t,invisible:r=!1,max:o=99,showZero:n=!1}=e,s=Sa({badgeContent:t,max:o});let i=r;r===!1&&t===0&&!n&&(i=!0);const{badgeContent:a,max:l=o}=i?s:e,c=a&&Number(a)>l?`${l}+`:a;return{badgeContent:a,invisible:i,max:l,displayValue:c}}function Wh(e){return ce("MuiBadge",e)}const zr=ue("MuiBadge",["root","badge","dot","standard","anchorOriginTopRight","anchorOriginBottomRight","anchorOriginTopLeft","anchorOriginBottomLeft","invisible","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","overlapRectangular","overlapCircular","anchorOriginTopLeftCircular","anchorOriginTopLeftRectangular","anchorOriginTopRightCircular","anchorOriginTopRightRectangular","anchorOriginBottomLeftCircular","anchorOriginBottomLeftRectangular","anchorOriginBottomRightCircular","anchorOriginBottomRightRectangular"]),ui=10,di=4,Uh=e=>{const{color:t,anchorOrigin:r,invisible:o,overlap:n,variant:s,classes:i={}}=e,a={root:["root"],badge:["badge",s,o&&"invisible",`anchorOrigin${B(r.vertical)}${B(r.horizontal)}`,`anchorOrigin${B(r.vertical)}${B(r.horizontal)}${B(n)}`,`overlap${B(n)}`,t!=="default"&&`color${B(t)}`]};return de(a,Wh,i)},Hh=j("span",{name:"MuiBadge",slot:"Root"})({position:"relative",display:"inline-flex",verticalAlign:"middle",flexShrink:0}),Vh=j("span",{name:"MuiBadge",slot:"Badge",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.badge,t[r.variant],t[`anchorOrigin${B(r.anchorOrigin.vertical)}${B(r.anchorOrigin.horizontal)}${B(r.overlap)}`],r.color!=="default"&&t[`color${B(r.color)}`],r.invisible&&t.invisible]}})(re(({theme:e})=>({display:"flex",flexDirection:"row",flexWrap:"wrap",justifyContent:"center",alignContent:"center",alignItems:"center",position:"absolute",boxSizing:"border-box",fontFamily:e.typography.fontFamily,fontWeight:e.typography.fontWeightMedium,fontSize:e.typography.pxToRem(12),minWidth:ui*2,lineHeight:1,padding:"0 6px",height:ui*2,borderRadius:ui,zIndex:1,transition:e.transitions.create("transform",{easing:e.transitions.easing.easeInOut,duration:e.transitions.duration.enteringScreen}),variants:[...Object.entries(e.palette).filter(et(["contrastText"])).map(([t])=>({props:{color:t},style:{backgroundColor:(e.vars||e).palette[t].main,color:(e.vars||e).palette[t].contrastText}})),{props:{variant:"dot"},style:{borderRadius:di,height:di*2,minWidth:di*2,padding:0}},{props:({ownerState:t})=>t.anchorOrigin.vertical==="top"&&t.anchorOrigin.horizontal==="right"&&t.overlap==="rectangular",style:{top:0,right:0,transform:"scale(1) translate(50%, -50%)",transformOrigin:"100% 0%",[`&.${zr.invisible}`]:{transform:"scale(0) translate(50%, -50%)"}}},{props:({ownerState:t})=>t.anchorOrigin.vertical==="bottom"&&t.anchorOrigin.horizontal==="right"&&t.overlap==="rectangular",style:{bottom:0,right:0,transform:"scale(1) translate(50%, 50%)",transformOrigin:"100% 100%",[`&.${zr.invisible}`]:{transform:"scale(0) translate(50%, 50%)"}}},{props:({ownerState:t})=>t.anchorOrigin.vertical==="top"&&t.anchorOrigin.horizontal==="left"&&t.overlap==="rectangular",style:{top:0,left:0,transform:"scale(1) translate(-50%, -50%)",transformOrigin:"0% 0%",[`&.${zr.invisible}`]:{transform:"scale(0) translate(-50%, -50%)"}}},{props:({ownerState:t})=>t.anchorOrigin.vertical==="bottom"&&t.anchorOrigin.horizontal==="left"&&t.overlap==="rectangular",style:{bottom:0,left:0,transform:"scale(1) translate(-50%, 50%)",transformOrigin:"0% 100%",[`&.${zr.invisible}`]:{transform:"scale(0) translate(-50%, 50%)"}}},{props:({ownerState:t})=>t.anchorOrigin.vertical==="top"&&t.anchorOrigin.horizontal==="right"&&t.overlap==="circular",style:{top:"14%",right:"14%",transform:"scale(1) translate(50%, -50%)",transformOrigin:"100% 0%",[`&.${zr.invisible}`]:{transform:"scale(0) translate(50%, -50%)"}}},{props:({ownerState:t})=>t.anchorOrigin.vertical==="bottom"&&t.anchorOrigin.horizontal==="right"&&t.overlap==="circular",style:{bottom:"14%",right:"14%",transform:"scale(1) translate(50%, 50%)",transformOrigin:"100% 100%",[`&.${zr.invisible}`]:{transform:"scale(0) translate(50%, 50%)"}}},{props:({ownerState:t})=>t.anchorOrigin.vertical==="top"&&t.anchorOrigin.horizontal==="left"&&t.overlap==="circular",style:{top:"14%",left:"14%",transform:"scale(1) translate(-50%, -50%)",transformOrigin:"0% 0%",[`&.${zr.invisible}`]:{transform:"scale(0) translate(-50%, -50%)"}}},{props:({ownerState:t})=>t.anchorOrigin.vertical==="bottom"&&t.anchorOrigin.horizontal==="left"&&t.overlap==="circular",style:{bottom:"14%",left:"14%",transform:"scale(1) translate(-50%, 50%)",transformOrigin:"0% 100%",[`&.${zr.invisible}`]:{transform:"scale(0) translate(-50%, 50%)"}}},{props:{invisible:!0},style:{transition:e.transitions.create("transform",{easing:e.transitions.easing.easeInOut,duration:e.transitions.duration.leavingScreen})}}]})));function Fl(e){return{vertical:(e==null?void 0:e.vertical)??"top",horizontal:(e==null?void 0:e.horizontal)??"right"}}const h$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiBadge"}),{anchorOrigin:n,className:s,classes:i,component:a,components:l={},componentsProps:c={},children:u,overlap:d="rectangular",color:f="default",invisible:m=!1,max:h=99,badgeContent:b,slots:y,slotProps:S,showZero:$=!1,variant:C="standard",...v}=o,{badgeContent:x,invisible:k,max:T,displayValue:I}=Dh({max:h,invisible:m,badgeContent:b,showZero:$}),A=Sa({anchorOrigin:Fl(n),color:f,overlap:d,variant:C,badgeContent:b}),M=k||x==null&&C!=="dot",{color:L=f,overlap:g=d,anchorOrigin:P,variant:R=C}=M?A:o,O=Fl(P),E=R!=="dot"?I:void 0,F={...o,badgeContent:x,invisible:M,max:T,displayValue:E,showZero:$,anchorOrigin:O,color:L,overlap:g,variant:R},z=Uh(F),N={slots:{root:(y==null?void 0:y.root)??l.Root,badge:(y==null?void 0:y.badge)??l.Badge},slotProps:{root:(S==null?void 0:S.root)??c.root,badge:(S==null?void 0:S.badge)??c.badge}},[Q,_]=le("root",{elementType:Hh,externalForwardedProps:{...N,...v},ownerState:F,className:V(z.root,s),ref:r,additionalProps:{as:a}}),[Z,se]=le("badge",{elementType:Vh,externalForwardedProps:N,ownerState:F,className:z.badge});return w.jsxs(Q,{..._,children:[u,w.jsx(Z,{...se,children:E})]})}),_h=ur(w.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}));function Gh(e){return ce("MuiChip",e)}const Ue=ue("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),Kh=e=>{const{classes:t,disabled:r,size:o,color:n,iconColor:s,onDelete:i,clickable:a,variant:l}=e,c={root:["root",l,r&&"disabled",`size${B(o)}`,`color${B(n)}`,a&&"clickable",a&&`clickableColor${B(n)}`,i&&"deletable",i&&`deletableColor${B(n)}`,`${l}${B(n)}`],label:["label",`label${B(o)}`],avatar:["avatar",`avatar${B(o)}`,`avatarColor${B(n)}`],icon:["icon",`icon${B(o)}`,`iconColor${B(s)}`],deleteIcon:["deleteIcon",`deleteIcon${B(o)}`,`deleteIconColor${B(n)}`,`deleteIcon${B(l)}Color${B(n)}`]};return de(c,Gh,t)},qh=j("div",{name:"MuiChip",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e,{color:o,iconColor:n,clickable:s,onDelete:i,size:a,variant:l}=r;return[{[`& .${Ue.avatar}`]:t.avatar},{[`& .${Ue.avatar}`]:t[`avatar${B(a)}`]},{[`& .${Ue.avatar}`]:t[`avatarColor${B(o)}`]},{[`& .${Ue.icon}`]:t.icon},{[`& .${Ue.icon}`]:t[`icon${B(a)}`]},{[`& .${Ue.icon}`]:t[`iconColor${B(n)}`]},{[`& .${Ue.deleteIcon}`]:t.deleteIcon},{[`& .${Ue.deleteIcon}`]:t[`deleteIcon${B(a)}`]},{[`& .${Ue.deleteIcon}`]:t[`deleteIconColor${B(o)}`]},{[`& .${Ue.deleteIcon}`]:t[`deleteIcon${B(l)}Color${B(o)}`]},t.root,t[`size${B(a)}`],t[`color${B(o)}`],s&&t.clickable,s&&o!=="default"&&t[`clickableColor${B(o)})`],i&&t.deletable,i&&o!=="default"&&t[`deletableColor${B(o)}`],t[l],t[`${l}${B(o)}`]]}})(re(({theme:e})=>{const t=e.palette.mode==="light"?e.palette.grey[700]:e.palette.grey[300];return{maxWidth:"100%",fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,lineHeight:1.5,color:(e.vars||e).palette.text.primary,backgroundColor:(e.vars||e).palette.action.selected,borderRadius:32/2,whiteSpace:"nowrap",transition:e.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${Ue.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${Ue.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:e.vars?e.vars.palette.Chip.defaultAvatarColor:t,fontSize:e.typography.pxToRem(12)},[`& .${Ue.avatarColorPrimary}`]:{color:(e.vars||e).palette.primary.contrastText,backgroundColor:(e.vars||e).palette.primary.dark},[`& .${Ue.avatarColorSecondary}`]:{color:(e.vars||e).palette.secondary.contrastText,backgroundColor:(e.vars||e).palette.secondary.dark},[`& .${Ue.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:e.typography.pxToRem(10)},[`& .${Ue.icon}`]:{marginLeft:5,marginRight:-6},[`& .${Ue.deleteIcon}`]:{WebkitTapHighlightColor:"transparent",color:e.alpha((e.vars||e).palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:e.alpha((e.vars||e).palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,[`& .${Ue.icon}`]:{fontSize:18,marginLeft:4,marginRight:-4},[`& .${Ue.deleteIcon}`]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(e.palette).filter(et(["contrastText"])).map(([r])=>({props:{color:r},style:{backgroundColor:(e.vars||e).palette[r].main,color:(e.vars||e).palette[r].contrastText,[`& .${Ue.deleteIcon}`]:{color:e.alpha((e.vars||e).palette[r].contrastText,.7),"&:hover, &:active":{color:(e.vars||e).palette[r].contrastText}}}})),{props:r=>r.iconColor===r.color,style:{[`& .${Ue.icon}`]:{color:e.vars?e.vars.palette.Chip.defaultIconColor:t}}},{props:r=>r.iconColor===r.color&&r.color!=="default",style:{[`& .${Ue.icon}`]:{color:"inherit"}}},{props:{onDelete:!0},style:{[`&.${Ue.focusVisible}`]:{backgroundColor:e.alpha((e.vars||e).palette.action.selected,`${(e.vars||e).palette.action.selectedOpacity} + ${(e.vars||e).palette.action.focusOpacity}`)}}},...Object.entries(e.palette).filter(et(["dark"])).map(([r])=>({props:{color:r,onDelete:!0},style:{[`&.${Ue.focusVisible}`]:{background:(e.vars||e).palette[r].dark}}})),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:e.alpha((e.vars||e).palette.action.selected,`${(e.vars||e).palette.action.selectedOpacity} + ${(e.vars||e).palette.action.hoverOpacity}`)},[`&.${Ue.focusVisible}`]:{backgroundColor:e.alpha((e.vars||e).palette.action.selected,`${(e.vars||e).palette.action.selectedOpacity} + ${(e.vars||e).palette.action.focusOpacity}`)},"&:active":{boxShadow:(e.vars||e).shadows[1]}}},...Object.entries(e.palette).filter(et(["dark"])).map(([r])=>({props:{color:r,clickable:!0},style:{[`&:hover, &.${Ue.focusVisible}`]:{backgroundColor:(e.vars||e).palette[r].dark}}})),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:e.vars?`1px solid ${e.vars.palette.Chip.defaultBorder}`:`1px solid ${e.palette.mode==="light"?e.palette.grey[400]:e.palette.grey[700]}`,[`&.${Ue.clickable}:hover`]:{backgroundColor:(e.vars||e).palette.action.hover},[`&.${Ue.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`& .${Ue.avatar}`]:{marginLeft:4},[`& .${Ue.avatarSmall}`]:{marginLeft:2},[`& .${Ue.icon}`]:{marginLeft:4},[`& .${Ue.iconSmall}`]:{marginLeft:2},[`& .${Ue.deleteIcon}`]:{marginRight:5},[`& .${Ue.deleteIconSmall}`]:{marginRight:3}}},...Object.entries(e.palette).filter(et()).map(([r])=>({props:{variant:"outlined",color:r},style:{color:(e.vars||e).palette[r].main,border:`1px solid ${e.alpha((e.vars||e).palette[r].main,.7)}`,[`&.${Ue.clickable}:hover`]:{backgroundColor:e.alpha((e.vars||e).palette[r].main,(e.vars||e).palette.action.hoverOpacity)},[`&.${Ue.focusVisible}`]:{backgroundColor:e.alpha((e.vars||e).palette[r].main,(e.vars||e).palette.action.focusOpacity)},[`& .${Ue.deleteIcon}`]:{color:e.alpha((e.vars||e).palette[r].main,.7),"&:hover, &:active":{color:(e.vars||e).palette[r].main}}}}))]}})),Yh=j("span",{name:"MuiChip",slot:"Label",overridesResolver:(e,t)=>{const{ownerState:r}=e,{size:o}=r;return[t.label,t[`label${B(o)}`]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function Dl(e){return e.key==="Backspace"||e.key==="Delete"}const Xh=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiChip"}),{avatar:n,className:s,clickable:i,color:a="default",component:l,deleteIcon:c,disabled:u=!1,icon:d,label:f,onClick:m,onDelete:h,onKeyDown:b,onKeyUp:y,size:S="medium",variant:$="filled",tabIndex:C,skipFocusWhenDisabled:v=!1,slots:x={},slotProps:k={},...T}=o,I=p.useRef(null),A=ot(I,r),M=q=>{q.stopPropagation(),h&&h(q)},L=q=>{q.currentTarget===q.target&&Dl(q)&&q.preventDefault(),b&&b(q)},g=q=>{q.currentTarget===q.target&&h&&Dl(q)&&h(q),y&&y(q)},P=i!==!1&&m?!0:i,R=P||h?Sr:l||"div",O={...o,component:R,disabled:u,size:S,color:a,iconColor:p.isValidElement(d)&&d.props.color||a,onDelete:!!h,clickable:P,variant:$},E=Kh(O),F=R===Sr?{component:l||"div",focusVisibleClassName:E.focusVisible,...h&&{disableRipple:!0}}:{};let z=null;h&&(z=c&&p.isValidElement(c)?p.cloneElement(c,{className:V(c.props.className,E.deleteIcon),onClick:M}):w.jsx(_h,{className:E.deleteIcon,onClick:M}));let N=null;n&&p.isValidElement(n)&&(N=p.cloneElement(n,{className:V(E.avatar,n.props.className)}));let Q=null;d&&p.isValidElement(d)&&(Q=p.cloneElement(d,{className:V(E.icon,d.props.className)}));const _={slots:x,slotProps:k},[Z,se]=le("root",{elementType:qh,externalForwardedProps:{..._,...T},ownerState:O,shouldForwardComponentProp:!0,ref:A,className:V(E.root,s),additionalProps:{disabled:P&&u?!0:void 0,tabIndex:v&&u?-1:C,...F},getSlotProps:q=>({...q,onClick:ie=>{var we;(we=q.onClick)==null||we.call(q,ie),m==null||m(ie)},onKeyDown:ie=>{var we;(we=q.onKeyDown)==null||we.call(q,ie),L(ie)},onKeyUp:ie=>{var we;(we=q.onKeyUp)==null||we.call(q,ie),g(ie)}})}),[ye,K]=le("label",{elementType:Yh,externalForwardedProps:_,ownerState:O,className:E.label});return w.jsxs(Z,{as:R,...se,children:[N||Q,w.jsx(ye,{...K,children:f}),z]})});function Qh(e){return ce("MuiDivider",e)}const Wl=ue("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]),Zh=e=>{const{absolute:t,children:r,classes:o,flexItem:n,light:s,orientation:i,textAlign:a,variant:l}=e;return de({root:["root",t&&"absolute",l,s&&"light",i==="vertical"&&"vertical",n&&"flexItem",r&&"withChildren",r&&i==="vertical"&&"withChildrenVertical",a==="right"&&i!=="vertical"&&"textAlignRight",a==="left"&&i!=="vertical"&&"textAlignLeft"],wrapper:["wrapper",i==="vertical"&&"wrapperVertical"]},Qh,o)},Jh=j("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.absolute&&t.absolute,t[r.variant],r.light&&t.light,r.orientation==="vertical"&&t.vertical,r.flexItem&&t.flexItem,r.children&&t.withChildren,r.children&&r.orientation==="vertical"&&t.withChildrenVertical,r.textAlign==="right"&&r.orientation!=="vertical"&&t.textAlignRight,r.textAlign==="left"&&r.orientation!=="vertical"&&t.textAlignLeft]}})(re(({theme:e})=>({margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(e.vars||e).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:e.alpha((e.vars||e).palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:e.spacing(2),marginRight:e.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:e.spacing(1),marginBottom:e.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:({ownerState:t})=>!!t.children,style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:({ownerState:t})=>t.children&&t.orientation!=="vertical",style:{"&::before, &::after":{width:"100%",borderTop:`thin solid ${(e.vars||e).palette.divider}`,borderTopStyle:"inherit"}}},{props:({ownerState:t})=>t.orientation==="vertical"&&t.children,style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:`thin solid ${(e.vars||e).palette.divider}`,borderLeftStyle:"inherit"}}},{props:({ownerState:t})=>t.textAlign==="right"&&t.orientation!=="vertical",style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:({ownerState:t})=>t.textAlign==="left"&&t.orientation!=="vertical",style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]}))),eb=j("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.wrapper,r.orientation==="vertical"&&t.wrapperVertical]}})(re(({theme:e})=>({display:"inline-block",paddingLeft:`calc(${e.spacing(1)} * 1.2)`,paddingRight:`calc(${e.spacing(1)} * 1.2)`,whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:`calc(${e.spacing(1)} * 1.2)`,paddingBottom:`calc(${e.spacing(1)} * 1.2)`}}]}))),Ul=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiDivider"}),{absolute:n=!1,children:s,className:i,orientation:a="horizontal",component:l=s||a==="vertical"?"div":"hr",flexItem:c=!1,light:u=!1,role:d=l!=="hr"?"separator":void 0,textAlign:f="center",variant:m="fullWidth",...h}=o,b={...o,absolute:n,component:l,flexItem:c,light:u,orientation:a,role:d,textAlign:f,variant:m},y=Zh(b);return w.jsx(Jh,{as:l,className:V(y.root,i),role:d,ref:r,ownerState:b,"aria-orientation":d==="separator"&&(l!=="hr"||a==="vertical")?a:void 0,...h,children:s?w.jsx(eb,{className:y.wrapper,ownerState:b,children:s}):null})});Ul&&(Ul.muiSkipListHighlight=!0);function tb(e){return ce("MuiIconButton",e)}const Hl=ue("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]),rb=e=>{const{classes:t,disabled:r,color:o,edge:n,size:s,loading:i}=e,a={root:["root",i&&"loading",r&&"disabled",o!=="default"&&`color${B(o)}`,n&&`edge${B(n)}`,`size${B(s)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return de(a,tb,t)},ob=j(Sr,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.loading&&t.loading,r.color!=="default"&&t[`color${B(r.color)}`],r.edge&&t[`edge${B(r.edge)}`],t[`size${B(r.size)}`]]}})(re(({theme:e})=>({textAlign:"center",flex:"0 0 auto",fontSize:e.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(e.vars||e).palette.action.active,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest}),variants:[{props:t=>!t.disableRipple,style:{"--IconButton-hoverBg":e.alpha((e.vars||e).palette.action.active,(e.vars||e).palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),re(({theme:e})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}})),...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t},style:{"--IconButton-hoverBg":e.alpha((e.vars||e).palette[t].main,(e.vars||e).palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:e.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:e.typography.pxToRem(28)}}],[`&.${Hl.disabled}`]:{backgroundColor:"transparent",color:(e.vars||e).palette.action.disabled},[`&.${Hl.loading}`]:{color:"transparent"}}))),nb=j("span",{name:"MuiIconButton",slot:"LoadingIndicator"})(({theme:e})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(e.vars||e).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),xo=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiIconButton"}),{edge:n=!1,children:s,className:i,color:a="default",disabled:l=!1,disableFocusRipple:c=!1,size:u="medium",id:d,loading:f=null,loadingIndicator:m,...h}=o,b=xr(d),y=m??w.jsx(Bu,{"aria-labelledby":b,color:"inherit",size:16}),S={...o,edge:n,color:a,disabled:l,disableFocusRipple:c,loading:f,loadingIndicator:y,size:u},$=rb(S);return w.jsxs(ob,{id:f?b:d,className:V($.root,i),centerRipple:!0,focusRipple:!c,disabled:l||f,ref:r,...h,ownerState:S,children:[typeof f=="boolean"&&w.jsx("span",{className:$.loadingWrapper,style:{display:"contents"},children:w.jsx(nb,{className:$.loadingIndicator,ownerState:S,children:f&&y})}),s]})});function Gr(e){var t;return parseInt(p.version,10)>=19?((t=e==null?void 0:e.props)==null?void 0:t.ref)||null:(e==null?void 0:e.ref)||null}const Ca=e=>e.scrollTop;function To(e,t){const{timeout:r,easing:o,style:n={}}=e;return{duration:n.transitionDuration??(typeof r=="number"?r:r[t.mode]||0),easing:n.transitionTimingFunction??(typeof o=="object"?o[t.mode]:o),delay:n.transitionDelay}}function Ni(e){return`scale(${e}, ${e**2})`}const sb={entering:{opacity:1,transform:Ni(1)},entered:{opacity:1,transform:"none"}},pi=typeof navigator<"u"&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),ss=p.forwardRef(function(t,r){const{addEndListener:o,appear:n=!0,children:s,easing:i,in:a,onEnter:l,onEntered:c,onEntering:u,onExit:d,onExited:f,onExiting:m,style:h,timeout:b="auto",TransitionComponent:y=nr,...S}=t,$=vo(),C=p.useRef(),v=$r(),x=p.useRef(null),k=ot(x,Gr(s),r),T=O=>E=>{if(O){const F=x.current;E===void 0?O(F):O(F,E)}},I=T(u),A=T((O,E)=>{Ca(O);const{duration:F,delay:z,easing:N}=To({style:h,timeout:b,easing:i},{mode:"enter"});let Q;b==="auto"?(Q=v.transitions.getAutoHeightDuration(O.clientHeight),C.current=Q):Q=F,O.style.transition=[v.transitions.create("opacity",{duration:Q,delay:z}),v.transitions.create("transform",{duration:pi?Q:Q*.666,delay:z,easing:N})].join(","),l&&l(O,E)}),M=T(c),L=T(m),g=T(O=>{const{duration:E,delay:F,easing:z}=To({style:h,timeout:b,easing:i},{mode:"exit"});let N;b==="auto"?(N=v.transitions.getAutoHeightDuration(O.clientHeight),C.current=N):N=E,O.style.transition=[v.transitions.create("opacity",{duration:N,delay:F}),v.transitions.create("transform",{duration:pi?N:N*.666,delay:pi?F:F||N*.333,easing:z})].join(","),O.style.opacity=0,O.style.transform=Ni(.75),d&&d(O)}),P=T(f),R=O=>{b==="auto"&&$.start(C.current||0,O),o&&o(x.current,O)};return w.jsx(y,{appear:n,in:a,nodeRef:x,onEnter:A,onEntered:M,onEntering:I,onExit:g,onExited:P,onExiting:L,addEndListener:R,timeout:b==="auto"?null:b,...S,children:(O,{ownerState:E,...F})=>p.cloneElement(s,{style:{opacity:0,transform:Ni(.75),visibility:O==="exited"&&!a?"hidden":void 0,...sb[O],...h,...s.props.style},ref:k,...F})})});ss&&(ss.muiSupportAuto=!0);var Lt="top",tr="bottom",rr="right",Bt="left",wa="auto",wn=[Lt,tr,rr,Bt],Io="start",gn="end",ib="clippingParents",Du="viewport",Ho="popper",ab="reference",Vl=wn.reduce(function(e,t){return e.concat([t+"-"+Io,t+"-"+gn])},[]),Wu=[].concat(wn,[wa]).reduce(function(e,t){return e.concat([t,t+"-"+Io,t+"-"+gn])},[]),lb="beforeRead",cb="read",ub="afterRead",db="beforeMain",pb="main",fb="afterMain",mb="beforeWrite",gb="write",hb="afterWrite",bb=[lb,cb,ub,db,pb,fb,mb,gb,hb];function Cr(e){return e?(e.nodeName||"").toLowerCase():null}function Ht(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function oo(e){var t=Ht(e).Element;return e instanceof t||e instanceof Element}function Jt(e){var t=Ht(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function $a(e){if(typeof ShadowRoot>"u")return!1;var t=Ht(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function yb(e){var t=e.state;Object.keys(t.elements).forEach(function(r){var o=t.styles[r]||{},n=t.attributes[r]||{},s=t.elements[r];!Jt(s)||!Cr(s)||(Object.assign(s.style,o),Object.keys(n).forEach(function(i){var a=n[i];a===!1?s.removeAttribute(i):s.setAttribute(i,a===!0?"":a)}))})}function vb(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow),function(){Object.keys(t.elements).forEach(function(o){var n=t.elements[o],s=t.attributes[o]||{},i=Object.keys(t.styles.hasOwnProperty(o)?t.styles[o]:r[o]),a=i.reduce(function(l,c){return l[c]="",l},{});!Jt(n)||!Cr(n)||(Object.assign(n.style,a),Object.keys(s).forEach(function(l){n.removeAttribute(l)}))})}}const xb={name:"applyStyles",enabled:!0,phase:"write",fn:yb,effect:vb,requires:["computeStyles"]};function vr(e){return e.split("-")[0]}var to=Math.max,is=Math.min,Eo=Math.round;function ji(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function Uu(){return!/^((?!chrome|android).)*safari/i.test(ji())}function Mo(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!1);var o=e.getBoundingClientRect(),n=1,s=1;t&&Jt(e)&&(n=e.offsetWidth>0&&Eo(o.width)/e.offsetWidth||1,s=e.offsetHeight>0&&Eo(o.height)/e.offsetHeight||1);var i=oo(e)?Ht(e):window,a=i.visualViewport,l=!Uu()&&r,c=(o.left+(l&&a?a.offsetLeft:0))/n,u=(o.top+(l&&a?a.offsetTop:0))/s,d=o.width/n,f=o.height/s;return{width:d,height:f,top:u,right:c+d,bottom:u+f,left:c,x:c,y:u}}function ka(e){var t=Mo(e),r=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-r)<=1&&(r=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:r,height:o}}function Hu(e,t){var r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&$a(r)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function Er(e){return Ht(e).getComputedStyle(e)}function Sb(e){return["table","td","th"].indexOf(Cr(e))>=0}function Kr(e){return((oo(e)?e.ownerDocument:e.document)||window.document).documentElement}function As(e){return Cr(e)==="html"?e:e.assignedSlot||e.parentNode||($a(e)?e.host:null)||Kr(e)}function _l(e){return!Jt(e)||Er(e).position==="fixed"?null:e.offsetParent}function Cb(e){var t=/firefox/i.test(ji()),r=/Trident/i.test(ji());if(r&&Jt(e)){var o=Er(e);if(o.position==="fixed")return null}var n=As(e);for($a(n)&&(n=n.host);Jt(n)&&["html","body"].indexOf(Cr(n))<0;){var s=Er(n);if(s.transform!=="none"||s.perspective!=="none"||s.contain==="paint"||["transform","perspective"].indexOf(s.willChange)!==-1||t&&s.willChange==="filter"||t&&s.filter&&s.filter!=="none")return n;n=n.parentNode}return null}function $n(e){for(var t=Ht(e),r=_l(e);r&&Sb(r)&&Er(r).position==="static";)r=_l(r);return r&&(Cr(r)==="html"||Cr(r)==="body"&&Er(r).position==="static")?t:r||Cb(e)||t}function Ra(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function tn(e,t,r){return to(e,is(t,r))}function wb(e,t,r){var o=tn(e,t,r);return o>r?r:o}function Vu(){return{top:0,right:0,bottom:0,left:0}}function _u(e){return Object.assign({},Vu(),e)}function Gu(e,t){return t.reduce(function(r,o){return r[o]=e,r},{})}var $b=function(t,r){return t=typeof t=="function"?t(Object.assign({},r.rects,{placement:r.placement})):t,_u(typeof t!="number"?t:Gu(t,wn))};function kb(e){var t,r=e.state,o=e.name,n=e.options,s=r.elements.arrow,i=r.modifiersData.popperOffsets,a=vr(r.placement),l=Ra(a),c=[Bt,rr].indexOf(a)>=0,u=c?"height":"width";if(!(!s||!i)){var d=$b(n.padding,r),f=ka(s),m=l==="y"?Lt:Bt,h=l==="y"?tr:rr,b=r.rects.reference[u]+r.rects.reference[l]-i[l]-r.rects.popper[u],y=i[l]-r.rects.reference[l],S=$n(s),$=S?l==="y"?S.clientHeight||0:S.clientWidth||0:0,C=b/2-y/2,v=d[m],x=$-f[u]-d[h],k=$/2-f[u]/2+C,T=tn(v,k,x),I=l;r.modifiersData[o]=(t={},t[I]=T,t.centerOffset=T-k,t)}}function Rb(e){var t=e.state,r=e.options,o=r.element,n=o===void 0?"[data-popper-arrow]":o;n!=null&&(typeof n=="string"&&(n=t.elements.popper.querySelector(n),!n)||Hu(t.elements.popper,n)&&(t.elements.arrow=n))}const Pb={name:"arrow",enabled:!0,phase:"main",fn:kb,effect:Rb,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ao(e){return e.split("-")[1]}var Tb={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ib(e,t){var r=e.x,o=e.y,n=t.devicePixelRatio||1;return{x:Eo(r*n)/n||0,y:Eo(o*n)/n||0}}function Gl(e){var t,r=e.popper,o=e.popperRect,n=e.placement,s=e.variation,i=e.offsets,a=e.position,l=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,d=e.isFixed,f=i.x,m=f===void 0?0:f,h=i.y,b=h===void 0?0:h,y=typeof u=="function"?u({x:m,y:b}):{x:m,y:b};m=y.x,b=y.y;var S=i.hasOwnProperty("x"),$=i.hasOwnProperty("y"),C=Bt,v=Lt,x=window;if(c){var k=$n(r),T="clientHeight",I="clientWidth";if(k===Ht(r)&&(k=Kr(r),Er(k).position!=="static"&&a==="absolute"&&(T="scrollHeight",I="scrollWidth")),k=k,n===Lt||(n===Bt||n===rr)&&s===gn){v=tr;var A=d&&k===x&&x.visualViewport?x.visualViewport.height:k[T];b-=A-o.height,b*=l?1:-1}if(n===Bt||(n===Lt||n===tr)&&s===gn){C=rr;var M=d&&k===x&&x.visualViewport?x.visualViewport.width:k[I];m-=M-o.width,m*=l?1:-1}}var L=Object.assign({position:a},c&&Tb),g=u===!0?Ib({x:m,y:b},Ht(r)):{x:m,y:b};if(m=g.x,b=g.y,l){var P;return Object.assign({},L,(P={},P[v]=$?"0":"",P[C]=S?"0":"",P.transform=(x.devicePixelRatio||1)<=1?"translate("+m+"px, "+b+"px)":"translate3d("+m+"px, "+b+"px, 0)",P))}return Object.assign({},L,(t={},t[v]=$?b+"px":"",t[C]=S?m+"px":"",t.transform="",t))}function Eb(e){var t=e.state,r=e.options,o=r.gpuAcceleration,n=o===void 0?!0:o,s=r.adaptive,i=s===void 0?!0:s,a=r.roundOffsets,l=a===void 0?!0:a,c={placement:vr(t.placement),variation:Ao(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:n,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Gl(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:l})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Gl(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const Mb={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Eb,data:{}};var Ln={passive:!0};function Ab(e){var t=e.state,r=e.instance,o=e.options,n=o.scroll,s=n===void 0?!0:n,i=o.resize,a=i===void 0?!0:i,l=Ht(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return s&&c.forEach(function(u){u.addEventListener("scroll",r.update,Ln)}),a&&l.addEventListener("resize",r.update,Ln),function(){s&&c.forEach(function(u){u.removeEventListener("scroll",r.update,Ln)}),a&&l.removeEventListener("resize",r.update,Ln)}}const Ob={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Ab,data:{}};var Lb={left:"right",right:"left",bottom:"top",top:"bottom"};function Xn(e){return e.replace(/left|right|bottom|top/g,function(t){return Lb[t]})}var Bb={start:"end",end:"start"};function Kl(e){return e.replace(/start|end/g,function(t){return Bb[t]})}function Pa(e){var t=Ht(e),r=t.pageXOffset,o=t.pageYOffset;return{scrollLeft:r,scrollTop:o}}function Ta(e){return Mo(Kr(e)).left+Pa(e).scrollLeft}function Nb(e,t){var r=Ht(e),o=Kr(e),n=r.visualViewport,s=o.clientWidth,i=o.clientHeight,a=0,l=0;if(n){s=n.width,i=n.height;var c=Uu();(c||!c&&t==="fixed")&&(a=n.offsetLeft,l=n.offsetTop)}return{width:s,height:i,x:a+Ta(e),y:l}}function jb(e){var t,r=Kr(e),o=Pa(e),n=(t=e.ownerDocument)==null?void 0:t.body,s=to(r.scrollWidth,r.clientWidth,n?n.scrollWidth:0,n?n.clientWidth:0),i=to(r.scrollHeight,r.clientHeight,n?n.scrollHeight:0,n?n.clientHeight:0),a=-o.scrollLeft+Ta(e),l=-o.scrollTop;return Er(n||r).direction==="rtl"&&(a+=to(r.clientWidth,n?n.clientWidth:0)-s),{width:s,height:i,x:a,y:l}}function Ia(e){var t=Er(e),r=t.overflow,o=t.overflowX,n=t.overflowY;return/auto|scroll|overlay|hidden/.test(r+n+o)}function Ku(e){return["html","body","#document"].indexOf(Cr(e))>=0?e.ownerDocument.body:Jt(e)&&Ia(e)?e:Ku(As(e))}function rn(e,t){var r;t===void 0&&(t=[]);var o=Ku(e),n=o===((r=e.ownerDocument)==null?void 0:r.body),s=Ht(o),i=n?[s].concat(s.visualViewport||[],Ia(o)?o:[]):o,a=t.concat(i);return n?a:a.concat(rn(As(i)))}function zi(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function zb(e,t){var r=Mo(e,!1,t==="fixed");return r.top=r.top+e.clientTop,r.left=r.left+e.clientLeft,r.bottom=r.top+e.clientHeight,r.right=r.left+e.clientWidth,r.width=e.clientWidth,r.height=e.clientHeight,r.x=r.left,r.y=r.top,r}function ql(e,t,r){return t===Du?zi(Nb(e,r)):oo(t)?zb(t,r):zi(jb(Kr(e)))}function Fb(e){var t=rn(As(e)),r=["absolute","fixed"].indexOf(Er(e).position)>=0,o=r&&Jt(e)?$n(e):e;return oo(o)?t.filter(function(n){return oo(n)&&Hu(n,o)&&Cr(n)!=="body"}):[]}function Db(e,t,r,o){var n=t==="clippingParents"?Fb(e):[].concat(t),s=[].concat(n,[r]),i=s[0],a=s.reduce(function(l,c){var u=ql(e,c,o);return l.top=to(u.top,l.top),l.right=is(u.right,l.right),l.bottom=is(u.bottom,l.bottom),l.left=to(u.left,l.left),l},ql(e,i,o));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function qu(e){var t=e.reference,r=e.element,o=e.placement,n=o?vr(o):null,s=o?Ao(o):null,i=t.x+t.width/2-r.width/2,a=t.y+t.height/2-r.height/2,l;switch(n){case Lt:l={x:i,y:t.y-r.height};break;case tr:l={x:i,y:t.y+t.height};break;case rr:l={x:t.x+t.width,y:a};break;case Bt:l={x:t.x-r.width,y:a};break;default:l={x:t.x,y:t.y}}var c=n?Ra(n):null;if(c!=null){var u=c==="y"?"height":"width";switch(s){case Io:l[c]=l[c]-(t[u]/2-r[u]/2);break;case gn:l[c]=l[c]+(t[u]/2-r[u]/2);break}}return l}function hn(e,t){t===void 0&&(t={});var r=t,o=r.placement,n=o===void 0?e.placement:o,s=r.strategy,i=s===void 0?e.strategy:s,a=r.boundary,l=a===void 0?ib:a,c=r.rootBoundary,u=c===void 0?Du:c,d=r.elementContext,f=d===void 0?Ho:d,m=r.altBoundary,h=m===void 0?!1:m,b=r.padding,y=b===void 0?0:b,S=_u(typeof y!="number"?y:Gu(y,wn)),$=f===Ho?ab:Ho,C=e.rects.popper,v=e.elements[h?$:f],x=Db(oo(v)?v:v.contextElement||Kr(e.elements.popper),l,u,i),k=Mo(e.elements.reference),T=qu({reference:k,element:C,placement:n}),I=zi(Object.assign({},C,T)),A=f===Ho?I:k,M={top:x.top-A.top+S.top,bottom:A.bottom-x.bottom+S.bottom,left:x.left-A.left+S.left,right:A.right-x.right+S.right},L=e.modifiersData.offset;if(f===Ho&&L){var g=L[n];Object.keys(M).forEach(function(P){var R=[rr,tr].indexOf(P)>=0?1:-1,O=[Lt,tr].indexOf(P)>=0?"y":"x";M[P]+=g[O]*R})}return M}function Wb(e,t){t===void 0&&(t={});var r=t,o=r.placement,n=r.boundary,s=r.rootBoundary,i=r.padding,a=r.flipVariations,l=r.allowedAutoPlacements,c=l===void 0?Wu:l,u=Ao(o),d=u?a?Vl:Vl.filter(function(h){return Ao(h)===u}):wn,f=d.filter(function(h){return c.indexOf(h)>=0});f.length===0&&(f=d);var m=f.reduce(function(h,b){return h[b]=hn(e,{placement:b,boundary:n,rootBoundary:s,padding:i})[vr(b)],h},{});return Object.keys(m).sort(function(h,b){return m[h]-m[b]})}function Ub(e){if(vr(e)===wa)return[];var t=Xn(e);return[Kl(e),t,Kl(t)]}function Hb(e){var t=e.state,r=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var n=r.mainAxis,s=n===void 0?!0:n,i=r.altAxis,a=i===void 0?!0:i,l=r.fallbackPlacements,c=r.padding,u=r.boundary,d=r.rootBoundary,f=r.altBoundary,m=r.flipVariations,h=m===void 0?!0:m,b=r.allowedAutoPlacements,y=t.options.placement,S=vr(y),$=S===y,C=l||($||!h?[Xn(y)]:Ub(y)),v=[y].concat(C).reduce(function(ye,K){return ye.concat(vr(K)===wa?Wb(t,{placement:K,boundary:u,rootBoundary:d,padding:c,flipVariations:h,allowedAutoPlacements:b}):K)},[]),x=t.rects.reference,k=t.rects.popper,T=new Map,I=!0,A=v[0],M=0;M<v.length;M++){var L=v[M],g=vr(L),P=Ao(L)===Io,R=[Lt,tr].indexOf(g)>=0,O=R?"width":"height",E=hn(t,{placement:L,boundary:u,rootBoundary:d,altBoundary:f,padding:c}),F=R?P?rr:Bt:P?tr:Lt;x[O]>k[O]&&(F=Xn(F));var z=Xn(F),N=[];if(s&&N.push(E[g]<=0),a&&N.push(E[F]<=0,E[z]<=0),N.every(function(ye){return ye})){A=L,I=!1;break}T.set(L,N)}if(I)for(var Q=h?3:1,_=function(K){var q=v.find(function(ie){var we=T.get(ie);if(we)return we.slice(0,K).every(function(he){return he})});if(q)return A=q,"break"},Z=Q;Z>0;Z--){var se=_(Z);if(se==="break")break}t.placement!==A&&(t.modifiersData[o]._skip=!0,t.placement=A,t.reset=!0)}}const Vb={name:"flip",enabled:!0,phase:"main",fn:Hb,requiresIfExists:["offset"],data:{_skip:!1}};function Yl(e,t,r){return r===void 0&&(r={x:0,y:0}),{top:e.top-t.height-r.y,right:e.right-t.width+r.x,bottom:e.bottom-t.height+r.y,left:e.left-t.width-r.x}}function Xl(e){return[Lt,rr,tr,Bt].some(function(t){return e[t]>=0})}function _b(e){var t=e.state,r=e.name,o=t.rects.reference,n=t.rects.popper,s=t.modifiersData.preventOverflow,i=hn(t,{elementContext:"reference"}),a=hn(t,{altBoundary:!0}),l=Yl(i,o),c=Yl(a,n,s),u=Xl(l),d=Xl(c);t.modifiersData[r]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}const Gb={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:_b};function Kb(e,t,r){var o=vr(e),n=[Bt,Lt].indexOf(o)>=0?-1:1,s=typeof r=="function"?r(Object.assign({},t,{placement:e})):r,i=s[0],a=s[1];return i=i||0,a=(a||0)*n,[Bt,rr].indexOf(o)>=0?{x:a,y:i}:{x:i,y:a}}function qb(e){var t=e.state,r=e.options,o=e.name,n=r.offset,s=n===void 0?[0,0]:n,i=Wu.reduce(function(u,d){return u[d]=Kb(d,t.rects,s),u},{}),a=i[t.placement],l=a.x,c=a.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[o]=i}const Yb={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:qb};function Xb(e){var t=e.state,r=e.name;t.modifiersData[r]=qu({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}const Qb={name:"popperOffsets",enabled:!0,phase:"read",fn:Xb,data:{}};function Zb(e){return e==="x"?"y":"x"}function Jb(e){var t=e.state,r=e.options,o=e.name,n=r.mainAxis,s=n===void 0?!0:n,i=r.altAxis,a=i===void 0?!1:i,l=r.boundary,c=r.rootBoundary,u=r.altBoundary,d=r.padding,f=r.tether,m=f===void 0?!0:f,h=r.tetherOffset,b=h===void 0?0:h,y=hn(t,{boundary:l,rootBoundary:c,padding:d,altBoundary:u}),S=vr(t.placement),$=Ao(t.placement),C=!$,v=Ra(S),x=Zb(v),k=t.modifiersData.popperOffsets,T=t.rects.reference,I=t.rects.popper,A=typeof b=="function"?b(Object.assign({},t.rects,{placement:t.placement})):b,M=typeof A=="number"?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),L=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,g={x:0,y:0};if(k){if(s){var P,R=v==="y"?Lt:Bt,O=v==="y"?tr:rr,E=v==="y"?"height":"width",F=k[v],z=F+y[R],N=F-y[O],Q=m?-I[E]/2:0,_=$===Io?T[E]:I[E],Z=$===Io?-I[E]:-T[E],se=t.elements.arrow,ye=m&&se?ka(se):{width:0,height:0},K=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:Vu(),q=K[R],ie=K[O],we=tn(0,T[E],ye[E]),he=C?T[E]/2-Q-we-q-M.mainAxis:_-we-q-M.mainAxis,ee=C?-T[E]/2+Q+we+ie+M.mainAxis:Z+we+ie+M.mainAxis,Ce=t.elements.arrow&&$n(t.elements.arrow),oe=Ce?v==="y"?Ce.clientTop||0:Ce.clientLeft||0:0,ge=(P=L==null?void 0:L[v])!=null?P:0,ve=F+he-ge-oe,Le=F+ee-ge,Me=tn(m?is(z,ve):z,F,m?to(N,Le):N);k[v]=Me,g[v]=Me-F}if(a){var $e,J=v==="x"?Lt:Bt,Ae=v==="x"?tr:rr,pe=k[x],Re=x==="y"?"height":"width",te=pe+y[J],Ge=pe-y[Ae],je=[Lt,Bt].indexOf(S)!==-1,H=($e=L==null?void 0:L[x])!=null?$e:0,Y=je?te:pe-T[Re]-I[Re]-H+M.altAxis,ae=je?pe+T[Re]+I[Re]-H-M.altAxis:Ge,xe=m&&je?wb(Y,pe,ae):tn(m?Y:te,pe,m?ae:Ge);k[x]=xe,g[x]=xe-pe}t.modifiersData[o]=g}}const ey={name:"preventOverflow",enabled:!0,phase:"main",fn:Jb,requiresIfExists:["offset"]};function ty(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function ry(e){return e===Ht(e)||!Jt(e)?Pa(e):ty(e)}function oy(e){var t=e.getBoundingClientRect(),r=Eo(t.width)/e.offsetWidth||1,o=Eo(t.height)/e.offsetHeight||1;return r!==1||o!==1}function ny(e,t,r){r===void 0&&(r=!1);var o=Jt(t),n=Jt(t)&&oy(t),s=Kr(t),i=Mo(e,n,r),a={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(o||!o&&!r)&&((Cr(t)!=="body"||Ia(s))&&(a=ry(t)),Jt(t)?(l=Mo(t,!0),l.x+=t.clientLeft,l.y+=t.clientTop):s&&(l.x=Ta(s))),{x:i.left+a.scrollLeft-l.x,y:i.top+a.scrollTop-l.y,width:i.width,height:i.height}}function sy(e){var t=new Map,r=new Set,o=[];e.forEach(function(s){t.set(s.name,s)});function n(s){r.add(s.name);var i=[].concat(s.requires||[],s.requiresIfExists||[]);i.forEach(function(a){if(!r.has(a)){var l=t.get(a);l&&n(l)}}),o.push(s)}return e.forEach(function(s){r.has(s.name)||n(s)}),o}function iy(e){var t=sy(e);return bb.reduce(function(r,o){return r.concat(t.filter(function(n){return n.phase===o}))},[])}function ay(e){var t;return function(){return t||(t=new Promise(function(r){Promise.resolve().then(function(){t=void 0,r(e())})})),t}}function ly(e){var t=e.reduce(function(r,o){var n=r[o.name];return r[o.name]=n?Object.assign({},n,o,{options:Object.assign({},n.options,o.options),data:Object.assign({},n.data,o.data)}):o,r},{});return Object.keys(t).map(function(r){return t[r]})}var Ql={placement:"bottom",modifiers:[],strategy:"absolute"};function Zl(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return!t.some(function(o){return!(o&&typeof o.getBoundingClientRect=="function")})}function cy(e){e===void 0&&(e={});var t=e,r=t.defaultModifiers,o=r===void 0?[]:r,n=t.defaultOptions,s=n===void 0?Ql:n;return function(a,l,c){c===void 0&&(c=s);var u={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ql,s),modifiersData:{},elements:{reference:a,popper:l},attributes:{},styles:{}},d=[],f=!1,m={state:u,setOptions:function(S){var $=typeof S=="function"?S(u.options):S;b(),u.options=Object.assign({},s,u.options,$),u.scrollParents={reference:oo(a)?rn(a):a.contextElement?rn(a.contextElement):[],popper:rn(l)};var C=iy(ly([].concat(o,u.options.modifiers)));return u.orderedModifiers=C.filter(function(v){return v.enabled}),h(),m.update()},forceUpdate:function(){if(!f){var S=u.elements,$=S.reference,C=S.popper;if(Zl($,C)){u.rects={reference:ny($,$n(C),u.options.strategy==="fixed"),popper:ka(C)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach(function(M){return u.modifiersData[M.name]=Object.assign({},M.data)});for(var v=0;v<u.orderedModifiers.length;v++){if(u.reset===!0){u.reset=!1,v=-1;continue}var x=u.orderedModifiers[v],k=x.fn,T=x.options,I=T===void 0?{}:T,A=x.name;typeof k=="function"&&(u=k({state:u,options:I,name:A,instance:m})||u)}}}},update:ay(function(){return new Promise(function(y){m.forceUpdate(),y(u)})}),destroy:function(){b(),f=!0}};if(!Zl(a,l))return m;m.setOptions(c).then(function(y){!f&&c.onFirstUpdate&&c.onFirstUpdate(y)});function h(){u.orderedModifiers.forEach(function(y){var S=y.name,$=y.options,C=$===void 0?{}:$,v=y.effect;if(typeof v=="function"){var x=v({state:u,name:S,instance:m,options:C}),k=function(){};d.push(x||k)}})}function b(){d.forEach(function(y){return y()}),d=[]}return m}}var uy=[Ob,Qb,Mb,xb,Yb,Vb,ey,Pb,Gb],dy=cy({defaultModifiers:uy});function Ot(e){var d;const{elementType:t,externalSlotProps:r,ownerState:o,skipResolvingSlotProps:n=!1,...s}=e,i=n?{}:zu(r,o),{props:a,internalRef:l}=Fu({...s,externalSlotProps:i}),c=ot(l,i==null?void 0:i.ref,(d=e.additionalProps)==null?void 0:d.ref);return ju(t,{...a,ref:c},o)}function py(e){return typeof e=="function"?e():e}const Yu=p.forwardRef(function(t,r){const{children:o,container:n,disablePortal:s=!1}=t,[i,a]=p.useState(null),l=ot(p.isValidElement(o)?Gr(o):null,r);if(It(()=>{s||a(py(n)||document.body)},[n,s]),It(()=>{if(i&&!s)return Ei(r,i),()=>{Ei(r,null)}},[r,i,s]),s){if(p.isValidElement(o)){const c={ref:l};return p.cloneElement(o,c)}return o}return i&&Uc.createPortal(o,i)});function fy(e){return ce("MuiPopper",e)}ue("MuiPopper",["root"]);function my(e,t){if(t==="ltr")return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}function Fi(e){return typeof e=="function"?e():e}function gy(e){return e.nodeType!==void 0}const hy=e=>{const{classes:t}=e;return de({root:["root"]},fy,t)},by={},yy=p.forwardRef(function(t,r){const{anchorEl:o,children:n,direction:s,disablePortal:i,modifiers:a,open:l,placement:c,popperOptions:u,popperRef:d,slotProps:f={},slots:m={},TransitionProps:h,ownerState:b,...y}=t,S=p.useRef(null),$=ot(S,r),C=p.useRef(null),v=ot(C,d),x=p.useRef(v);It(()=>{x.current=v},[v]),p.useImperativeHandle(d,()=>C.current,[]);const k=my(c,s),[T,I]=p.useState(k),[A,M]=p.useState(Fi(o));p.useEffect(()=>{C.current&&C.current.forceUpdate()}),p.useEffect(()=>{o&&M(Fi(o))},[o]),It(()=>{if(!A||!l)return;const O=z=>{I(z.placement)};let E=[{name:"preventOverflow",options:{altBoundary:i}},{name:"flip",options:{altBoundary:i}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:z})=>{O(z)}}];a!=null&&(E=E.concat(a)),u&&u.modifiers!=null&&(E=E.concat(u.modifiers));const F=dy(A,S.current,{placement:k,...u,modifiers:E});return x.current(F),()=>{F.destroy(),x.current(null)}},[A,i,a,l,u,k]);const L={placement:T};h!==null&&(L.TransitionProps=h);const g=hy(t),P=m.root??"div",R=Ot({elementType:P,externalSlotProps:f.root,externalForwardedProps:y,additionalProps:{role:"tooltip",ref:$},ownerState:t,className:g.root});return w.jsx(P,{...R,children:typeof n=="function"?n(L):n})}),vy=p.forwardRef(function(t,r){const{anchorEl:o,children:n,container:s,direction:i="ltr",disablePortal:a=!1,keepMounted:l=!1,modifiers:c,open:u,placement:d="bottom",popperOptions:f=by,popperRef:m,style:h,transition:b=!1,slotProps:y={},slots:S={},...$}=t,[C,v]=p.useState(!0),x=()=>{v(!1)},k=()=>{v(!0)};if(!l&&!u&&(!b||C))return null;let T;if(s)T=s;else if(o){const M=Fi(o);T=M&&gy(M)?mt(M).body:mt(null).body}const I=!u&&l&&(!b||C)?"none":void 0,A=b?{in:u,onEnter:x,onExited:k}:void 0;return w.jsx(Yu,{disablePortal:a,container:T,children:w.jsx(yy,{anchorEl:o,direction:i,disablePortal:a,modifiers:c,ref:r,open:b?!C:u,placement:d,popperOptions:f,popperRef:m,slotProps:y,slots:S,...$,style:{position:"fixed",top:0,left:0,display:I,...h},TransitionProps:A,children:n})})}),xy=j(vy,{name:"MuiPopper",slot:"Root"})({}),Os=p.forwardRef(function(t,r){const o=Mr(),n=me({props:t,name:"MuiPopper"}),{anchorEl:s,component:i,components:a,componentsProps:l,container:c,disablePortal:u,keepMounted:d,modifiers:f,open:m,placement:h,popperOptions:b,popperRef:y,transition:S,slots:$,slotProps:C,...v}=n,x=($==null?void 0:$.root)??(a==null?void 0:a.Root),k={anchorEl:s,container:c,disablePortal:u,keepMounted:d,modifiers:f,open:m,placement:h,popperOptions:b,popperRef:y,transition:S,...v};return w.jsx(xy,{as:i,direction:o?"rtl":"ltr",slots:{root:x},slotProps:C??l,...k,ref:r})});function Sy(e){return ce("MuiTooltip",e)}const ut=ue("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]);function Cy(e){return Math.round(e*1e5)/1e5}const wy=e=>{const{classes:t,disableInteractive:r,arrow:o,touch:n,placement:s}=e,i={popper:["popper",!r&&"popperInteractive",o&&"popperArrow"],tooltip:["tooltip",o&&"tooltipArrow",n&&"touch",`tooltipPlacement${B(s.split("-")[0])}`],arrow:["arrow"]};return de(i,Sy,t)},$y=j(Os,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.popper,!r.disableInteractive&&t.popperInteractive,r.arrow&&t.popperArrow,!r.open&&t.popperClose]}})(re(({theme:e})=>({zIndex:(e.vars||e).zIndex.tooltip,pointerEvents:"none",variants:[{props:({ownerState:t})=>!t.disableInteractive,style:{pointerEvents:"auto"}},{props:({open:t})=>!t,style:{pointerEvents:"none"}},{props:({ownerState:t})=>t.arrow,style:{[`&[data-popper-placement*="bottom"] .${ut.arrow}`]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},[`&[data-popper-placement*="top"] .${ut.arrow}`]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},[`&[data-popper-placement*="right"] .${ut.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},[`&[data-popper-placement*="left"] .${ut.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:({ownerState:t})=>t.arrow&&!t.isRtl,style:{[`&[data-popper-placement*="right"] .${ut.arrow}`]:{left:0,marginLeft:"-0.71em"}}},{props:({ownerState:t})=>t.arrow&&!!t.isRtl,style:{[`&[data-popper-placement*="right"] .${ut.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:t})=>t.arrow&&!t.isRtl,style:{[`&[data-popper-placement*="left"] .${ut.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:t})=>t.arrow&&!!t.isRtl,style:{[`&[data-popper-placement*="left"] .${ut.arrow}`]:{left:0,marginLeft:"-0.71em"}}}]}))),ky=j("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.tooltip,r.touch&&t.touch,r.arrow&&t.tooltipArrow,t[`tooltipPlacement${B(r.placement.split("-")[0])}`]]}})(re(({theme:e})=>({backgroundColor:e.vars?e.vars.palette.Tooltip.bg:e.alpha(e.palette.grey[700],.92),borderRadius:(e.vars||e).shape.borderRadius,color:(e.vars||e).palette.common.white,fontFamily:e.typography.fontFamily,padding:"4px 8px",fontSize:e.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:e.typography.fontWeightMedium,[`.${ut.popper}[data-popper-placement*="left"] &`]:{transformOrigin:"right center"},[`.${ut.popper}[data-popper-placement*="right"] &`]:{transformOrigin:"left center"},[`.${ut.popper}[data-popper-placement*="top"] &`]:{transformOrigin:"center bottom",marginBottom:"14px"},[`.${ut.popper}[data-popper-placement*="bottom"] &`]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:({ownerState:t})=>t.arrow,style:{position:"relative",margin:0}},{props:({ownerState:t})=>t.touch,style:{padding:"8px 16px",fontSize:e.typography.pxToRem(14),lineHeight:`${Cy(16/14)}em`,fontWeight:e.typography.fontWeightRegular}},{props:({ownerState:t})=>!t.isRtl,style:{[`.${ut.popper}[data-popper-placement*="left"] &`]:{marginRight:"14px"},[`.${ut.popper}[data-popper-placement*="right"] &`]:{marginLeft:"14px"}}},{props:({ownerState:t})=>!t.isRtl&&t.touch,style:{[`.${ut.popper}[data-popper-placement*="left"] &`]:{marginRight:"24px"},[`.${ut.popper}[data-popper-placement*="right"] &`]:{marginLeft:"24px"}}},{props:({ownerState:t})=>!!t.isRtl,style:{[`.${ut.popper}[data-popper-placement*="left"] &`]:{marginLeft:"14px"},[`.${ut.popper}[data-popper-placement*="right"] &`]:{marginRight:"14px"}}},{props:({ownerState:t})=>!!t.isRtl&&t.touch,style:{[`.${ut.popper}[data-popper-placement*="left"] &`]:{marginLeft:"24px"},[`.${ut.popper}[data-popper-placement*="right"] &`]:{marginRight:"24px"}}},{props:({ownerState:t})=>t.touch,style:{[`.${ut.popper}[data-popper-placement*="top"] &`]:{marginBottom:"24px"}}},{props:({ownerState:t})=>t.touch,style:{[`.${ut.popper}[data-popper-placement*="bottom"] &`]:{marginTop:"24px"}}}]}))),Ry=j("span",{name:"MuiTooltip",slot:"Arrow"})(re(({theme:e})=>({overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:e.vars?e.vars.palette.Tooltip.bg:e.alpha(e.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}})));let Bn=!1;const Jl=new Ou;let Vo={x:0,y:0};function Nn(e,t){return(r,...o)=>{t&&t(r,...o),e(r,...o)}}const b$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiTooltip"}),{arrow:n=!1,children:s,classes:i,components:a={},componentsProps:l={},describeChild:c=!1,disableFocusListener:u=!1,disableHoverListener:d=!1,disableInteractive:f=!1,disableTouchListener:m=!1,enterDelay:h=100,enterNextDelay:b=0,enterTouchDelay:y=700,followCursor:S=!1,id:$,leaveDelay:C=0,leaveTouchDelay:v=1500,onClose:x,onOpen:k,open:T,placement:I="bottom",PopperComponent:A,PopperProps:M={},slotProps:L={},slots:g={},title:P,TransitionComponent:R,TransitionProps:O,...E}=o,F=p.isValidElement(s)?s:w.jsx("span",{children:s}),z=$r(),N=Mr(),[Q,_]=p.useState(),[Z,se]=p.useState(null),ye=p.useRef(!1),K=f||S,q=vo(),ie=vo(),we=vo(),he=vo(),[ee,Ce]=Wr({controlled:T,default:!1,name:"Tooltip",state:"open"});let oe=ee;const ge=xr($),ve=p.useRef(),Le=vt(()=>{ve.current!==void 0&&(document.body.style.WebkitUserSelect=ve.current,ve.current=void 0),he.clear()});p.useEffect(()=>Le,[Le]);const Me=Oe=>{Jl.clear(),Bn=!0,Ce(!0),k&&!oe&&k(Oe)},$e=vt(Oe=>{Jl.start(800+C,()=>{Bn=!1}),Ce(!1),x&&oe&&x(Oe),q.start(z.transitions.duration.shortest,()=>{ye.current=!1})}),J=Oe=>{ye.current&&Oe.type!=="touchstart"||(Q&&Q.removeAttribute("title"),ie.clear(),we.clear(),h||Bn&&b?ie.start(Bn?b:h,()=>{Me(Oe)}):Me(Oe))},Ae=Oe=>{ie.clear(),we.start(C,()=>{$e(Oe)})},[,pe]=p.useState(!1),Re=Oe=>{Vr(Oe.target)||(pe(!1),Ae(Oe))},te=Oe=>{Q||_(Oe.currentTarget),Vr(Oe.target)&&(pe(!0),J(Oe))},Ge=Oe=>{ye.current=!0;const pt=F.props;pt.onTouchStart&&pt.onTouchStart(Oe)},je=Oe=>{Ge(Oe),we.clear(),q.clear(),Le(),ve.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",he.start(y,()=>{document.body.style.WebkitUserSelect=ve.current,J(Oe)})},H=Oe=>{F.props.onTouchEnd&&F.props.onTouchEnd(Oe),Le(),we.start(v,()=>{$e(Oe)})};p.useEffect(()=>{if(!oe)return;function Oe(pt){pt.key==="Escape"&&$e(pt)}return document.addEventListener("keydown",Oe),()=>{document.removeEventListener("keydown",Oe)}},[$e,oe]);const Y=ot(Gr(F),_,r);!P&&P!==0&&(oe=!1);const ae=p.useRef(),xe=Oe=>{const pt=F.props;pt.onMouseMove&&pt.onMouseMove(Oe),Vo={x:Oe.clientX,y:Oe.clientY},ae.current&&ae.current.update()},Te={},X=typeof P=="string";c?(Te.title=!oe&&X&&!d?P:null,Te["aria-describedby"]=oe?ge:null):(Te["aria-label"]=X?P:null,Te["aria-labelledby"]=oe&&!X?ge:null);const G={...Te,...E,...F.props,className:V(E.className,F.props.className),onTouchStart:Ge,ref:Y,...S?{onMouseMove:xe}:{}},Pe={};m||(G.onTouchStart=je,G.onTouchEnd=H),d||(G.onMouseOver=Nn(J,G.onMouseOver),G.onMouseLeave=Nn(Ae,G.onMouseLeave),K||(Pe.onMouseOver=J,Pe.onMouseLeave=Ae)),u||(G.onFocus=Nn(te,G.onFocus),G.onBlur=Nn(Re,G.onBlur),K||(Pe.onFocus=te,Pe.onBlur=Re));const Ie={...o,isRtl:N,arrow:n,disableInteractive:K,placement:I,PopperComponentProp:A,touch:ye.current},ke=typeof L.popper=="function"?L.popper(Ie):L.popper,dt=p.useMemo(()=>{var pt,ne;let Oe=[{name:"arrow",enabled:!!Z,options:{element:Z,padding:4}}];return(pt=M.popperOptions)!=null&&pt.modifiers&&(Oe=Oe.concat(M.popperOptions.modifiers)),(ne=ke==null?void 0:ke.popperOptions)!=null&&ne.modifiers&&(Oe=Oe.concat(ke.popperOptions.modifiers)),{...M.popperOptions,...ke==null?void 0:ke.popperOptions,modifiers:Oe}},[Z,M.popperOptions,ke==null?void 0:ke.popperOptions]),bt=wy(Ie),nt=typeof L.transition=="function"?L.transition(Ie):L.transition,kt={slots:{popper:a.Popper,transition:a.Transition??R,tooltip:a.Tooltip,arrow:a.Arrow,...g},slotProps:{arrow:L.arrow??l.arrow,popper:{...M,...ke??l.popper},tooltip:L.tooltip??l.tooltip,transition:{...O,...nt??l.transition}}},[be,Fe]=le("popper",{elementType:$y,externalForwardedProps:kt,ownerState:Ie,className:V(bt.popper,M==null?void 0:M.className)}),[He,Ne]=le("transition",{elementType:ss,externalForwardedProps:kt,ownerState:Ie}),[Ve,Rt]=le("tooltip",{elementType:ky,className:bt.tooltip,externalForwardedProps:kt,ownerState:Ie}),[St,st]=le("arrow",{elementType:Ry,className:bt.arrow,externalForwardedProps:kt,ownerState:Ie,ref:se});return w.jsxs(p.Fragment,{children:[p.cloneElement(F,G),w.jsx(be,{as:A??Os,placement:I,anchorEl:S?{getBoundingClientRect:()=>({top:Vo.y,left:Vo.x,right:Vo.x,bottom:Vo.y,width:0,height:0})}:Q,popperRef:ae,open:Q?oe:!1,id:ge,transition:!0,...Pe,...Fe,popperOptions:dt,children:({TransitionProps:Oe})=>w.jsx(He,{timeout:z.transitions.duration.shorter,...Oe,...Ne,children:w.jsxs(Ve,{...Rt,children:[P,n?w.jsx(St,{...st}):null]})})})]})});function Xu(e=window){const t=e.document.documentElement.clientWidth;return e.innerWidth-t}function Py(e){const t=mt(e);return t.body===e?Ut(e).innerWidth>t.documentElement.clientWidth:e.scrollHeight>e.clientHeight}function on(e,t){t?e.setAttribute("aria-hidden","true"):e.removeAttribute("aria-hidden")}function ec(e){return parseInt(Ut(e).getComputedStyle(e).paddingRight,10)||0}function Ty(e){const r=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(e.tagName),o=e.tagName==="INPUT"&&e.getAttribute("type")==="hidden";return r||o}function tc(e,t,r,o,n){const s=[t,r,...o];[].forEach.call(e.children,i=>{const a=!s.includes(i),l=!Ty(i);a&&l&&on(i,n)})}function fi(e,t){let r=-1;return e.some((o,n)=>t(o)?(r=n,!0):!1),r}function Iy(e,t){const r=[],o=e.container;if(!t.disableScrollLock){if(Py(o)){const i=Xu(Ut(o));r.push({value:o.style.paddingRight,property:"padding-right",el:o}),o.style.paddingRight=`${ec(o)+i}px`;const a=mt(o).querySelectorAll(".mui-fixed");[].forEach.call(a,l=>{r.push({value:l.style.paddingRight,property:"padding-right",el:l}),l.style.paddingRight=`${ec(l)+i}px`})}let s;if(o.parentNode instanceof DocumentFragment)s=mt(o).body;else{const i=o.parentElement,a=Ut(o);s=(i==null?void 0:i.nodeName)==="HTML"&&a.getComputedStyle(i).overflowY==="scroll"?i:o}r.push({value:s.style.overflow,property:"overflow",el:s},{value:s.style.overflowX,property:"overflow-x",el:s},{value:s.style.overflowY,property:"overflow-y",el:s}),s.style.overflow="hidden"}return()=>{r.forEach(({value:s,el:i,property:a})=>{s?i.style.setProperty(a,s):i.style.removeProperty(a)})}}function Ey(e){const t=[];return[].forEach.call(e.children,r=>{r.getAttribute("aria-hidden")==="true"&&t.push(r)}),t}class My{constructor(){this.modals=[],this.containers=[]}add(t,r){let o=this.modals.indexOf(t);if(o!==-1)return o;o=this.modals.length,this.modals.push(t),t.modalRef&&on(t.modalRef,!1);const n=Ey(r);tc(r,t.mount,t.modalRef,n,!0);const s=fi(this.containers,i=>i.container===r);return s!==-1?(this.containers[s].modals.push(t),o):(this.containers.push({modals:[t],container:r,restore:null,hiddenSiblings:n}),o)}mount(t,r){const o=fi(this.containers,s=>s.modals.includes(t)),n=this.containers[o];n.restore||(n.restore=Iy(n,r))}remove(t,r=!0){const o=this.modals.indexOf(t);if(o===-1)return o;const n=fi(this.containers,i=>i.modals.includes(t)),s=this.containers[n];if(s.modals.splice(s.modals.indexOf(t),1),this.modals.splice(o,1),s.modals.length===0)s.restore&&s.restore(),t.modalRef&&on(t.modalRef,r),tc(s.container,t.mount,t.modalRef,s.hiddenSiblings,!1),this.containers.splice(n,1);else{const i=s.modals[s.modals.length-1];i.modalRef&&on(i.modalRef,!1)}return o}isTopModal(t){return this.modals.length>0&&this.modals[this.modals.length-1]===t}}const Ay=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function Oy(e){const t=parseInt(e.getAttribute("tabindex")||"",10);return Number.isNaN(t)?e.contentEditable==="true"||(e.nodeName==="AUDIO"||e.nodeName==="VIDEO"||e.nodeName==="DETAILS")&&e.getAttribute("tabindex")===null?0:e.tabIndex:t}function Ly(e){if(e.tagName!=="INPUT"||e.type!=="radio"||!e.name)return!1;const t=o=>e.ownerDocument.querySelector(`input[type="radio"]${o}`);let r=t(`[name="${e.name}"]:checked`);return r||(r=t(`[name="${e.name}"]`)),r!==e}function By(e){return!(e.disabled||e.tagName==="INPUT"&&e.type==="hidden"||Ly(e))}function Ny(e){const t=[],r=[];return Array.from(e.querySelectorAll(Ay)).forEach((o,n)=>{const s=Oy(o);s===-1||!By(o)||(s===0?t.push(o):r.push({documentOrder:n,tabIndex:s,node:o}))}),r.sort((o,n)=>o.tabIndex===n.tabIndex?o.documentOrder-n.documentOrder:o.tabIndex-n.tabIndex).map(o=>o.node).concat(t)}function jy(){return!0}function zy(e){const{children:t,disableAutoFocus:r=!1,disableEnforceFocus:o=!1,disableRestoreFocus:n=!1,getTabbable:s=Ny,isEnabled:i=jy,open:a}=e,l=p.useRef(!1),c=p.useRef(null),u=p.useRef(null),d=p.useRef(null),f=p.useRef(null),m=p.useRef(!1),h=p.useRef(null),b=ot(Gr(t),h),y=p.useRef(null);p.useEffect(()=>{!a||!h.current||(m.current=!r)},[r,a]),p.useEffect(()=>{if(!a||!h.current)return;const C=mt(h.current);return h.current.contains(C.activeElement)||(h.current.hasAttribute("tabIndex")||h.current.setAttribute("tabIndex","-1"),m.current&&h.current.focus()),()=>{n||(d.current&&d.current.focus&&(l.current=!0,d.current.focus()),d.current=null)}},[a]),p.useEffect(()=>{if(!a||!h.current)return;const C=mt(h.current),v=T=>{y.current=T,!(o||!i()||T.key!=="Tab")&&C.activeElement===h.current&&T.shiftKey&&(l.current=!0,u.current&&u.current.focus())},x=()=>{var A,M;const T=h.current;if(T===null)return;if(!C.hasFocus()||!i()||l.current){l.current=!1;return}if(T.contains(C.activeElement)||o&&C.activeElement!==c.current&&C.activeElement!==u.current)return;if(C.activeElement!==f.current)f.current=null;else if(f.current!==null)return;if(!m.current)return;let I=[];if((C.activeElement===c.current||C.activeElement===u.current)&&(I=s(h.current)),I.length>0){const L=!!((A=y.current)!=null&&A.shiftKey&&((M=y.current)==null?void 0:M.key)==="Tab"),g=I[0],P=I[I.length-1];typeof g!="string"&&typeof P!="string"&&(L?P.focus():g.focus())}else T.focus()};C.addEventListener("focusin",x),C.addEventListener("keydown",v,!0);const k=setInterval(()=>{C.activeElement&&C.activeElement.tagName==="BODY"&&x()},50);return()=>{clearInterval(k),C.removeEventListener("focusin",x),C.removeEventListener("keydown",v,!0)}},[r,o,n,i,a,s]);const S=C=>{d.current===null&&(d.current=C.relatedTarget),m.current=!0,f.current=C.target;const v=t.props.onFocus;v&&v(C)},$=C=>{d.current===null&&(d.current=C.relatedTarget),m.current=!0};return w.jsxs(p.Fragment,{children:[w.jsx("div",{tabIndex:a?0:-1,onFocus:$,ref:c,"data-testid":"sentinelStart"}),p.cloneElement(t,{ref:b,onFocus:S}),w.jsx("div",{tabIndex:a?0:-1,onFocus:$,ref:u,"data-testid":"sentinelEnd"})]})}const Fy={entering:{opacity:1},entered:{opacity:1}},Di=p.forwardRef(function(t,r){const o=$r(),n={enter:o.transitions.duration.enteringScreen,exit:o.transitions.duration.leavingScreen},{addEndListener:s,appear:i=!0,children:a,easing:l,in:c,onEnter:u,onEntered:d,onEntering:f,onExit:m,onExited:h,onExiting:b,style:y,timeout:S=n,TransitionComponent:$=nr,...C}=t,v=p.useRef(null),x=ot(v,Gr(a),r),k=R=>O=>{if(R){const E=v.current;O===void 0?R(E):R(E,O)}},T=k(f),I=k((R,O)=>{Ca(R);const E=To({style:y,timeout:S,easing:l},{mode:"enter"});R.style.webkitTransition=o.transitions.create("opacity",E),R.style.transition=o.transitions.create("opacity",E),u&&u(R,O)}),A=k(d),M=k(b),L=k(R=>{const O=To({style:y,timeout:S,easing:l},{mode:"exit"});R.style.webkitTransition=o.transitions.create("opacity",O),R.style.transition=o.transitions.create("opacity",O),m&&m(R)}),g=k(h),P=R=>{s&&s(v.current,R)};return w.jsx($,{appear:i,in:c,nodeRef:v,onEnter:I,onEntered:A,onEntering:T,onExit:L,onExited:g,onExiting:M,addEndListener:P,timeout:S,...C,children:(R,{ownerState:O,...E})=>p.cloneElement(a,{style:{opacity:0,visibility:R==="exited"&&!c?"hidden":void 0,...Fy[R],...y,...a.props.style},ref:x,...E})})});function Dy(e){return ce("MuiBackdrop",e)}const y$=ue("MuiBackdrop",["root","invisible"]),Wy=e=>{const{classes:t,invisible:r}=e;return de({root:["root",r&&"invisible"]},Dy,t)},Uy=j("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.invisible&&t.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]}),Qu=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiBackdrop"}),{children:n,className:s,component:i="div",invisible:a=!1,open:l,components:c={},componentsProps:u={},slotProps:d={},slots:f={},TransitionComponent:m,transitionDuration:h,...b}=o,y={...o,component:i,invisible:a},S=Wy(y),$={transition:m,root:c.Root,...f},C={...u,...d},v={component:i,slots:$,slotProps:C},[x,k]=le("root",{elementType:Uy,externalForwardedProps:v,className:V(S.root,s),ownerState:y}),[T,I]=le("transition",{elementType:Di,externalForwardedProps:v,ownerState:y});return w.jsx(T,{in:l,timeout:h,...b,...I,children:w.jsx(x,{"aria-hidden":!0,...k,classes:S,ref:r,children:n})})});function Hy(e){return typeof e=="function"?e():e}function Vy(e){return e?e.props.hasOwnProperty("in"):!1}const rc=()=>{},jn=new My;function _y(e){const{container:t,disableEscapeKeyDown:r=!1,disableScrollLock:o=!1,closeAfterTransition:n=!1,onTransitionEnter:s,onTransitionExited:i,children:a,onClose:l,open:c,rootRef:u}=e,d=p.useRef({}),f=p.useRef(null),m=p.useRef(null),h=ot(m,u),[b,y]=p.useState(!c),S=Vy(a);let $=!0;(e["aria-hidden"]==="false"||e["aria-hidden"]===!1)&&($=!1);const C=()=>mt(f.current),v=()=>(d.current.modalRef=m.current,d.current.mount=f.current,d.current),x=()=>{jn.mount(v(),{disableScrollLock:o}),m.current&&(m.current.scrollTop=0)},k=vt(()=>{const O=Hy(t)||C().body;jn.add(v(),O),m.current&&x()}),T=()=>jn.isTopModal(v()),I=vt(O=>{f.current=O,O&&(c&&T()?x():m.current&&on(m.current,$))}),A=p.useCallback(()=>{jn.remove(v(),$)},[$]);p.useEffect(()=>()=>{A()},[A]),p.useEffect(()=>{c?k():(!S||!n)&&A()},[c,A,S,n,k]);const M=O=>E=>{var F;(F=O.onKeyDown)==null||F.call(O,E),!(E.key!=="Escape"||E.which===229||!T())&&(r||(E.stopPropagation(),l&&l(E,"escapeKeyDown")))},L=O=>E=>{var F;(F=O.onClick)==null||F.call(O,E),E.target===E.currentTarget&&l&&l(E,"backdropClick")};return{getRootProps:(O={})=>{const E=en(e);delete E.onTransitionEnter,delete E.onTransitionExited;const F={...E,...O};return{role:"presentation",...F,onKeyDown:M(F),ref:h}},getBackdropProps:(O={})=>{const E=O;return{"aria-hidden":!0,...E,onClick:L(E),open:c}},getTransitionProps:()=>{const O=()=>{y(!1),s&&s()},E=()=>{y(!0),i&&i(),n&&A()};return{onEnter:Al(O,(a==null?void 0:a.props.onEnter)??rc),onExited:Al(E,(a==null?void 0:a.props.onExited)??rc)}},rootRef:h,portalRef:I,isTopModal:T,exited:b,hasTransition:S}}function Gy(e){return ce("MuiModal",e)}ue("MuiModal",["root","hidden","backdrop"]);const Ky=e=>{const{open:t,exited:r,classes:o}=e;return de({root:["root",!t&&r&&"hidden"],backdrop:["backdrop"]},Gy,o)},qy=j("div",{name:"MuiModal",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,!r.open&&r.exited&&t.hidden]}})(re(({theme:e})=>({position:"fixed",zIndex:(e.vars||e).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:({ownerState:t})=>!t.open&&t.exited,style:{visibility:"hidden"}}]}))),Yy=j(Qu,{name:"MuiModal",slot:"Backdrop"})({zIndex:-1}),Ea=p.forwardRef(function(t,r){const o=me({name:"MuiModal",props:t}),{BackdropComponent:n=Yy,BackdropProps:s,classes:i,className:a,closeAfterTransition:l=!1,children:c,container:u,component:d,components:f={},componentsProps:m={},disableAutoFocus:h=!1,disableEnforceFocus:b=!1,disableEscapeKeyDown:y=!1,disablePortal:S=!1,disableRestoreFocus:$=!1,disableScrollLock:C=!1,hideBackdrop:v=!1,keepMounted:x=!1,onClose:k,onTransitionEnter:T,onTransitionExited:I,open:A,slotProps:M={},slots:L={},theme:g,...P}=o,R={...o,closeAfterTransition:l,disableAutoFocus:h,disableEnforceFocus:b,disableEscapeKeyDown:y,disablePortal:S,disableRestoreFocus:$,disableScrollLock:C,hideBackdrop:v,keepMounted:x},{getRootProps:O,getBackdropProps:E,getTransitionProps:F,portalRef:z,isTopModal:N,exited:Q,hasTransition:_}=_y({...R,rootRef:r}),Z={...R,exited:Q},se=Ky(Z),ye={};if(c.props.tabIndex===void 0&&(ye.tabIndex="-1"),_){const{onEnter:ee,onExited:Ce}=F();ye.onEnter=ee,ye.onExited=Ce}const K={slots:{root:f.Root,backdrop:f.Backdrop,...L},slotProps:{...m,...M}},[q,ie]=le("root",{ref:r,elementType:qy,externalForwardedProps:{...K,...P,component:d},getSlotProps:O,ownerState:Z,className:V(a,se==null?void 0:se.root,!Z.open&&Z.exited&&(se==null?void 0:se.hidden))}),[we,he]=le("backdrop",{ref:s==null?void 0:s.ref,elementType:n,externalForwardedProps:K,shouldForwardComponentProp:!0,additionalProps:s,getSlotProps:ee=>E({...ee,onClick:Ce=>{ee!=null&&ee.onClick&&ee.onClick(Ce)}}),className:V(s==null?void 0:s.className,se==null?void 0:se.backdrop),ownerState:Z});return!x&&!A&&(!_||Q)?null:w.jsx(Yu,{ref:z,container:u,disablePortal:S,children:w.jsxs(q,{...ie,children:[!v&&n?w.jsx(we,{...he}):null,w.jsx(zy,{disableEnforceFocus:b,disableAutoFocus:h,disableRestoreFocus:$,isEnabled:N,open:A,children:p.cloneElement(c,ye)})]})})});function Xy(e){return ce("MuiPaper",e)}const v$=ue("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]),Qy=e=>{const{square:t,elevation:r,variant:o,classes:n}=e,s={root:["root",o,!t&&"rounded",o==="elevation"&&`elevation${r}`]};return de(s,Xy,n)},Zy=j("div",{name:"MuiPaper",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[r.variant],!r.square&&t.rounded,r.variant==="elevation"&&t[`elevation${r.elevation}`]]}})(re(({theme:e})=>({backgroundColor:(e.vars||e).palette.background.paper,color:(e.vars||e).palette.text.primary,transition:e.transitions.create("box-shadow"),variants:[{props:({ownerState:t})=>!t.square,style:{borderRadius:e.shape.borderRadius}},{props:{variant:"outlined"},style:{border:`1px solid ${(e.vars||e).palette.divider}`}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]}))),so=p.forwardRef(function(t,r){var m;const o=me({props:t,name:"MuiPaper"}),n=$r(),{className:s,component:i="div",elevation:a=1,square:l=!1,variant:c="elevation",...u}=o,d={...o,component:i,elevation:a,square:l,variant:c},f=Qy(d);return w.jsx(Zy,{as:i,ownerState:d,className:V(f.root,s),ref:r,...u,style:{...c==="elevation"&&{"--Paper-shadow":(n.vars||n).shadows[a],...n.vars&&{"--Paper-overlay":(m=n.vars.overlays)==null?void 0:m[a]},...!n.vars&&n.palette.mode==="dark"&&{"--Paper-overlay":`linear-gradient(${fn("#fff",Ti(a))}, ${fn("#fff",Ti(a))})`}},...u.style}})});function Jy(e){return ce("MuiPopover",e)}ue("MuiPopover",["root","paper"]);function oc(e,t){let r=0;return typeof t=="number"?r=t:t==="center"?r=e.height/2:t==="bottom"&&(r=e.height),r}function nc(e,t){let r=0;return typeof t=="number"?r=t:t==="center"?r=e.width/2:t==="right"&&(r=e.width),r}function sc(e){return[e.horizontal,e.vertical].map(t=>typeof t=="number"?`${t}px`:t).join(" ")}function zn(e){return typeof e=="function"?e():e}const ev=e=>{const{classes:t}=e;return de({root:["root"],paper:["paper"]},Jy,t)},tv=j(Ea,{name:"MuiPopover",slot:"Root"})({}),Zu=j(so,{name:"MuiPopover",slot:"Paper"})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),rv=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiPopover"}),{action:n,anchorEl:s,anchorOrigin:i={vertical:"top",horizontal:"left"},anchorPosition:a,anchorReference:l="anchorEl",children:c,className:u,container:d,elevation:f=8,marginThreshold:m=16,open:h,PaperProps:b={},slots:y={},slotProps:S={},transformOrigin:$={vertical:"top",horizontal:"left"},TransitionComponent:C,transitionDuration:v="auto",TransitionProps:x={},disableScrollLock:k=!1,...T}=o,I=p.useRef(),A={...o,anchorOrigin:i,anchorReference:l,elevation:f,marginThreshold:m,transformOrigin:$,TransitionComponent:C,transitionDuration:v,TransitionProps:x},M=ev(A),L=p.useCallback(()=>{if(l==="anchorPosition")return a;const ee=zn(s),oe=(ee&&ee.nodeType===1?ee:mt(I.current).body).getBoundingClientRect();return{top:oe.top+oc(oe,i.vertical),left:oe.left+nc(oe,i.horizontal)}},[s,i.horizontal,i.vertical,a,l]),g=p.useCallback(ee=>({vertical:oc(ee,$.vertical),horizontal:nc(ee,$.horizontal)}),[$.horizontal,$.vertical]),P=p.useCallback(ee=>{const Ce={width:ee.offsetWidth,height:ee.offsetHeight},oe=g(Ce);if(l==="none")return{top:null,left:null,transformOrigin:sc(oe)};const ge=L();let ve=ge.top-oe.vertical,Le=ge.left-oe.horizontal;const Me=ve+Ce.height,$e=Le+Ce.width,J=Ut(zn(s)),Ae=J.innerHeight-m,pe=J.innerWidth-m;if(m!==null&&ve<m){const Re=ve-m;ve-=Re,oe.vertical+=Re}else if(m!==null&&Me>Ae){const Re=Me-Ae;ve-=Re,oe.vertical+=Re}if(m!==null&&Le<m){const Re=Le-m;Le-=Re,oe.horizontal+=Re}else if($e>pe){const Re=$e-pe;Le-=Re,oe.horizontal+=Re}return{top:`${Math.round(ve)}px`,left:`${Math.round(Le)}px`,transformOrigin:sc(oe)}},[s,l,L,g,m]),[R,O]=p.useState(h),E=p.useCallback(()=>{const ee=I.current;if(!ee)return;const Ce=P(ee);Ce.top!==null&&ee.style.setProperty("top",Ce.top),Ce.left!==null&&(ee.style.left=Ce.left),ee.style.transformOrigin=Ce.transformOrigin,O(!0)},[P]);p.useEffect(()=>(k&&window.addEventListener("scroll",E),()=>window.removeEventListener("scroll",E)),[s,k,E]);const F=()=>{E()},z=()=>{O(!1)};p.useEffect(()=>{h&&E()}),p.useImperativeHandle(n,()=>h?{updatePosition:()=>{E()}}:null,[h,E]),p.useEffect(()=>{if(!h)return;const ee=Cn(()=>{E()}),Ce=Ut(zn(s));return Ce.addEventListener("resize",ee),()=>{ee.clear(),Ce.removeEventListener("resize",ee)}},[s,h,E]);let N=v;const Q={slots:{transition:C,...y},slotProps:{transition:x,paper:b,...S}},[_,Z]=le("transition",{elementType:ss,externalForwardedProps:Q,ownerState:A,getSlotProps:ee=>({...ee,onEntering:(Ce,oe)=>{var ge;(ge=ee.onEntering)==null||ge.call(ee,Ce,oe),F()},onExited:Ce=>{var oe;(oe=ee.onExited)==null||oe.call(ee,Ce),z()}}),additionalProps:{appear:!0,in:h}});v==="auto"&&!_.muiSupportAuto&&(N=void 0);const se=d||(s?mt(zn(s)).body:void 0),[ye,{slots:K,slotProps:q,...ie}]=le("root",{ref:r,elementType:tv,externalForwardedProps:{...Q,...T},shouldForwardComponentProp:!0,additionalProps:{slots:{backdrop:y.backdrop},slotProps:{backdrop:ha(typeof S.backdrop=="function"?S.backdrop(A):S.backdrop,{invisible:!0})},container:se,open:h},ownerState:A,className:V(M.root,u)}),[we,he]=le("paper",{ref:I,className:M.paper,elementType:Zu,externalForwardedProps:Q,shouldForwardComponentProp:!0,additionalProps:{elevation:f,style:R?void 0:{opacity:0}},ownerState:A});return w.jsx(ye,{...ie,...!yr(ye)&&{slots:K,slotProps:q,disableScrollLock:k},children:w.jsx(_,{...Z,timeout:N,children:w.jsx(we,{...he,children:c})})})}),ov=typeof window<"u"?p.useLayoutEffect:p.useEffect;function nv(e){return ce("MuiDialog",e)}const mi=ue("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]),sv=p.createContext({}),iv=j(Qu,{name:"MuiDialog",slot:"Backdrop",overrides:(e,t)=>t.backdrop})({zIndex:-1}),av=e=>{const{classes:t,scroll:r,maxWidth:o,fullWidth:n,fullScreen:s}=e,i={root:["root"],container:["container",`scroll${B(r)}`],paper:["paper",`paperScroll${B(r)}`,`paperWidth${B(String(o))}`,n&&"paperFullWidth",s&&"paperFullScreen"]};return de(i,nv,t)},lv=j(Ea,{name:"MuiDialog",slot:"Root"})({"@media print":{position:"absolute !important"}}),cv=j("div",{name:"MuiDialog",slot:"Container",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.container,t[`scroll${B(r.scroll)}`]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),uv=j(so,{name:"MuiDialog",slot:"Paper",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.paper,t[`scrollPaper${B(r.scroll)}`],t[`paperWidth${B(String(r.maxWidth))}`],r.fullWidth&&t.paperFullWidth,r.fullScreen&&t.paperFullScreen]}})(re(({theme:e})=>({margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:({ownerState:t})=>!t.maxWidth,style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:e.breakpoints.unit==="px"?Math.max(e.breakpoints.values.xs,444):`max(${e.breakpoints.values.xs}${e.breakpoints.unit}, 444px)`,[`&.${mi.paperScrollBody}`]:{[e.breakpoints.down(Math.max(e.breakpoints.values.xs,444)+64)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(e.breakpoints.values).filter(t=>t!=="xs").map(t=>({props:{maxWidth:t},style:{maxWidth:`${e.breakpoints.values[t]}${e.breakpoints.unit}`,[`&.${mi.paperScrollBody}`]:{[e.breakpoints.down(e.breakpoints.values[t]+64)]:{maxWidth:"calc(100% - 64px)"}}}})),{props:({ownerState:t})=>t.fullWidth,style:{width:"calc(100% - 64px)"}},{props:({ownerState:t})=>t.fullScreen,style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,[`&.${mi.paperScrollBody}`]:{margin:0,maxWidth:"100%"}}}]}))),x$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiDialog"}),n=$r(),s={enter:n.transitions.duration.enteringScreen,exit:n.transitions.duration.leavingScreen},{"aria-describedby":i,"aria-labelledby":a,"aria-modal":l=!0,BackdropComponent:c,BackdropProps:u,children:d,className:f,disableEscapeKeyDown:m=!1,fullScreen:h=!1,fullWidth:b=!1,maxWidth:y="sm",onClick:S,onClose:$,open:C,PaperComponent:v=so,PaperProps:x={},scroll:k="paper",slots:T={},slotProps:I={},TransitionComponent:A=Di,transitionDuration:M=s,TransitionProps:L,...g}=o,P={...o,disableEscapeKeyDown:m,fullScreen:h,fullWidth:b,maxWidth:y,scroll:k},R=av(P),O=p.useRef(),E=ge=>{O.current=ge.target===ge.currentTarget},F=ge=>{S&&S(ge),O.current&&(O.current=null,$&&$(ge,"backdropClick"))},z=xr(a),N=p.useMemo(()=>({titleId:z}),[z]),Q={transition:A,...T},_={transition:L,paper:x,backdrop:u,...I},Z={slots:Q,slotProps:_},[se,ye]=le("root",{elementType:lv,shouldForwardComponentProp:!0,externalForwardedProps:Z,ownerState:P,className:V(R.root,f),ref:r}),[K,q]=le("backdrop",{elementType:iv,shouldForwardComponentProp:!0,externalForwardedProps:Z,ownerState:P}),[ie,we]=le("paper",{elementType:uv,shouldForwardComponentProp:!0,externalForwardedProps:Z,ownerState:P,className:V(R.paper,x.className)}),[he,ee]=le("container",{elementType:cv,externalForwardedProps:Z,ownerState:P,className:R.container}),[Ce,oe]=le("transition",{elementType:Di,externalForwardedProps:Z,ownerState:P,additionalProps:{appear:!0,in:C,timeout:M,role:"presentation"}});return w.jsx(se,{closeAfterTransition:!0,slots:{backdrop:K},slotProps:{backdrop:{transitionDuration:M,as:c,...q}},disableEscapeKeyDown:m,onClose:$,open:C,onClick:F,...ye,...g,children:w.jsx(Ce,{...oe,children:w.jsx(he,{onMouseDown:E,...ee,children:w.jsx(ie,{as:v,elevation:24,role:"dialog","aria-describedby":i,"aria-labelledby":z,"aria-modal":l,...we,children:w.jsx(sv.Provider,{value:N,children:d})})})})})});function dv(e){return ce("MuiDialogContent",e)}ue("MuiDialogContent",["root","dividers"]);const pv=ue("MuiDialogTitle",["root"]),fv=e=>{const{classes:t,dividers:r}=e;return de({root:["root",r&&"dividers"]},dv,t)},mv=j("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.dividers&&t.dividers]}})(re(({theme:e})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:t})=>t.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(e.vars||e).palette.divider}`,borderBottom:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:t})=>!t.dividers,style:{[`.${pv.root} + &`]:{paddingTop:0}}}]}))),S$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiDialogContent"}),{className:n,dividers:s=!1,...i}=o,a={...o,dividers:s},l=fv(a);return w.jsx(mv,{className:V(l.root,n),ownerState:a,ref:r,...i})}),Ls=p.createContext(void 0);function Ar(){return p.useContext(Ls)}function gv(e){return ce("MuiInputAdornment",e)}const ic=ue("MuiInputAdornment",["root","filled","standard","outlined","positionStart","positionEnd","disablePointerEvents","hiddenLabel","sizeSmall"]);var ac;const hv=(e,t)=>{const{ownerState:r}=e;return[t.root,t[`position${B(r.position)}`],r.disablePointerEvents===!0&&t.disablePointerEvents,t[r.variant]]},bv=e=>{const{classes:t,disablePointerEvents:r,hiddenLabel:o,position:n,size:s,variant:i}=e,a={root:["root",r&&"disablePointerEvents",n&&`position${B(n)}`,i,o&&"hiddenLabel",s&&`size${B(s)}`]};return de(a,gv,t)},yv=j("div",{name:"MuiInputAdornment",slot:"Root",overridesResolver:hv})(re(({theme:e})=>({display:"flex",maxHeight:"2em",alignItems:"center",whiteSpace:"nowrap",color:(e.vars||e).palette.action.active,variants:[{props:{variant:"filled"},style:{[`&.${ic.positionStart}&:not(.${ic.hiddenLabel})`]:{marginTop:16}}},{props:{position:"start"},style:{marginRight:8}},{props:{position:"end"},style:{marginLeft:8}},{props:{disablePointerEvents:!0},style:{pointerEvents:"none"}}]}))),C$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiInputAdornment"}),{children:n,className:s,component:i="div",disablePointerEvents:a=!1,disableTypography:l=!1,position:c,variant:u,...d}=o,f=Ar()||{};let m=u;u&&f.variant,f&&!m&&(m=f.variant);const h={...o,hiddenLabel:f.hiddenLabel,size:f.size,disablePointerEvents:a,position:c,variant:m},b=bv(h);return w.jsx(Ls.Provider,{value:null,children:w.jsx(yv,{as:i,ownerState:h,className:V(b.root,s),ref:r,...d,children:typeof n=="string"&&!l?w.jsx(Xt,{color:"textSecondary",children:n}):w.jsxs(p.Fragment,{children:[c==="start"?ac||(ac=w.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):null,n]})})})});var lc;const vv=j("fieldset",{name:"MuiNotchedOutlined",shouldForwardProp:Et})({textAlign:"left",position:"absolute",bottom:0,right:0,top:-5,left:0,margin:0,padding:"0 8px",pointerEvents:"none",borderRadius:"inherit",borderStyle:"solid",borderWidth:1,overflow:"hidden",minWidth:"0%"}),xv=j("legend",{name:"MuiNotchedOutlined",shouldForwardProp:Et})(re(({theme:e})=>({float:"unset",width:"auto",overflow:"hidden",variants:[{props:({ownerState:t})=>!t.withLabel,style:{padding:0,lineHeight:"11px",transition:e.transitions.create("width",{duration:150,easing:e.transitions.easing.easeOut})}},{props:({ownerState:t})=>t.withLabel,style:{display:"block",padding:0,height:11,fontSize:"0.75em",visibility:"hidden",maxWidth:.01,transition:e.transitions.create("max-width",{duration:50,easing:e.transitions.easing.easeOut}),whiteSpace:"nowrap","& > span":{paddingLeft:5,paddingRight:5,display:"inline-block",opacity:0,visibility:"visible"}}},{props:({ownerState:t})=>t.withLabel&&t.notched,style:{maxWidth:"100%",transition:e.transitions.create("max-width",{duration:100,easing:e.transitions.easing.easeOut,delay:50})}}]})));function Sv(e){const{children:t,classes:r,className:o,label:n,notched:s,...i}=e,a=n!=null&&n!=="",l={...e,notched:s,withLabel:a};return w.jsx(vv,{"aria-hidden":!0,className:o,ownerState:l,...i,children:w.jsx(xv,{ownerState:l,children:a?w.jsx("span",{children:n}):lc||(lc=w.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"}))})})}function io({props:e,states:t,muiFormControl:r}){return t.reduce((o,n)=>(o[n]=e[n],r&&typeof e[n]>"u"&&(o[n]=r[n]),o),{})}function Fn(e){return parseInt(e,10)||0}const Cv={shadow:{visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"}};function wv(e){for(const t in e)return!1;return!0}function cc(e){return wv(e)||e.outerHeightStyle===0&&!e.overflowing}const $v=p.forwardRef(function(t,r){const{onChange:o,maxRows:n,minRows:s=1,style:i,value:a,...l}=t,{current:c}=p.useRef(a!=null),u=p.useRef(null),d=ot(r,u),f=p.useRef(null),m=p.useRef(null),h=p.useCallback(()=>{const C=u.current,v=m.current;if(!C||!v)return;const k=Ut(C).getComputedStyle(C);if(k.width==="0px")return{outerHeightStyle:0,overflowing:!1};v.style.width=k.width,v.value=C.value||t.placeholder||"x",v.value.slice(-1)===`
`&&(v.value+=" ");const T=k.boxSizing,I=Fn(k.paddingBottom)+Fn(k.paddingTop),A=Fn(k.borderBottomWidth)+Fn(k.borderTopWidth),M=v.scrollHeight;v.value="x";const L=v.scrollHeight;let g=M;s&&(g=Math.max(Number(s)*L,g)),n&&(g=Math.min(Number(n)*L,g)),g=Math.max(g,L);const P=g+(T==="border-box"?I+A:0),R=Math.abs(g-M)<=1;return{outerHeightStyle:P,overflowing:R}},[n,s,t.placeholder]),b=vt(()=>{const C=u.current,v=h();if(!C||!v||cc(v))return!1;const x=v.outerHeightStyle;return f.current!=null&&f.current!==x}),y=p.useCallback(()=>{const C=u.current,v=h();if(!C||!v||cc(v))return;const x=v.outerHeightStyle;f.current!==x&&(f.current=x,C.style.height=`${x}px`),C.style.overflow=v.overflowing?"hidden":""},[h]),S=p.useRef(-1);It(()=>{const C=Cn(y),v=u==null?void 0:u.current;if(!v)return;const x=Ut(v);x.addEventListener("resize",C);let k;return typeof ResizeObserver<"u"&&(k=new ResizeObserver(()=>{b()&&(k.unobserve(v),cancelAnimationFrame(S.current),y(),S.current=requestAnimationFrame(()=>{k.observe(v)}))}),k.observe(v)),()=>{C.clear(),cancelAnimationFrame(S.current),x.removeEventListener("resize",C),k&&k.disconnect()}},[h,y,b]),It(()=>{y()});const $=C=>{c||y();const v=C.target,x=v.value.length,k=v.value.endsWith(`
`),T=v.selectionStart===x;k&&T&&v.setSelectionRange(x,x),o&&o(C)};return w.jsxs(p.Fragment,{children:[w.jsx("textarea",{value:a,onChange:$,ref:d,rows:s,style:i,...l}),w.jsx("textarea",{"aria-hidden":!0,className:t.className,readOnly:!0,ref:m,tabIndex:-1,style:{...Cv.shadow,...i,paddingTop:0,paddingBottom:0}})]})});function uc(e){return e!=null&&!(Array.isArray(e)&&e.length===0)}function as(e,t=!1){return e&&(uc(e.value)&&e.value!==""||t&&uc(e.defaultValue)&&e.defaultValue!=="")}function kv(e){return e.startAdornment}function Rv(e){return ce("MuiInputBase",e)}const jt=ue("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]);var dc;const Bs=(e,t)=>{const{ownerState:r}=e;return[t.root,r.formControl&&t.formControl,r.startAdornment&&t.adornedStart,r.endAdornment&&t.adornedEnd,r.error&&t.error,r.size==="small"&&t.sizeSmall,r.multiline&&t.multiline,r.color&&t[`color${B(r.color)}`],r.fullWidth&&t.fullWidth,r.hiddenLabel&&t.hiddenLabel]},Ns=(e,t)=>{const{ownerState:r}=e;return[t.input,r.size==="small"&&t.inputSizeSmall,r.multiline&&t.inputMultiline,r.type==="search"&&t.inputTypeSearch,r.startAdornment&&t.inputAdornedStart,r.endAdornment&&t.inputAdornedEnd,r.hiddenLabel&&t.inputHiddenLabel]},Pv=e=>{const{classes:t,color:r,disabled:o,error:n,endAdornment:s,focused:i,formControl:a,fullWidth:l,hiddenLabel:c,multiline:u,readOnly:d,size:f,startAdornment:m,type:h}=e,b={root:["root",`color${B(r)}`,o&&"disabled",n&&"error",l&&"fullWidth",i&&"focused",a&&"formControl",f&&f!=="medium"&&`size${B(f)}`,u&&"multiline",m&&"adornedStart",s&&"adornedEnd",c&&"hiddenLabel",d&&"readOnly"],input:["input",o&&"disabled",h==="search"&&"inputTypeSearch",u&&"inputMultiline",f==="small"&&"inputSizeSmall",c&&"inputHiddenLabel",m&&"inputAdornedStart",s&&"inputAdornedEnd",d&&"readOnly"]};return de(b,Rv,t)},js=j("div",{name:"MuiInputBase",slot:"Root",overridesResolver:Bs})(re(({theme:e})=>({...e.typography.body1,color:(e.vars||e).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",[`&.${jt.disabled}`]:{color:(e.vars||e).palette.text.disabled,cursor:"default"},variants:[{props:({ownerState:t})=>t.multiline,style:{padding:"4px 0 5px"}},{props:({ownerState:t,size:r})=>t.multiline&&r==="small",style:{paddingTop:1}},{props:({ownerState:t})=>t.fullWidth,style:{width:"100%"}}]}))),zs=j("input",{name:"MuiInputBase",slot:"Input",overridesResolver:Ns})(re(({theme:e})=>{const t=e.palette.mode==="light",r={color:"currentColor",...e.vars?{opacity:e.vars.opacity.inputPlaceholder}:{opacity:t?.42:.5},transition:e.transitions.create("opacity",{duration:e.transitions.duration.shorter})},o={opacity:"0 !important"},n=e.vars?{opacity:e.vars.opacity.inputPlaceholder}:{opacity:t?.42:.5};return{font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%","&::-webkit-input-placeholder":r,"&::-moz-placeholder":r,"&::-ms-input-placeholder":r,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},[`label[data-shrink=false] + .${jt.formControl} &`]:{"&::-webkit-input-placeholder":o,"&::-moz-placeholder":o,"&::-ms-input-placeholder":o,"&:focus::-webkit-input-placeholder":n,"&:focus::-moz-placeholder":n,"&:focus::-ms-input-placeholder":n},[`&.${jt.disabled}`]:{opacity:1,WebkitTextFillColor:(e.vars||e).palette.text.disabled},variants:[{props:({ownerState:s})=>!s.disableInjectingGlobalStyles,style:{animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}}},{props:{size:"small"},style:{paddingTop:1}},{props:({ownerState:s})=>s.multiline,style:{height:"auto",resize:"none",padding:0,paddingTop:0}},{props:{type:"search"},style:{MozAppearance:"textfield"}}]}})),pc=ga({"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}),Fs=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiInputBase"}),{"aria-describedby":n,autoComplete:s,autoFocus:i,className:a,color:l,components:c={},componentsProps:u={},defaultValue:d,disabled:f,disableInjectingGlobalStyles:m,endAdornment:h,error:b,fullWidth:y=!1,id:S,inputComponent:$="input",inputProps:C={},inputRef:v,margin:x,maxRows:k,minRows:T,multiline:I=!1,name:A,onBlur:M,onChange:L,onClick:g,onFocus:P,onKeyDown:R,onKeyUp:O,placeholder:E,readOnly:F,renderSuffix:z,rows:N,size:Q,slotProps:_={},slots:Z={},startAdornment:se,type:ye="text",value:K,...q}=o,ie=C.value!=null?C.value:K,{current:we}=p.useRef(ie!=null),he=p.useRef(),ee=p.useCallback(G=>{},[]),Ce=ot(he,v,C.ref,ee),[oe,ge]=p.useState(!1),ve=Ar(),Le=io({props:o,muiFormControl:ve,states:["color","disabled","error","hiddenLabel","size","required","filled"]});Le.focused=ve?ve.focused:oe,p.useEffect(()=>{!ve&&f&&oe&&(ge(!1),M&&M())},[ve,f,oe,M]);const Me=ve&&ve.onFilled,$e=ve&&ve.onEmpty,J=p.useCallback(G=>{as(G)?Me&&Me():$e&&$e()},[Me,$e]);It(()=>{we&&J({value:ie})},[ie,J,we]);const Ae=G=>{P&&P(G),C.onFocus&&C.onFocus(G),ve&&ve.onFocus?ve.onFocus(G):ge(!0)},pe=G=>{M&&M(G),C.onBlur&&C.onBlur(G),ve&&ve.onBlur?ve.onBlur(G):ge(!1)},Re=(G,...Pe)=>{if(!we){const Ie=G.target||he.current;if(Ie==null)throw new Error(Ir(1));J({value:Ie.value})}C.onChange&&C.onChange(G,...Pe),L&&L(G,...Pe)};p.useEffect(()=>{J(he.current)},[]);const te=G=>{he.current&&G.currentTarget===G.target&&he.current.focus(),g&&g(G)};let Ge=$,je=C;I&&Ge==="input"&&(N?je={type:void 0,minRows:N,maxRows:N,...je}:je={type:void 0,maxRows:k,minRows:T,...je},Ge=$v);const H=G=>{J(G.animationName==="mui-auto-fill-cancel"?he.current:{value:"x"})};p.useEffect(()=>{ve&&ve.setAdornedStart(!!se)},[ve,se]);const Y={...o,color:Le.color||"primary",disabled:Le.disabled,endAdornment:h,error:Le.error,focused:Le.focused,formControl:ve,fullWidth:y,hiddenLabel:Le.hiddenLabel,multiline:I,size:Le.size,startAdornment:se,type:ye},ae=Pv(Y),xe=Z.root||c.Root||js,Te=_.root||u.root||{},X=Z.input||c.Input||zs;return je={...je,..._.input??u.input},w.jsxs(p.Fragment,{children:[!m&&typeof pc=="function"&&(dc||(dc=w.jsx(pc,{}))),w.jsxs(xe,{...Te,ref:r,onClick:te,...q,...!yr(xe)&&{ownerState:{...Y,...Te.ownerState}},className:V(ae.root,Te.className,a,F&&"MuiInputBase-readOnly"),children:[se,w.jsx(Ls.Provider,{value:null,children:w.jsx(X,{"aria-invalid":Le.error,"aria-describedby":n,autoComplete:s,autoFocus:i,defaultValue:d,disabled:Le.disabled,id:S,onAnimationStart:H,name:A,placeholder:E,readOnly:F,required:Le.required,rows:N,value:ie,onKeyDown:R,onKeyUp:O,type:ye,...je,...!yr(X)&&{as:Ge,ownerState:{...Y,...je.ownerState}},ref:Ce,className:V(ae.input,je.className,F&&"MuiInputBase-readOnly"),onBlur:pe,onChange:Re,onFocus:Ae})}),h,z?z({...Le,startAdornment:se}):null]})]})});function Tv(e){return ce("MuiOutlinedInput",e)}const _t={...jt,...ue("MuiOutlinedInput",["root","notchedOutline","input"])},Iv=e=>{const{classes:t}=e,o=de({root:["root"],notchedOutline:["notchedOutline"],input:["input"]},Tv,t);return{...t,...o}},Ev=j(js,{shouldForwardProp:e=>Et(e)||e==="classes",name:"MuiOutlinedInput",slot:"Root",overridesResolver:Bs})(re(({theme:e})=>{const t=e.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{position:"relative",borderRadius:(e.vars||e).shape.borderRadius,[`&:hover .${_t.notchedOutline}`]:{borderColor:(e.vars||e).palette.text.primary},"@media (hover: none)":{[`&:hover .${_t.notchedOutline}`]:{borderColor:e.vars?e.alpha(e.vars.palette.common.onBackground,.23):t}},[`&.${_t.focused} .${_t.notchedOutline}`]:{borderWidth:2},variants:[...Object.entries(e.palette).filter(et()).map(([r])=>({props:{color:r},style:{[`&.${_t.focused} .${_t.notchedOutline}`]:{borderColor:(e.vars||e).palette[r].main}}})),{props:{},style:{[`&.${_t.error} .${_t.notchedOutline}`]:{borderColor:(e.vars||e).palette.error.main},[`&.${_t.disabled} .${_t.notchedOutline}`]:{borderColor:(e.vars||e).palette.action.disabled}}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:14}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:14}},{props:({ownerState:r})=>r.multiline,style:{padding:"16.5px 14px"}},{props:({ownerState:r,size:o})=>r.multiline&&o==="small",style:{padding:"8.5px 14px"}}]}})),Mv=j(Sv,{name:"MuiOutlinedInput",slot:"NotchedOutline"})(re(({theme:e})=>{const t=e.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{borderColor:e.vars?e.alpha(e.vars.palette.common.onBackground,.23):t}})),Av=j(zs,{name:"MuiOutlinedInput",slot:"Input",overridesResolver:Ns})(re(({theme:e})=>({padding:"16.5px 14px",...!e.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:e.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:e.palette.mode==="light"?null:"#fff",caretColor:e.palette.mode==="light"?null:"#fff",borderRadius:"inherit"}},...e.vars&&{"&:-webkit-autofill":{borderRadius:"inherit"},[e.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{padding:"8.5px 14px"}},{props:({ownerState:t})=>t.multiline,style:{padding:0}},{props:({ownerState:t})=>t.startAdornment,style:{paddingLeft:0}},{props:({ownerState:t})=>t.endAdornment,style:{paddingRight:0}}]}))),Ma=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiOutlinedInput"}),{components:n={},fullWidth:s=!1,inputComponent:i="input",label:a,multiline:l=!1,notched:c,slots:u={},slotProps:d={},type:f="text",...m}=o,h=Iv(o),b=Ar(),y=io({props:o,muiFormControl:b,states:["color","disabled","error","focused","hiddenLabel","size","required"]}),S={...o,color:y.color||"primary",disabled:y.disabled,error:y.error,focused:y.focused,formControl:b,fullWidth:s,hiddenLabel:y.hiddenLabel,multiline:l,size:y.size,type:f},$=u.root??n.Root??Ev,C=u.input??n.Input??Av,[v,x]=le("notchedOutline",{elementType:Mv,className:h.notchedOutline,shouldForwardComponentProp:!0,ownerState:S,externalForwardedProps:{slots:u,slotProps:d},additionalProps:{label:a!=null&&a!==""&&y.required?w.jsxs(p.Fragment,{children:[a," ","*"]}):a}});return w.jsx(Fs,{slots:{root:$,input:C},slotProps:d,renderSuffix:k=>w.jsx(v,{...x,notched:typeof c<"u"?c:!!(k.startAdornment||k.filled||k.focused)}),fullWidth:s,inputComponent:i,multiline:l,ref:r,type:f,...m,classes:{...h,notchedOutline:null}})});Ma.muiName="Input";function Ov(e){return ce("MuiLink",e)}const Lv=ue("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]),Bv=({theme:e,ownerState:t})=>{const r=t.color;if("colorSpace"in e&&e.colorSpace){const s=mr(e,`palette.${r}.main`)||mr(e,`palette.${r}`)||t.color;return e.alpha(s,.4)}const o=mr(e,`palette.${r}.main`,!1)||mr(e,`palette.${r}`,!1)||t.color,n=mr(e,`palette.${r}.mainChannel`)||mr(e,`palette.${r}Channel`);return"vars"in e&&n?`rgba(${n} / 0.4)`:fn(o,.4)},fc={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},Nv=e=>{const{classes:t,component:r,focusVisible:o,underline:n}=e,s={root:["root",`underline${B(n)}`,r==="button"&&"button",o&&"focusVisible"]};return de(s,Ov,t)},jv=j(Xt,{name:"MuiLink",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[`underline${B(r.underline)}`],r.component==="button"&&t.button]}})(re(({theme:e})=>({variants:[{props:{underline:"none"},style:{textDecoration:"none"}},{props:{underline:"hover"},style:{textDecoration:"none","&:hover":{textDecoration:"underline"}}},{props:{underline:"always"},style:{textDecoration:"underline","&:hover":{textDecorationColor:"inherit"}}},{props:({underline:t,ownerState:r})=>t==="always"&&r.color!=="inherit",style:{textDecorationColor:"var(--Link-underlineColor)"}},{props:({underline:t,ownerState:r})=>t==="always"&&r.color==="inherit",style:e.colorSpace?{textDecorationColor:e.alpha("currentColor",.4)}:null},...Object.entries(e.palette).filter(et()).map(([t])=>({props:{underline:"always",color:t},style:{"--Link-underlineColor":e.alpha((e.vars||e).palette[t].main,.4)}})),{props:{underline:"always",color:"textPrimary"},style:{"--Link-underlineColor":e.alpha((e.vars||e).palette.text.primary,.4)}},{props:{underline:"always",color:"textSecondary"},style:{"--Link-underlineColor":e.alpha((e.vars||e).palette.text.secondary,.4)}},{props:{underline:"always",color:"textDisabled"},style:{"--Link-underlineColor":(e.vars||e).palette.text.disabled}},{props:{component:"button"},style:{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},[`&.${Lv.focusVisible}`]:{outline:"auto"}}}]}))),w$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiLink"}),n=$r(),{className:s,color:i="primary",component:a="a",onBlur:l,onFocus:c,TypographyClasses:u,underline:d="always",variant:f="inherit",sx:m,...h}=o,[b,y]=p.useState(!1),S=x=>{Vr(x.target)||y(!1),l&&l(x)},$=x=>{Vr(x.target)&&y(!0),c&&c(x)},C={...o,color:i,component:a,focusVisible:b,underline:d,variant:f},v=Nv(C);return w.jsx(jv,{color:i,className:V(v.root,s),classes:u,component:a,onBlur:S,onFocus:$,ref:r,ownerState:C,variant:f,...h,sx:[...fc[i]===void 0?[{color:i}]:[],...Array.isArray(m)?m:[m]],style:{...h.style,...d==="always"&&i!=="inherit"&&!fc[i]&&{"--Link-underlineColor":Bv({theme:n,ownerState:C})}}})}),er=p.createContext({});function zv(e){return ce("MuiList",e)}ue("MuiList",["root","padding","dense","subheader"]);const Fv=e=>{const{classes:t,disablePadding:r,dense:o,subheader:n}=e;return de({root:["root",!r&&"padding",o&&"dense",n&&"subheader"]},zv,t)},Dv=j("ul",{name:"MuiList",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,!r.disablePadding&&t.padding,r.dense&&t.dense,r.subheader&&t.subheader]}})({listStyle:"none",margin:0,padding:0,position:"relative",variants:[{props:({ownerState:e})=>!e.disablePadding,style:{paddingTop:8,paddingBottom:8}},{props:({ownerState:e})=>e.subheader,style:{paddingTop:0}}]}),Wv=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiList"}),{children:n,className:s,component:i="ul",dense:a=!1,disablePadding:l=!1,subheader:c,...u}=o,d=p.useMemo(()=>({dense:a}),[a]),f={...o,component:i,dense:a,disablePadding:l},m=Fv(f);return w.jsx(er.Provider,{value:d,children:w.jsxs(Dv,{as:i,className:V(m.root,s),ref:r,ownerState:f,...u,children:[c,n]})})});function Uv(e){return ce("MuiListItem",e)}ue("MuiListItem",["root","container","dense","alignItemsFlexStart","divider","gutters","padding","secondaryAction"]);function Hv(e){return ce("MuiListItemButton",e)}const yo=ue("MuiListItemButton",["root","focusVisible","dense","alignItemsFlexStart","disabled","divider","gutters","selected"]),Vv=(e,t)=>{const{ownerState:r}=e;return[t.root,r.dense&&t.dense,r.alignItems==="flex-start"&&t.alignItemsFlexStart,r.divider&&t.divider,!r.disableGutters&&t.gutters]},_v=e=>{const{alignItems:t,classes:r,dense:o,disabled:n,disableGutters:s,divider:i,selected:a}=e,c=de({root:["root",o&&"dense",!s&&"gutters",i&&"divider",n&&"disabled",t==="flex-start"&&"alignItemsFlexStart",a&&"selected"]},Hv,r);return{...r,...c}},Gv=j(Sr,{shouldForwardProp:e=>Et(e)||e==="classes",name:"MuiListItemButton",slot:"Root",overridesResolver:Vv})(re(({theme:e})=>({display:"flex",flexGrow:1,justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minWidth:0,boxSizing:"border-box",textAlign:"left",paddingTop:8,paddingBottom:8,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${yo.selected}`]:{backgroundColor:e.alpha((e.vars||e).palette.primary.main,(e.vars||e).palette.action.selectedOpacity),[`&.${yo.focusVisible}`]:{backgroundColor:e.alpha((e.vars||e).palette.primary.main,`${(e.vars||e).palette.action.selectedOpacity} + ${(e.vars||e).palette.action.focusOpacity}`)}},[`&.${yo.selected}:hover`]:{backgroundColor:e.alpha((e.vars||e).palette.primary.main,`${(e.vars||e).palette.action.selectedOpacity} + ${(e.vars||e).palette.action.hoverOpacity}`),"@media (hover: none)":{backgroundColor:e.alpha((e.vars||e).palette.primary.main,(e.vars||e).palette.action.selectedOpacity)}},[`&.${yo.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`&.${yo.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity},variants:[{props:({ownerState:t})=>t.divider,style:{borderBottom:`1px solid ${(e.vars||e).palette.divider}`,backgroundClip:"padding-box"}},{props:{alignItems:"flex-start"},style:{alignItems:"flex-start"}},{props:({ownerState:t})=>!t.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:t})=>t.dense,style:{paddingTop:4,paddingBottom:4}}]}))),$$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiListItemButton"}),{alignItems:n="center",autoFocus:s=!1,component:i="div",children:a,dense:l=!1,disableGutters:c=!1,divider:u=!1,focusVisibleClassName:d,selected:f=!1,className:m,...h}=o,b=p.useContext(er),y=p.useMemo(()=>({dense:l||b.dense||!1,alignItems:n,disableGutters:c}),[n,b.dense,l,c]),S=p.useRef(null);It(()=>{s&&S.current&&S.current.focus()},[s]);const $={...o,alignItems:n,dense:y.dense,disableGutters:c,divider:u,selected:f},C=_v($),v=ot(S,r);return w.jsx(er.Provider,{value:y,children:w.jsx(Gv,{ref:v,href:h.href||h.to,component:(h.href||h.to)&&i==="div"?"button":i,focusVisibleClassName:V(C.focusVisible,d),ownerState:$,className:V(C.root,m),...h,classes:C,children:a})})});function Kv(e){return ce("MuiListItemSecondaryAction",e)}ue("MuiListItemSecondaryAction",["root","disableGutters"]);const qv=e=>{const{disableGutters:t,classes:r}=e;return de({root:["root",t&&"disableGutters"]},Kv,r)},Yv=j("div",{name:"MuiListItemSecondaryAction",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.disableGutters&&t.disableGutters]}})({position:"absolute",right:16,top:"50%",transform:"translateY(-50%)",variants:[{props:({ownerState:e})=>e.disableGutters,style:{right:0}}]}),Ju=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiListItemSecondaryAction"}),{className:n,...s}=o,i=p.useContext(er),a={...o,disableGutters:i.disableGutters},l=qv(a);return w.jsx(Yv,{className:V(l.root,n),ownerState:a,ref:r,...s})});Ju.muiName="ListItemSecondaryAction";const Xv=(e,t)=>{const{ownerState:r}=e;return[t.root,r.dense&&t.dense,r.alignItems==="flex-start"&&t.alignItemsFlexStart,r.divider&&t.divider,!r.disableGutters&&t.gutters,!r.disablePadding&&t.padding,r.hasSecondaryAction&&t.secondaryAction]},Qv=e=>{const{alignItems:t,classes:r,dense:o,disableGutters:n,disablePadding:s,divider:i,hasSecondaryAction:a}=e;return de({root:["root",o&&"dense",!n&&"gutters",!s&&"padding",i&&"divider",t==="flex-start"&&"alignItemsFlexStart",a&&"secondaryAction"],container:["container"]},Uv,r)},Zv=j("div",{name:"MuiListItem",slot:"Root",overridesResolver:Xv})(re(({theme:e})=>({display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",width:"100%",boxSizing:"border-box",textAlign:"left",variants:[{props:({ownerState:t})=>!t.disablePadding,style:{paddingTop:8,paddingBottom:8}},{props:({ownerState:t})=>!t.disablePadding&&t.dense,style:{paddingTop:4,paddingBottom:4}},{props:({ownerState:t})=>!t.disablePadding&&!t.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:t})=>!t.disablePadding&&!!t.secondaryAction,style:{paddingRight:48}},{props:({ownerState:t})=>!!t.secondaryAction,style:{[`& > .${yo.root}`]:{paddingRight:48}}},{props:{alignItems:"flex-start"},style:{alignItems:"flex-start"}},{props:({ownerState:t})=>t.divider,style:{borderBottom:`1px solid ${(e.vars||e).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:t})=>t.button,style:{transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}}}},{props:({ownerState:t})=>t.hasSecondaryAction,style:{paddingRight:48}}]}))),Jv=j("li",{name:"MuiListItem",slot:"Container"})({position:"relative"}),k$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiListItem"}),{alignItems:n="center",children:s,className:i,component:a,components:l={},componentsProps:c={},ContainerComponent:u="li",ContainerProps:{className:d,...f}={},dense:m=!1,disableGutters:h=!1,disablePadding:b=!1,divider:y=!1,secondaryAction:S,slotProps:$={},slots:C={},...v}=o,x=p.useContext(er),k=p.useMemo(()=>({dense:m||x.dense||!1,alignItems:n,disableGutters:h}),[n,x.dense,m,h]),T=p.useRef(null),I=p.Children.toArray(s),A=I.length&&Jo(I[I.length-1],["ListItemSecondaryAction"]),M={...o,alignItems:n,dense:k.dense,disableGutters:h,disablePadding:b,divider:y,hasSecondaryAction:A},L=Qv(M),g=ot(T,r),P=C.root||l.Root||Zv,R=$.root||c.root||{},O={className:V(L.root,R.className,i),...v};let E=a||"li";return A?(E=!O.component&&!a?"div":E,u==="li"&&(E==="li"?E="div":O.component==="li"&&(O.component="div")),w.jsx(er.Provider,{value:k,children:w.jsxs(Jv,{as:u,className:V(L.container,d),ref:g,ownerState:M,...f,children:[w.jsx(P,{...R,...!yr(P)&&{as:E,ownerState:{...M,...R.ownerState}},...O,children:I}),I.pop()]})})):w.jsx(er.Provider,{value:k,children:w.jsxs(P,{...R,as:E,ref:g,...!yr(P)&&{ownerState:{...M,...R.ownerState}},...O,children:[I,S&&w.jsx(Ju,{children:S})]})})});function e0(e){return ce("MuiListItemAvatar",e)}ue("MuiListItemAvatar",["root","alignItemsFlexStart"]);const t0=e=>{const{alignItems:t,classes:r}=e;return de({root:["root",t==="flex-start"&&"alignItemsFlexStart"]},e0,r)},r0=j("div",{name:"MuiListItemAvatar",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.alignItems==="flex-start"&&t.alignItemsFlexStart]}})({minWidth:56,flexShrink:0,variants:[{props:{alignItems:"flex-start"},style:{marginTop:8}}]}),R$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiListItemAvatar"}),{className:n,...s}=o,i=p.useContext(er),a={...o,alignItems:i.alignItems},l=t0(a);return w.jsx(r0,{className:V(l.root,n),ownerState:a,ref:r,...s})});function o0(e){return ce("MuiListItemText",e)}const So=ue("MuiListItemText",["root","multiline","dense","inset","primary","secondary"]),n0=e=>{const{classes:t,inset:r,primary:o,secondary:n,dense:s}=e;return de({root:["root",r&&"inset",s&&"dense",o&&n&&"multiline"],primary:["primary"],secondary:["secondary"]},o0,t)},s0=j("div",{name:"MuiListItemText",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[{[`& .${So.primary}`]:t.primary},{[`& .${So.secondary}`]:t.secondary},t.root,r.inset&&t.inset,r.primary&&r.secondary&&t.multiline,r.dense&&t.dense]}})({flex:"1 1 auto",minWidth:0,marginTop:4,marginBottom:4,[`.${ns.root}:where(& .${So.primary})`]:{display:"block"},[`.${ns.root}:where(& .${So.secondary})`]:{display:"block"},variants:[{props:({ownerState:e})=>e.primary&&e.secondary,style:{marginTop:6,marginBottom:6}},{props:({ownerState:e})=>e.inset,style:{paddingLeft:56}}]}),P$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiListItemText"}),{children:n,className:s,disableTypography:i=!1,inset:a=!1,primary:l,primaryTypographyProps:c,secondary:u,secondaryTypographyProps:d,slots:f={},slotProps:m={},...h}=o,{dense:b}=p.useContext(er);let y=l??n,S=u;const $={...o,disableTypography:i,inset:a,primary:!!y,secondary:!!S,dense:b},C=n0($),v={slots:f,slotProps:{primary:c,secondary:d,...m}},[x,k]=le("root",{className:V(C.root,s),elementType:s0,externalForwardedProps:{...v,...h},ownerState:$,ref:r}),[T,I]=le("primary",{className:C.primary,elementType:Xt,externalForwardedProps:v,ownerState:$}),[A,M]=le("secondary",{className:C.secondary,elementType:Xt,externalForwardedProps:v,ownerState:$});return y!=null&&y.type!==Xt&&!i&&(y=w.jsx(T,{variant:b?"body2":"body1",component:I!=null&&I.variant?void 0:"span",...I,children:y})),S!=null&&S.type!==Xt&&!i&&(S=w.jsx(A,{variant:"body2",color:"textSecondary",...M,children:S})),w.jsxs(x,{...k,children:[y,S]})});function i0(e){return ce("MuiListItemIcon",e)}const mc=ue("MuiListItemIcon",["root","alignItemsFlexStart"]),a0=e=>{const{alignItems:t,classes:r}=e;return de({root:["root",t==="flex-start"&&"alignItemsFlexStart"]},i0,r)},l0=j("div",{name:"MuiListItemIcon",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.alignItems==="flex-start"&&t.alignItemsFlexStart]}})(re(({theme:e})=>({minWidth:56,color:(e.vars||e).palette.action.active,flexShrink:0,display:"inline-flex",variants:[{props:{alignItems:"flex-start"},style:{marginTop:8}}]}))),T$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiListItemIcon"}),{className:n,...s}=o,i=p.useContext(er),a={...o,alignItems:i.alignItems},l=a0(a);return w.jsx(l0,{className:V(l.root,n),ownerState:a,ref:r,...s})});function gi(e,t,r){return e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:r?null:e.firstChild}function gc(e,t,r){return e===t?r?e.firstChild:e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:r?null:e.lastChild}function ed(e,t){if(t===void 0)return!0;let r=e.innerText;return r===void 0&&(r=e.textContent),r=r.trim().toLowerCase(),r.length===0?!1:t.repeating?r[0]===t.keys[0]:r.startsWith(t.keys.join(""))}function _o(e,t,r,o,n,s){let i=!1,a=n(e,t,t?r:!1);for(;a;){if(a===e.firstChild){if(i)return!1;i=!0}const l=o?!1:a.disabled||a.getAttribute("aria-disabled")==="true";if(!a.hasAttribute("tabindex")||!ed(a,s)||l)a=n(e,a,r);else return a.focus(),!0}return!1}const c0=p.forwardRef(function(t,r){const{actions:o,autoFocus:n=!1,autoFocusItem:s=!1,children:i,className:a,disabledItemsFocusable:l=!1,disableListWrap:c=!1,onKeyDown:u,variant:d="selectedMenu",...f}=t,m=p.useRef(null),h=p.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});It(()=>{n&&m.current.focus()},[n]),p.useImperativeHandle(o,()=>({adjustStyleForScrollbar:(C,{direction:v})=>{const x=!m.current.style.width;if(C.clientHeight<m.current.clientHeight&&x){const k=`${Xu(Ut(C))}px`;m.current.style[v==="rtl"?"paddingLeft":"paddingRight"]=k,m.current.style.width=`calc(100% + ${k})`}return m.current}}),[]);const b=C=>{const v=m.current,x=C.key;if(C.ctrlKey||C.metaKey||C.altKey){u&&u(C);return}const T=mt(v).activeElement;if(x==="ArrowDown")C.preventDefault(),_o(v,T,c,l,gi);else if(x==="ArrowUp")C.preventDefault(),_o(v,T,c,l,gc);else if(x==="Home")C.preventDefault(),_o(v,null,c,l,gi);else if(x==="End")C.preventDefault(),_o(v,null,c,l,gc);else if(x.length===1){const I=h.current,A=x.toLowerCase(),M=performance.now();I.keys.length>0&&(M-I.lastTime>500?(I.keys=[],I.repeating=!0,I.previousKeyMatched=!0):I.repeating&&A!==I.keys[0]&&(I.repeating=!1)),I.lastTime=M,I.keys.push(A);const L=T&&!I.repeating&&ed(T,I);I.previousKeyMatched&&(L||_o(v,T,!1,l,gi,I))?C.preventDefault():I.previousKeyMatched=!1}u&&u(C)},y=ot(m,r);let S=-1;p.Children.forEach(i,(C,v)=>{if(!p.isValidElement(C)){S===v&&(S+=1,S>=i.length&&(S=-1));return}C.props.disabled||(d==="selectedMenu"&&C.props.selected||S===-1)&&(S=v),S===v&&(C.props.disabled||C.props.muiSkipListHighlight||C.type.muiSkipListHighlight)&&(S+=1,S>=i.length&&(S=-1))});const $=p.Children.map(i,(C,v)=>{if(v===S){const x={};return s&&(x.autoFocus=!0),C.props.tabIndex===void 0&&d==="selectedMenu"&&(x.tabIndex=0),p.cloneElement(C,x)}return C});return w.jsx(Wv,{role:"menu",ref:y,className:a,onKeyDown:b,tabIndex:n?0:-1,...f,children:$})});function u0(e){return ce("MuiMenu",e)}ue("MuiMenu",["root","paper","list"]);const d0={vertical:"top",horizontal:"right"},p0={vertical:"top",horizontal:"left"},f0=e=>{const{classes:t}=e;return de({root:["root"],paper:["paper"],list:["list"]},u0,t)},m0=j(rv,{shouldForwardProp:e=>Et(e)||e==="classes",name:"MuiMenu",slot:"Root"})({}),g0=j(Zu,{name:"MuiMenu",slot:"Paper"})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),h0=j(c0,{name:"MuiMenu",slot:"List"})({outline:0}),b0=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiMenu"}),{autoFocus:n=!0,children:s,className:i,disableAutoFocusItem:a=!1,MenuListProps:l={},onClose:c,open:u,PaperProps:d={},PopoverClasses:f,transitionDuration:m="auto",TransitionProps:{onEntering:h,...b}={},variant:y="selectedMenu",slots:S={},slotProps:$={},...C}=o,v=Mr(),x={...o,autoFocus:n,disableAutoFocusItem:a,MenuListProps:l,onEntering:h,PaperProps:d,transitionDuration:m,TransitionProps:b,variant:y},k=f0(x),T=n&&!a&&u,I=p.useRef(null),A=(N,Q)=>{I.current&&I.current.adjustStyleForScrollbar(N,{direction:v?"rtl":"ltr"}),h&&h(N,Q)},M=N=>{N.key==="Tab"&&(N.preventDefault(),c&&c(N,"tabKeyDown"))};let L=-1;p.Children.map(s,(N,Q)=>{p.isValidElement(N)&&(N.props.disabled||(y==="selectedMenu"&&N.props.selected||L===-1)&&(L=Q))});const g={slots:S,slotProps:{list:l,transition:b,paper:d,...$}},P=Ot({elementType:S.root,externalSlotProps:$.root,ownerState:x,className:[k.root,i]}),[R,O]=le("paper",{className:k.paper,elementType:g0,externalForwardedProps:g,shouldForwardComponentProp:!0,ownerState:x}),[E,F]=le("list",{className:V(k.list,l.className),elementType:h0,shouldForwardComponentProp:!0,externalForwardedProps:g,getSlotProps:N=>({...N,onKeyDown:Q=>{var _;M(Q),(_=N.onKeyDown)==null||_.call(N,Q)}}),ownerState:x}),z=typeof g.slotProps.transition=="function"?g.slotProps.transition(x):g.slotProps.transition;return w.jsx(m0,{onClose:c,anchorOrigin:{vertical:"bottom",horizontal:v?"right":"left"},transformOrigin:v?d0:p0,slots:{root:S.root,paper:R,backdrop:S.backdrop,...S.transition&&{transition:S.transition}},slotProps:{root:P,paper:O,backdrop:typeof $.backdrop=="function"?$.backdrop(x):$.backdrop,transition:{...z,onEntering:(...N)=>{var Q;A(...N),(Q=z==null?void 0:z.onEntering)==null||Q.call(z,...N)}}},open:u,ref:r,transitionDuration:m,ownerState:x,...C,classes:f,children:w.jsx(E,{actions:I,autoFocus:n&&(L===-1||a),autoFocusItem:T,variant:y,...F,children:s})})});function y0(e){return ce("MuiMenuItem",e)}const Go=ue("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]),v0=(e,t)=>{const{ownerState:r}=e;return[t.root,r.dense&&t.dense,r.divider&&t.divider,!r.disableGutters&&t.gutters]},x0=e=>{const{disabled:t,dense:r,divider:o,disableGutters:n,selected:s,classes:i}=e,l=de({root:["root",r&&"dense",t&&"disabled",!n&&"gutters",o&&"divider",s&&"selected"]},y0,i);return{...i,...l}},S0=j(Sr,{shouldForwardProp:e=>Et(e)||e==="classes",name:"MuiMenuItem",slot:"Root",overridesResolver:v0})(re(({theme:e})=>({...e.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Go.selected}`]:{backgroundColor:e.alpha((e.vars||e).palette.primary.main,(e.vars||e).palette.action.selectedOpacity),[`&.${Go.focusVisible}`]:{backgroundColor:e.alpha((e.vars||e).palette.primary.main,`${(e.vars||e).palette.action.selectedOpacity} + ${(e.vars||e).palette.action.focusOpacity}`)}},[`&.${Go.selected}:hover`]:{backgroundColor:e.alpha((e.vars||e).palette.primary.main,`${(e.vars||e).palette.action.selectedOpacity} + ${(e.vars||e).palette.action.hoverOpacity}`),"@media (hover: none)":{backgroundColor:e.alpha((e.vars||e).palette.primary.main,(e.vars||e).palette.action.selectedOpacity)}},[`&.${Go.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`&.${Go.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity},[`& + .${Wl.root}`]:{marginTop:e.spacing(1),marginBottom:e.spacing(1)},[`& + .${Wl.inset}`]:{marginLeft:52},[`& .${So.root}`]:{marginTop:0,marginBottom:0},[`& .${So.inset}`]:{paddingLeft:36},[`& .${mc.root}`]:{minWidth:36},variants:[{props:({ownerState:t})=>!t.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:t})=>t.divider,style:{borderBottom:`1px solid ${(e.vars||e).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:t})=>!t.dense,style:{[e.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:({ownerState:t})=>t.dense,style:{minHeight:32,paddingTop:4,paddingBottom:4,...e.typography.body2,[`& .${mc.root} svg`]:{fontSize:"1.25rem"}}}]}))),C0=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiMenuItem"}),{autoFocus:n=!1,component:s="li",dense:i=!1,divider:a=!1,disableGutters:l=!1,focusVisibleClassName:c,role:u="menuitem",tabIndex:d,className:f,...m}=o,h=p.useContext(er),b=p.useMemo(()=>({dense:i||h.dense||!1,disableGutters:l}),[h.dense,i,l]),y=p.useRef(null);It(()=>{n&&y.current&&y.current.focus()},[n]);const S={...o,dense:b.dense,divider:a,disableGutters:l},$=x0(o),C=ot(y,r);let v;return o.disabled||(v=d!==void 0?d:-1),w.jsx(er.Provider,{value:b,children:w.jsx(S0,{ref:C,role:u,tabIndex:v,component:s,focusVisibleClassName:V($.focusVisible,c),className:V($.root,f),...m,ownerState:S,classes:$})})});function w0(e,t,r){const o=t.getBoundingClientRect(),n=r&&r.getBoundingClientRect(),s=Ut(t);let i;if(t.fakeTransform)i=t.fakeTransform;else{const c=s.getComputedStyle(t);i=c.getPropertyValue("-webkit-transform")||c.getPropertyValue("transform")}let a=0,l=0;if(i&&i!=="none"&&typeof i=="string"){const c=i.split("(")[1].split(")")[0].split(",");a=parseInt(c[4],10),l=parseInt(c[5],10)}return e==="left"?n?`translateX(${n.right+a-o.left}px)`:`translateX(${s.innerWidth+a-o.left}px)`:e==="right"?n?`translateX(-${o.right-n.left-a}px)`:`translateX(-${o.left+o.width-a}px)`:e==="up"?n?`translateY(${n.bottom+l-o.top}px)`:`translateY(${s.innerHeight+l-o.top}px)`:n?`translateY(-${o.top-n.top+o.height-l}px)`:`translateY(-${o.top+o.height-l}px)`}function $0(e){return typeof e=="function"?e():e}function Dn(e,t,r){const o=$0(r),n=w0(e,t,o);n&&(t.style.webkitTransform=n,t.style.transform=n)}const k0=p.forwardRef(function(t,r){const o=$r(),n={enter:o.transitions.easing.easeOut,exit:o.transitions.easing.sharp},s={enter:o.transitions.duration.enteringScreen,exit:o.transitions.duration.leavingScreen},{addEndListener:i,appear:a=!0,children:l,container:c,direction:u="down",easing:d=n,in:f,onEnter:m,onEntered:h,onEntering:b,onExit:y,onExited:S,onExiting:$,style:C,timeout:v=s,TransitionComponent:x=nr,...k}=t,T=p.useRef(null),I=ot(Gr(l),T,r),A=z=>N=>{z&&(N===void 0?z(T.current):z(T.current,N))},M=A((z,N)=>{Dn(u,z,c),Ca(z),m&&m(z,N)}),L=A((z,N)=>{const Q=To({timeout:v,style:C,easing:d},{mode:"enter"});z.style.webkitTransition=o.transitions.create("-webkit-transform",{...Q}),z.style.transition=o.transitions.create("transform",{...Q}),z.style.webkitTransform="none",z.style.transform="none",b&&b(z,N)}),g=A(h),P=A($),R=A(z=>{const N=To({timeout:v,style:C,easing:d},{mode:"exit"});z.style.webkitTransition=o.transitions.create("-webkit-transform",N),z.style.transition=o.transitions.create("transform",N),Dn(u,z,c),y&&y(z)}),O=A(z=>{z.style.webkitTransition="",z.style.transition="",S&&S(z)}),E=z=>{i&&i(T.current,z)},F=p.useCallback(()=>{T.current&&Dn(u,T.current,c)},[u,c]);return p.useEffect(()=>{if(f||u==="down"||u==="right")return;const z=Cn(()=>{T.current&&Dn(u,T.current,c)}),N=Ut(T.current);return N.addEventListener("resize",z),()=>{z.clear(),N.removeEventListener("resize",z)}},[u,f,c]),p.useEffect(()=>{f||F()},[f,F]),w.jsx(x,{nodeRef:T,onEnter:M,onEntered:g,onEntering:L,onExit:R,onExited:O,onExiting:P,addEndListener:E,appear:a,in:f,timeout:v,...k,children:(z,{ownerState:N,...Q})=>p.cloneElement(l,{ref:I,style:{visibility:z==="exited"&&!f?"hidden":void 0,...C,...l.props.style},...Q})})});function R0(e){return ce("MuiDrawer",e)}ue("MuiDrawer",["root","docked","paper","anchorLeft","anchorRight","anchorTop","anchorBottom","paperAnchorLeft","paperAnchorRight","paperAnchorTop","paperAnchorBottom","paperAnchorDockedLeft","paperAnchorDockedRight","paperAnchorDockedTop","paperAnchorDockedBottom","modal"]);const td=(e,t)=>{const{ownerState:r}=e;return[t.root,(r.variant==="permanent"||r.variant==="persistent")&&t.docked,t.modal]},P0=e=>{const{classes:t,anchor:r,variant:o}=e,n={root:["root",`anchor${B(r)}`],docked:[(o==="permanent"||o==="persistent")&&"docked"],modal:["modal"],paper:["paper",`paperAnchor${B(r)}`,o!=="temporary"&&`paperAnchorDocked${B(r)}`]};return de(n,R0,t)},T0=j(Ea,{name:"MuiDrawer",slot:"Root",overridesResolver:td})(re(({theme:e})=>({zIndex:(e.vars||e).zIndex.drawer}))),I0=j("div",{shouldForwardProp:Et,name:"MuiDrawer",slot:"Docked",skipVariantsResolver:!1,overridesResolver:td})({flex:"0 0 auto"}),E0=j(so,{name:"MuiDrawer",slot:"Paper",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.paper,t[`paperAnchor${B(r.anchor)}`],r.variant!=="temporary"&&t[`paperAnchorDocked${B(r.anchor)}`]]}})(re(({theme:e})=>({overflowY:"auto",display:"flex",flexDirection:"column",height:"100%",flex:"1 0 auto",zIndex:(e.vars||e).zIndex.drawer,WebkitOverflowScrolling:"touch",position:"fixed",top:0,outline:0,variants:[{props:{anchor:"left"},style:{left:0}},{props:{anchor:"top"},style:{top:0,left:0,right:0,height:"auto",maxHeight:"100%"}},{props:{anchor:"right"},style:{right:0}},{props:{anchor:"bottom"},style:{top:"auto",left:0,bottom:0,right:0,height:"auto",maxHeight:"100%"}},{props:({ownerState:t})=>t.anchor==="left"&&t.variant!=="temporary",style:{borderRight:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:t})=>t.anchor==="top"&&t.variant!=="temporary",style:{borderBottom:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:t})=>t.anchor==="right"&&t.variant!=="temporary",style:{borderLeft:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:t})=>t.anchor==="bottom"&&t.variant!=="temporary",style:{borderTop:`1px solid ${(e.vars||e).palette.divider}`}}]}))),rd={left:"right",right:"left",top:"down",bottom:"up"};function M0(e){return["left","right"].includes(e)}function A0({direction:e},t){return e==="rtl"&&M0(t)?rd[t]:t}const I$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiDrawer"}),n=$r(),s=Mr(),i={enter:n.transitions.duration.enteringScreen,exit:n.transitions.duration.leavingScreen},{anchor:a="left",BackdropProps:l,children:c,className:u,elevation:d=16,hideBackdrop:f=!1,ModalProps:{BackdropProps:m,...h}={},onClose:b,open:y=!1,PaperProps:S={},SlideProps:$,TransitionComponent:C,transitionDuration:v=i,variant:x="temporary",slots:k={},slotProps:T={},...I}=o,A=p.useRef(!1);p.useEffect(()=>{A.current=!0},[]);const M=A0({direction:s?"rtl":"ltr"},a),g={...o,anchor:a,elevation:d,open:y,variant:x,...I},P=P0(g),R={slots:{transition:C,...k},slotProps:{paper:S,transition:$,...T,backdrop:ha(T.backdrop||{...l,...m},{transitionDuration:v})}},[O,E]=le("root",{ref:r,elementType:T0,className:V(P.root,P.modal,u),shouldForwardComponentProp:!0,ownerState:g,externalForwardedProps:{...R,...I,...h},additionalProps:{open:y,onClose:b,hideBackdrop:f,slots:{backdrop:R.slots.backdrop},slotProps:{backdrop:R.slotProps.backdrop}}}),[F,z]=le("paper",{elementType:E0,shouldForwardComponentProp:!0,className:V(P.paper,S.className),ownerState:g,externalForwardedProps:R,additionalProps:{elevation:x==="temporary"?d:0,square:!0,...x==="temporary"&&{role:"dialog","aria-modal":"true"}}}),[N,Q]=le("docked",{elementType:I0,ref:r,className:V(P.root,P.docked,u),ownerState:g,externalForwardedProps:R,additionalProps:I}),[_,Z]=le("transition",{elementType:k0,ownerState:g,externalForwardedProps:R,additionalProps:{in:y,direction:rd[M],timeout:v,appear:A.current}}),se=w.jsx(F,{...z,children:c});if(x==="permanent")return w.jsx(N,{...Q,children:se});const ye=w.jsx(_,{...Z,children:se});return x==="persistent"?w.jsx(N,{...Q,children:ye}):w.jsx(O,{...E,children:ye})}),Wi=typeof ga({})=="function",O0=(e,t)=>({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%",...t&&!e.vars&&{colorScheme:e.palette.mode}}),L0=e=>({color:(e.vars||e).palette.text.primary,...e.typography.body1,backgroundColor:(e.vars||e).palette.background.default,"@media print":{backgroundColor:(e.vars||e).palette.common.white}}),od=(e,t=!1)=>{var s,i;const r={};t&&e.colorSchemes&&typeof e.getColorSchemeSelector=="function"&&Object.entries(e.colorSchemes).forEach(([a,l])=>{var u,d;const c=e.getColorSchemeSelector(a);c.startsWith("@")?r[c]={":root":{colorScheme:(u=l.palette)==null?void 0:u.mode}}:r[c.replace(/\s*&/,"")]={colorScheme:(d=l.palette)==null?void 0:d.mode}});let o={html:O0(e,t),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:e.typography.fontWeightBold},body:{margin:0,...L0(e),"&::backdrop":{backgroundColor:(e.vars||e).palette.background.default}},...r};const n=(i=(s=e.components)==null?void 0:s.MuiCssBaseline)==null?void 0:i.styleOverrides;return n&&(o=[o,n]),o},Qn="mui-ecs",B0=e=>{const t=od(e,!1),r=Array.isArray(t)?t[0]:t;return!e.vars&&r&&(r.html[`:root:has(${Qn})`]={colorScheme:e.palette.mode}),e.colorSchemes&&Object.entries(e.colorSchemes).forEach(([o,n])=>{var i,a;const s=e.getColorSchemeSelector(o);s.startsWith("@")?r[s]={[`:root:not(:has(.${Qn}))`]:{colorScheme:(i=n.palette)==null?void 0:i.mode}}:r[s.replace(/\s*&/,"")]={[`&:not(:has(.${Qn}))`]:{colorScheme:(a=n.palette)==null?void 0:a.mode}}}),t},N0=ga(Wi?({theme:e,enableColorScheme:t})=>od(e,t):({theme:e})=>B0(e));function E$(e){const t=me({props:e,name:"MuiCssBaseline"}),{children:r,enableColorScheme:o=!1}=t;return w.jsxs(p.Fragment,{children:[Wi&&w.jsx(N0,{enableColorScheme:o}),!Wi&&!o&&w.jsx("span",{className:Qn,style:{display:"none"}}),r]})}const j0=p.createContext(),z0=p.createContext();function F0(e){return ce("MuiTableCell",e)}const D0=ue("MuiTableCell",["root","head","body","footer","sizeSmall","sizeMedium","paddingCheckbox","paddingNone","alignLeft","alignCenter","alignRight","alignJustify","stickyHeader"]),W0=e=>{const{classes:t,variant:r,align:o,padding:n,size:s,stickyHeader:i}=e,a={root:["root",r,i&&"stickyHeader",o!=="inherit"&&`align${B(o)}`,n!=="normal"&&`padding${B(n)}`,`size${B(s)}`]};return de(a,F0,t)},U0=j("td",{name:"MuiTableCell",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[r.variant],t[`size${B(r.size)}`],r.padding!=="normal"&&t[`padding${B(r.padding)}`],r.align!=="inherit"&&t[`align${B(r.align)}`],r.stickyHeader&&t.stickyHeader]}})(re(({theme:e})=>({...e.typography.body2,display:"table-cell",verticalAlign:"inherit",borderBottom:e.vars?`1px solid ${e.vars.palette.TableCell.border}`:`1px solid
    ${e.palette.mode==="light"?e.lighten(e.alpha(e.palette.divider,1),.88):e.darken(e.alpha(e.palette.divider,1),.68)}`,textAlign:"left",padding:16,variants:[{props:{variant:"head"},style:{color:(e.vars||e).palette.text.primary,lineHeight:e.typography.pxToRem(24),fontWeight:e.typography.fontWeightMedium}},{props:{variant:"body"},style:{color:(e.vars||e).palette.text.primary}},{props:{variant:"footer"},style:{color:(e.vars||e).palette.text.secondary,lineHeight:e.typography.pxToRem(21),fontSize:e.typography.pxToRem(12)}},{props:{size:"small"},style:{padding:"6px 16px",[`&.${D0.paddingCheckbox}`]:{width:24,padding:"0 12px 0 16px","& > *":{padding:0}}}},{props:{padding:"checkbox"},style:{width:48,padding:"0 0 0 4px"}},{props:{padding:"none"},style:{padding:0}},{props:{align:"left"},style:{textAlign:"left"}},{props:{align:"center"},style:{textAlign:"center"}},{props:{align:"right"},style:{textAlign:"right",flexDirection:"row-reverse"}},{props:{align:"justify"},style:{textAlign:"justify"}},{props:({ownerState:t})=>t.stickyHeader,style:{position:"sticky",top:0,zIndex:2,backgroundColor:(e.vars||e).palette.background.default}}]}))),Ui=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiTableCell"}),{align:n="inherit",className:s,component:i,padding:a,scope:l,size:c,sortDirection:u,variant:d,...f}=o,m=p.useContext(j0),h=p.useContext(z0),b=h&&h.variant==="head";let y;i?y=i:y=b?"th":"td";let S=l;y==="td"?S=void 0:!S&&b&&(S="col");const $=d||h&&h.variant,C={...o,align:n,component:y,padding:a||(m&&m.padding?m.padding:"normal"),size:c||(m&&m.size?m.size:"medium"),sortDirection:u,stickyHeader:$==="head"&&m&&m.stickyHeader,variant:$},v=W0(C);let x=null;return u&&(x=u==="asc"?"ascending":"descending"),w.jsx(U0,{as:y,ref:r,className:V(v.root,s),"aria-sort":x,scope:S,ownerState:C,...f})}),M$=ue("MuiTableRow",["root","selected","hover","head","footer"]);function H0(e){return ce("MuiTab",e)}const Vt=ue("MuiTab",["root","labelIcon","textColorInherit","textColorPrimary","textColorSecondary","selected","disabled","fullWidth","wrapped","iconWrapper","icon"]),V0=e=>{const{classes:t,textColor:r,fullWidth:o,wrapped:n,icon:s,label:i,selected:a,disabled:l}=e,c={root:["root",s&&i&&"labelIcon",`textColor${B(r)}`,o&&"fullWidth",n&&"wrapped",a&&"selected",l&&"disabled"],icon:["iconWrapper","icon"]};return de(c,H0,t)},_0=j(Sr,{name:"MuiTab",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.label&&r.icon&&t.labelIcon,t[`textColor${B(r.textColor)}`],r.fullWidth&&t.fullWidth,r.wrapped&&t.wrapped,{[`& .${Vt.iconWrapper}`]:t.iconWrapper},{[`& .${Vt.icon}`]:t.icon}]}})(re(({theme:e})=>({...e.typography.button,maxWidth:360,minWidth:90,position:"relative",minHeight:48,flexShrink:0,padding:"12px 16px",overflow:"hidden",whiteSpace:"normal",textAlign:"center",lineHeight:1.25,variants:[{props:({ownerState:t})=>t.label&&(t.iconPosition==="top"||t.iconPosition==="bottom"),style:{flexDirection:"column"}},{props:({ownerState:t})=>t.label&&t.iconPosition!=="top"&&t.iconPosition!=="bottom",style:{flexDirection:"row"}},{props:({ownerState:t})=>t.icon&&t.label,style:{minHeight:72,paddingTop:9,paddingBottom:9}},{props:({ownerState:t,iconPosition:r})=>t.icon&&t.label&&r==="top",style:{[`& > .${Vt.icon}`]:{marginBottom:6}}},{props:({ownerState:t,iconPosition:r})=>t.icon&&t.label&&r==="bottom",style:{[`& > .${Vt.icon}`]:{marginTop:6}}},{props:({ownerState:t,iconPosition:r})=>t.icon&&t.label&&r==="start",style:{[`& > .${Vt.icon}`]:{marginRight:e.spacing(1)}}},{props:({ownerState:t,iconPosition:r})=>t.icon&&t.label&&r==="end",style:{[`& > .${Vt.icon}`]:{marginLeft:e.spacing(1)}}},{props:{textColor:"inherit"},style:{color:"inherit",opacity:.6,[`&.${Vt.selected}`]:{opacity:1},[`&.${Vt.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity}}},{props:{textColor:"primary"},style:{color:(e.vars||e).palette.text.secondary,[`&.${Vt.selected}`]:{color:(e.vars||e).palette.primary.main},[`&.${Vt.disabled}`]:{color:(e.vars||e).palette.text.disabled}}},{props:{textColor:"secondary"},style:{color:(e.vars||e).palette.text.secondary,[`&.${Vt.selected}`]:{color:(e.vars||e).palette.secondary.main},[`&.${Vt.disabled}`]:{color:(e.vars||e).palette.text.disabled}}},{props:({ownerState:t})=>t.fullWidth,style:{flexShrink:1,flexGrow:1,flexBasis:0,maxWidth:"none"}},{props:({ownerState:t})=>t.wrapped,style:{fontSize:e.typography.pxToRem(12)}}]}))),A$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiTab"}),{className:n,disabled:s=!1,disableFocusRipple:i=!1,fullWidth:a,icon:l,iconPosition:c="top",indicator:u,label:d,onChange:f,onClick:m,onFocus:h,selected:b,selectionFollowsFocus:y,textColor:S="inherit",value:$,wrapped:C=!1,...v}=o,x={...o,disabled:s,disableFocusRipple:i,selected:b,icon:!!l,iconPosition:c,label:!!d,fullWidth:a,textColor:S,wrapped:C},k=V0(x),T=l&&d&&p.isValidElement(l)?p.cloneElement(l,{className:V(k.icon,l.props.className)}):l,I=M=>{!b&&f&&f(M,$),m&&m(M)},A=M=>{y&&!b&&f&&f(M,$),h&&h(M)};return w.jsxs(_0,{focusRipple:!i,className:V(k.root,n),ref:r,role:"tab","aria-selected":b,disabled:s,onClick:I,onFocus:A,ownerState:x,tabIndex:b?0:-1,...v,children:[c==="top"||c==="start"?w.jsxs(p.Fragment,{children:[T,d]}):w.jsxs(p.Fragment,{children:[d,T]}),u]})});function G0(e){return(1+Math.sin(Math.PI*e-Math.PI/2))/2}function K0(e,t,r,o={},n=()=>{}){const{ease:s=G0,duration:i=300}=o;let a=null;const l=t[e];let c=!1;const u=()=>{c=!0},d=f=>{if(c){n(new Error("Animation cancelled"));return}a===null&&(a=f);const m=Math.min(1,(f-a)/i);if(t[e]=s(m)*(r-l)+l,m>=1){requestAnimationFrame(()=>{n(null)});return}requestAnimationFrame(d)};return l===r?(n(new Error("Element already at target position")),u):(requestAnimationFrame(d),u)}const q0={width:99,height:99,position:"absolute",top:-9999,overflow:"scroll"};function Y0(e){const{onChange:t,...r}=e,o=p.useRef(),n=p.useRef(null),s=()=>{o.current=n.current.offsetHeight-n.current.clientHeight};return It(()=>{const i=Cn(()=>{const l=o.current;s(),l!==o.current&&t(o.current)}),a=Ut(n.current);return a.addEventListener("resize",i),()=>{i.clear(),a.removeEventListener("resize",i)}},[t]),p.useEffect(()=>{s(),t(o.current)},[t]),w.jsx("div",{style:q0,...r,ref:n})}const nd=ur(w.jsx("path",{d:"M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"})),sd=ur(w.jsx("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}));function X0(e){return ce("MuiTabScrollButton",e)}const Q0=ue("MuiTabScrollButton",["root","vertical","horizontal","disabled"]),Z0=e=>{const{classes:t,orientation:r,disabled:o}=e;return de({root:["root",r,o&&"disabled"]},X0,t)},J0=j(Sr,{name:"MuiTabScrollButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.orientation&&t[r.orientation]]}})({width:40,flexShrink:0,opacity:.8,[`&.${Q0.disabled}`]:{opacity:0},variants:[{props:{orientation:"vertical"},style:{width:"100%",height:40,"& svg":{transform:"var(--TabScrollButton-svgRotate)"}}}]}),ex=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiTabScrollButton"}),{className:n,slots:s={},slotProps:i={},direction:a,orientation:l,disabled:c,...u}=o,d=Mr(),f={isRtl:d,...o},m=Z0(f),h=s.StartScrollButtonIcon??nd,b=s.EndScrollButtonIcon??sd,y=Ot({elementType:h,externalSlotProps:i.startScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:f}),S=Ot({elementType:b,externalSlotProps:i.endScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:f});return w.jsx(J0,{component:"div",className:V(m.root,n),ref:r,role:null,ownerState:f,tabIndex:null,...u,style:{...u.style,...l==="vertical"&&{"--TabScrollButton-svgRotate":`rotate(${d?-90:90}deg)`}},children:a==="left"?w.jsx(h,{...y}):w.jsx(b,{...S})})});function tx(e){return ce("MuiTabs",e)}const hi=ue("MuiTabs",["root","vertical","list","flexContainer","flexContainerVertical","centered","scroller","fixed","scrollableX","scrollableY","hideScrollbar","scrollButtons","scrollButtonsHideMobile","indicator"]),hc=(e,t)=>e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:e.firstChild,bc=(e,t)=>e===t?e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:e.lastChild,Wn=(e,t,r)=>{let o=!1,n=r(e,t);for(;n;){if(n===e.firstChild){if(o)return;o=!0}const s=n.disabled||n.getAttribute("aria-disabled")==="true";if(!n.hasAttribute("tabindex")||s)n=r(e,n);else{n.focus();return}}},rx=e=>{const{vertical:t,fixed:r,hideScrollbar:o,scrollableX:n,scrollableY:s,centered:i,scrollButtonsHideMobile:a,classes:l}=e;return de({root:["root",t&&"vertical"],scroller:["scroller",r&&"fixed",o&&"hideScrollbar",n&&"scrollableX",s&&"scrollableY"],list:["list","flexContainer",t&&"flexContainerVertical",t&&"vertical",i&&"centered"],indicator:["indicator"],scrollButtons:["scrollButtons",a&&"scrollButtonsHideMobile"],scrollableX:[n&&"scrollableX"],hideScrollbar:[o&&"hideScrollbar"]},tx,l)},ox=j("div",{name:"MuiTabs",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[{[`& .${hi.scrollButtons}`]:t.scrollButtons},{[`& .${hi.scrollButtons}`]:r.scrollButtonsHideMobile&&t.scrollButtonsHideMobile},t.root,r.vertical&&t.vertical]}})(re(({theme:e})=>({overflow:"hidden",minHeight:48,WebkitOverflowScrolling:"touch",display:"flex",variants:[{props:({ownerState:t})=>t.vertical,style:{flexDirection:"column"}},{props:({ownerState:t})=>t.scrollButtonsHideMobile,style:{[`& .${hi.scrollButtons}`]:{[e.breakpoints.down("sm")]:{display:"none"}}}}]}))),nx=j("div",{name:"MuiTabs",slot:"Scroller",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.scroller,r.fixed&&t.fixed,r.hideScrollbar&&t.hideScrollbar,r.scrollableX&&t.scrollableX,r.scrollableY&&t.scrollableY]}})({position:"relative",display:"inline-block",flex:"1 1 auto",whiteSpace:"nowrap",variants:[{props:({ownerState:e})=>e.fixed,style:{overflowX:"hidden",width:"100%"}},{props:({ownerState:e})=>e.hideScrollbar,style:{scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}},{props:({ownerState:e})=>e.scrollableX,style:{overflowX:"auto",overflowY:"hidden"}},{props:({ownerState:e})=>e.scrollableY,style:{overflowY:"auto",overflowX:"hidden"}}]}),sx=j("div",{name:"MuiTabs",slot:"List",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.list,t.flexContainer,r.vertical&&t.flexContainerVertical,r.centered&&t.centered]}})({display:"flex",variants:[{props:({ownerState:e})=>e.vertical,style:{flexDirection:"column"}},{props:({ownerState:e})=>e.centered,style:{justifyContent:"center"}}]}),ix=j("span",{name:"MuiTabs",slot:"Indicator"})(re(({theme:e})=>({position:"absolute",height:2,bottom:0,width:"100%",transition:e.transitions.create(),variants:[{props:{indicatorColor:"primary"},style:{backgroundColor:(e.vars||e).palette.primary.main}},{props:{indicatorColor:"secondary"},style:{backgroundColor:(e.vars||e).palette.secondary.main}},{props:({ownerState:t})=>t.vertical,style:{height:"100%",width:2,right:0}}]}))),ax=j(Y0)({overflowX:"auto",overflowY:"hidden",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}),yc={},O$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiTabs"}),n=$r(),s=Mr(),{"aria-label":i,"aria-labelledby":a,action:l,centered:c=!1,children:u,className:d,component:f="div",allowScrollButtonsMobile:m=!1,indicatorColor:h="primary",onChange:b,orientation:y="horizontal",ScrollButtonComponent:S,scrollButtons:$="auto",selectionFollowsFocus:C,slots:v={},slotProps:x={},TabIndicatorProps:k={},TabScrollButtonProps:T={},textColor:I="primary",value:A,variant:M="standard",visibleScrollbar:L=!1,...g}=o,P=M==="scrollable",R=y==="vertical",O=R?"scrollTop":"scrollLeft",E=R?"top":"left",F=R?"bottom":"right",z=R?"clientHeight":"clientWidth",N=R?"height":"width",Q={...o,component:f,allowScrollButtonsMobile:m,indicatorColor:h,orientation:y,vertical:R,scrollButtons:$,textColor:I,variant:M,visibleScrollbar:L,fixed:!P,hideScrollbar:P&&!L,scrollableX:P&&!R,scrollableY:P&&R,centered:c&&!P,scrollButtonsHideMobile:!m},_=rx(Q),Z=Ot({elementType:v.StartScrollButtonIcon,externalSlotProps:x.startScrollButtonIcon,ownerState:Q}),se=Ot({elementType:v.EndScrollButtonIcon,externalSlotProps:x.endScrollButtonIcon,ownerState:Q}),[ye,K]=p.useState(!1),[q,ie]=p.useState(yc),[we,he]=p.useState(!1),[ee,Ce]=p.useState(!1),[oe,ge]=p.useState(!1),[ve,Le]=p.useState({overflow:"hidden",scrollbarWidth:0}),Me=new Map,$e=p.useRef(null),J=p.useRef(null),Ae={slots:v,slotProps:{indicator:k,scrollButton:T,...x}},pe=()=>{const ne=$e.current;let Se;if(ne){const We=ne.getBoundingClientRect();Se={clientWidth:ne.clientWidth,scrollLeft:ne.scrollLeft,scrollTop:ne.scrollTop,scrollWidth:ne.scrollWidth,top:We.top,bottom:We.bottom,left:We.left,right:We.right}}let De;if(ne&&A!==!1){const We=J.current.children;if(We.length>0){const Ze=We[Me.get(A)];De=Ze?Ze.getBoundingClientRect():null}}return{tabsMeta:Se,tabMeta:De}},Re=vt(()=>{const{tabsMeta:ne,tabMeta:Se}=pe();let De=0,We;R?(We="top",Se&&ne&&(De=Se.top-ne.top+ne.scrollTop)):(We=s?"right":"left",Se&&ne&&(De=(s?-1:1)*(Se[We]-ne[We]+ne.scrollLeft)));const Ze={[We]:De,[N]:Se?Se[N]:0};if(typeof q[We]!="number"||typeof q[N]!="number")ie(Ze);else{const At=Math.abs(q[We]-Ze[We]),ir=Math.abs(q[N]-Ze[N]);(At>=1||ir>=1)&&ie(Ze)}}),te=(ne,{animation:Se=!0}={})=>{Se?K0(O,$e.current,ne,{duration:n.transitions.duration.standard}):$e.current[O]=ne},Ge=ne=>{let Se=$e.current[O];R?Se+=ne:Se+=ne*(s?-1:1),te(Se)},je=()=>{const ne=$e.current[z];let Se=0;const De=Array.from(J.current.children);for(let We=0;We<De.length;We+=1){const Ze=De[We];if(Se+Ze[z]>ne){We===0&&(Se=ne);break}Se+=Ze[z]}return Se},H=()=>{Ge(-1*je())},Y=()=>{Ge(je())},[ae,{onChange:xe,...Te}]=le("scrollbar",{className:V(_.scrollableX,_.hideScrollbar),elementType:ax,shouldForwardComponentProp:!0,externalForwardedProps:Ae,ownerState:Q}),X=p.useCallback(ne=>{xe==null||xe(ne),Le({overflow:null,scrollbarWidth:ne})},[xe]),[G,Pe]=le("scrollButtons",{className:V(_.scrollButtons,T.className),elementType:ex,externalForwardedProps:Ae,ownerState:Q,additionalProps:{orientation:y,slots:{StartScrollButtonIcon:v.startScrollButtonIcon||v.StartScrollButtonIcon,EndScrollButtonIcon:v.endScrollButtonIcon||v.EndScrollButtonIcon},slotProps:{startScrollButtonIcon:Z,endScrollButtonIcon:se}}}),Ie=()=>{const ne={};ne.scrollbarSizeListener=P?w.jsx(ae,{...Te,onChange:X}):null;const De=P&&($==="auto"&&(we||ee)||$===!0);return ne.scrollButtonStart=De?w.jsx(G,{direction:s?"right":"left",onClick:H,disabled:!we,...Pe}):null,ne.scrollButtonEnd=De?w.jsx(G,{direction:s?"left":"right",onClick:Y,disabled:!ee,...Pe}):null,ne},ke=vt(ne=>{const{tabsMeta:Se,tabMeta:De}=pe();if(!(!De||!Se)){if(De[E]<Se[E]){const We=Se[O]+(De[E]-Se[E]);te(We,{animation:ne})}else if(De[F]>Se[F]){const We=Se[O]+(De[F]-Se[F]);te(We,{animation:ne})}}}),dt=vt(()=>{P&&$!==!1&&ge(!oe)});p.useEffect(()=>{const ne=Cn(()=>{$e.current&&Re()});let Se;const De=At=>{At.forEach(ir=>{ir.removedNodes.forEach(kr=>{Se==null||Se.unobserve(kr)}),ir.addedNodes.forEach(kr=>{Se==null||Se.observe(kr)})}),ne(),dt()},We=Ut($e.current);We.addEventListener("resize",ne);let Ze;return typeof ResizeObserver<"u"&&(Se=new ResizeObserver(ne),Array.from(J.current.children).forEach(At=>{Se.observe(At)})),typeof MutationObserver<"u"&&(Ze=new MutationObserver(De),Ze.observe(J.current,{childList:!0})),()=>{ne.clear(),We.removeEventListener("resize",ne),Ze==null||Ze.disconnect(),Se==null||Se.disconnect()}},[Re,dt]),p.useEffect(()=>{const ne=Array.from(J.current.children),Se=ne.length;if(typeof IntersectionObserver<"u"&&Se>0&&P&&$!==!1){const De=ne[0],We=ne[Se-1],Ze={root:$e.current,threshold:.99},At=Rr=>{he(!Rr[0].isIntersecting)},ir=new IntersectionObserver(At,Ze);ir.observe(De);const kr=Rr=>{Ce(!Rr[0].isIntersecting)},ao=new IntersectionObserver(kr,Ze);return ao.observe(We),()=>{ir.disconnect(),ao.disconnect()}}},[P,$,oe,u==null?void 0:u.length]),p.useEffect(()=>{K(!0)},[]),p.useEffect(()=>{Re()}),p.useEffect(()=>{ke(yc!==q)},[ke,q]),p.useImperativeHandle(l,()=>({updateIndicator:Re,updateScrollButtons:dt}),[Re,dt]);const[bt,nt]=le("indicator",{className:V(_.indicator,k.className),elementType:ix,externalForwardedProps:Ae,ownerState:Q,additionalProps:{style:q}}),kt=w.jsx(bt,{...nt});let be=0;const Fe=p.Children.map(u,ne=>{if(!p.isValidElement(ne))return null;const Se=ne.props.value===void 0?be:ne.props.value;Me.set(Se,be);const De=Se===A;return be+=1,p.cloneElement(ne,{fullWidth:M==="fullWidth",indicator:De&&!ye&&kt,selected:De,selectionFollowsFocus:C,onChange:b,textColor:I,value:Se,...be===1&&A===!1&&!ne.props.tabIndex?{tabIndex:0}:{}})}),He=ne=>{if(ne.altKey||ne.shiftKey||ne.ctrlKey||ne.metaKey)return;const Se=J.current,De=mt(Se).activeElement;if(De.getAttribute("role")!=="tab")return;let Ze=y==="horizontal"?"ArrowLeft":"ArrowUp",At=y==="horizontal"?"ArrowRight":"ArrowDown";switch(y==="horizontal"&&s&&(Ze="ArrowRight",At="ArrowLeft"),ne.key){case Ze:ne.preventDefault(),Wn(Se,De,bc);break;case At:ne.preventDefault(),Wn(Se,De,hc);break;case"Home":ne.preventDefault(),Wn(Se,null,hc);break;case"End":ne.preventDefault(),Wn(Se,null,bc);break}},Ne=Ie(),[Ve,Rt]=le("root",{ref:r,className:V(_.root,d),elementType:ox,externalForwardedProps:{...Ae,...g,component:f},ownerState:Q}),[St,st]=le("scroller",{ref:$e,className:_.scroller,elementType:nx,externalForwardedProps:Ae,ownerState:Q,additionalProps:{style:{overflow:ve.overflow,[R?`margin${s?"Left":"Right"}`:"marginBottom"]:L?void 0:-ve.scrollbarWidth}}}),[Oe,pt]=le("list",{ref:J,className:V(_.list,_.flexContainer),elementType:sx,externalForwardedProps:Ae,ownerState:Q,getSlotProps:ne=>({...ne,onKeyDown:Se=>{var De;He(Se),(De=ne.onKeyDown)==null||De.call(ne,Se)}})});return w.jsxs(Ve,{...Rt,children:[Ne.scrollButtonStart,Ne.scrollbarSizeListener,w.jsxs(St,{...st,children:[w.jsx(Oe,{"aria-label":i,"aria-labelledby":a,"aria-orientation":y==="vertical"?"vertical":null,role:"tablist",...pt,children:Fe}),ye&&kt]}),Ne.scrollButtonEnd]})});function lx(e){return ce("MuiFormLabel",e)}const nn=ue("MuiFormLabel",["root","colorSecondary","focused","disabled","error","filled","required","asterisk"]),cx=e=>{const{classes:t,color:r,focused:o,disabled:n,error:s,filled:i,required:a}=e,l={root:["root",`color${B(r)}`,n&&"disabled",s&&"error",i&&"filled",o&&"focused",a&&"required"],asterisk:["asterisk",s&&"error"]};return de(l,lx,t)},ux=j("label",{name:"MuiFormLabel",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.color==="secondary"&&t.colorSecondary,r.filled&&t.filled]}})(re(({theme:e})=>({color:(e.vars||e).palette.text.secondary,...e.typography.body1,lineHeight:"1.4375em",padding:0,position:"relative",variants:[...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t},style:{[`&.${nn.focused}`]:{color:(e.vars||e).palette[t].main}}})),{props:{},style:{[`&.${nn.disabled}`]:{color:(e.vars||e).palette.text.disabled},[`&.${nn.error}`]:{color:(e.vars||e).palette.error.main}}}]}))),dx=j("span",{name:"MuiFormLabel",slot:"Asterisk"})(re(({theme:e})=>({[`&.${nn.error}`]:{color:(e.vars||e).palette.error.main}}))),px=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiFormLabel"}),{children:n,className:s,color:i,component:a="label",disabled:l,error:c,filled:u,focused:d,required:f,...m}=o,h=Ar(),b=io({props:o,muiFormControl:h,states:["color","required","focused","disabled","error","filled"]}),y={...o,color:b.color||"primary",component:a,disabled:b.disabled,error:b.error,filled:b.filled,focused:b.focused,required:b.required},S=cx(y);return w.jsxs(ux,{as:a,ownerState:y,className:V(S.root,s),ref:r,...m,children:[n,b.required&&w.jsxs(dx,{ownerState:y,"aria-hidden":!0,className:S.asterisk,children:[" ","*"]})]})});function fx(e){return ce("MuiInputLabel",e)}ue("MuiInputLabel",["root","focused","disabled","error","required","asterisk","formControl","sizeSmall","shrink","animated","standard","filled","outlined"]);const mx=e=>{const{classes:t,formControl:r,size:o,shrink:n,disableAnimation:s,variant:i,required:a}=e,l={root:["root",r&&"formControl",!s&&"animated",n&&"shrink",o&&o!=="medium"&&`size${B(o)}`,i],asterisk:[a&&"asterisk"]},c=de(l,fx,t);return{...t,...c}},gx=j(px,{shouldForwardProp:e=>Et(e)||e==="classes",name:"MuiInputLabel",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[{[`& .${nn.asterisk}`]:t.asterisk},t.root,r.formControl&&t.formControl,r.size==="small"&&t.sizeSmall,r.shrink&&t.shrink,!r.disableAnimation&&t.animated,r.focused&&t.focused,t[r.variant]]}})(re(({theme:e})=>({display:"block",transformOrigin:"top left",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",variants:[{props:({ownerState:t})=>t.formControl,style:{position:"absolute",left:0,top:0,transform:"translate(0, 20px) scale(1)"}},{props:{size:"small"},style:{transform:"translate(0, 17px) scale(1)"}},{props:({ownerState:t})=>t.shrink,style:{transform:"translate(0, -1.5px) scale(0.75)",transformOrigin:"top left",maxWidth:"133%"}},{props:({ownerState:t})=>!t.disableAnimation,style:{transition:e.transitions.create(["color","transform","max-width"],{duration:e.transitions.duration.shorter,easing:e.transitions.easing.easeOut})}},{props:{variant:"filled"},style:{zIndex:1,pointerEvents:"none",transform:"translate(12px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"filled",size:"small"},style:{transform:"translate(12px, 13px) scale(1)"}},{props:({variant:t,ownerState:r})=>t==="filled"&&r.shrink,style:{userSelect:"none",pointerEvents:"auto",transform:"translate(12px, 7px) scale(0.75)",maxWidth:"calc(133% - 24px)"}},{props:({variant:t,ownerState:r,size:o})=>t==="filled"&&r.shrink&&o==="small",style:{transform:"translate(12px, 4px) scale(0.75)"}},{props:{variant:"outlined"},style:{zIndex:1,pointerEvents:"none",transform:"translate(14px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"outlined",size:"small"},style:{transform:"translate(14px, 9px) scale(1)"}},{props:({variant:t,ownerState:r})=>t==="outlined"&&r.shrink,style:{userSelect:"none",pointerEvents:"auto",maxWidth:"calc(133% - 32px)",transform:"translate(14px, -9px) scale(0.75)"}}]}))),hx=p.forwardRef(function(t,r){const o=me({name:"MuiInputLabel",props:t}),{disableAnimation:n=!1,margin:s,shrink:i,variant:a,className:l,...c}=o,u=Ar();let d=i;typeof d>"u"&&u&&(d=u.filled||u.focused||u.adornedStart);const f=io({props:o,muiFormControl:u,states:["size","variant","required","focused"]}),m={...o,disableAnimation:n,formControl:u,shrink:d,size:f.size,variant:f.variant,required:f.required,focused:f.focused},h=mx(m);return w.jsx(gx,{"data-shrink":d,ref:r,className:V(h.root,l),...c,ownerState:m,classes:h})}),L$=Dm({createStyledComponent:j("div",{name:"MuiGrid",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.container&&t.container]}}),componentName:"MuiGrid",useThemeProps:e=>me({props:e,name:"MuiGrid"}),useTheme:$r});function bx(e){return ce("MuiCard",e)}ue("MuiCard",["root"]);const yx=e=>{const{classes:t}=e;return de({root:["root"]},bx,t)},vx=j(so,{name:"MuiCard",slot:"Root"})({overflow:"hidden"}),B$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiCard"}),{className:n,raised:s=!1,...i}=o,a={...o,raised:s},l=yx(a);return w.jsx(vx,{className:V(l.root,n),elevation:s?8:void 0,ref:r,ownerState:a,...i})});function xx(e){return ce("MuiCardActions",e)}ue("MuiCardActions",["root","spacing"]);const Sx=e=>{const{classes:t,disableSpacing:r}=e;return de({root:["root",!r&&"spacing"]},xx,t)},Cx=j("div",{name:"MuiCardActions",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,!r.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,variants:[{props:{disableSpacing:!1},style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),N$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiCardActions"}),{disableSpacing:n=!1,className:s,...i}=o,a={...o,disableSpacing:n},l=Sx(a);return w.jsx(Cx,{className:V(l.root,s),ownerState:a,ref:r,...i})});function wx(e){return ce("MuiCardHeader",e)}const ls=ue("MuiCardHeader",["root","avatar","action","content","title","subheader"]),$x=e=>{const{classes:t}=e;return de({root:["root"],avatar:["avatar"],action:["action"],content:["content"],title:["title"],subheader:["subheader"]},wx,t)},kx=j("div",{name:"MuiCardHeader",slot:"Root",overridesResolver:(e,t)=>[{[`& .${ls.title}`]:t.title},{[`& .${ls.subheader}`]:t.subheader},t.root]})({display:"flex",alignItems:"center",padding:16}),Rx=j("div",{name:"MuiCardHeader",slot:"Avatar"})({display:"flex",flex:"0 0 auto",marginRight:16}),Px=j("div",{name:"MuiCardHeader",slot:"Action"})({flex:"0 0 auto",alignSelf:"flex-start",marginTop:-4,marginRight:-8,marginBottom:-4}),Tx=j("div",{name:"MuiCardHeader",slot:"Content"})({flex:"1 1 auto",[`.${ns.root}:where(& .${ls.title})`]:{display:"block"},[`.${ns.root}:where(& .${ls.subheader})`]:{display:"block"}}),j$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiCardHeader"}),{action:n,avatar:s,component:i="div",disableTypography:a=!1,subheader:l,subheaderTypographyProps:c,title:u,titleTypographyProps:d,slots:f={},slotProps:m={},...h}=o,b={...o,component:i,disableTypography:a},y=$x(b),S={slots:f,slotProps:{title:d,subheader:c,...m}};let $=u;const[C,v]=le("title",{className:y.title,elementType:Xt,externalForwardedProps:S,ownerState:b,additionalProps:{variant:s?"body2":"h5",component:"span"}});$!=null&&$.type!==Xt&&!a&&($=w.jsx(C,{...v,children:$}));let x=l;const[k,T]=le("subheader",{className:y.subheader,elementType:Xt,externalForwardedProps:S,ownerState:b,additionalProps:{variant:s?"body2":"body1",color:"textSecondary",component:"span"}});x!=null&&x.type!==Xt&&!a&&(x=w.jsx(k,{...T,children:x}));const[I,A]=le("root",{ref:r,className:y.root,elementType:kx,externalForwardedProps:{...S,...h,component:i},ownerState:b}),[M,L]=le("avatar",{className:y.avatar,elementType:Rx,externalForwardedProps:S,ownerState:b}),[g,P]=le("content",{className:y.content,elementType:Tx,externalForwardedProps:S,ownerState:b}),[R,O]=le("action",{className:y.action,elementType:Px,externalForwardedProps:S,ownerState:b});return w.jsxs(I,{...A,children:[s&&w.jsx(M,{...L,children:s}),w.jsxs(g,{...P,children:[$,x]}),n&&w.jsx(R,{...O,children:n})]})});function Ix(e){return ce("MuiCardContent",e)}ue("MuiCardContent",["root"]);const Ex=e=>{const{classes:t}=e;return de({root:["root"]},Ix,t)},Mx=j("div",{name:"MuiCardContent",slot:"Root"})({padding:16,"&:last-child":{paddingBottom:24}}),z$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiCardContent"}),{className:n,component:s="div",...i}=o,a={...o,component:s},l=Ex(a);return w.jsx(Mx,{as:s,className:V(l.root,n),ownerState:a,ref:r,...i})});function Ax(e,...t){const r=new URL(`https://mui.com/production-error/?code=${e}`);return t.forEach(o=>r.searchParams.append("args[]",o)),`Minified MUI error #${e}; visit ${r} for the full message.`}function Fr(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function id(e){if(p.isValidElement(e)||gs.isValidElementType(e)||!Fr(e))return e;const t={};return Object.keys(e).forEach(r=>{t[r]=id(e[r])}),t}function Oo(e,t,r={clone:!0}){const o=r.clone?{...e}:e;return Fr(e)&&Fr(t)&&Object.keys(t).forEach(n=>{p.isValidElement(t[n])||gs.isValidElementType(t[n])?o[n]=t[n]:Fr(t[n])&&Object.prototype.hasOwnProperty.call(e,n)&&Fr(e[n])?o[n]=Oo(e[n],t[n],r):r.clone?o[n]=Fr(t[n])?id(t[n]):t[n]:o[n]=t[n]}),o}const Ox=e=>{const t=Object.keys(e).map(r=>({key:r,val:e[r]}))||[];return t.sort((r,o)=>r.val-o.val),t.reduce((r,o)=>({...r,[o.key]:o.val}),{})};function Lx(e){const{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:r="px",step:o=5,...n}=e,s=Ox(t),i=Object.keys(s);function a(f){return`@media (min-width:${typeof t[f]=="number"?t[f]:f}${r})`}function l(f){return`@media (max-width:${(typeof t[f]=="number"?t[f]:f)-o/100}${r})`}function c(f,m){const h=i.indexOf(m);return`@media (min-width:${typeof t[f]=="number"?t[f]:f}${r}) and (max-width:${(h!==-1&&typeof t[i[h]]=="number"?t[i[h]]:m)-o/100}${r})`}function u(f){return i.indexOf(f)+1<i.length?c(f,i[i.indexOf(f)+1]):a(f)}function d(f){const m=i.indexOf(f);return m===0?a(i[1]):m===i.length-1?l(i[m]):c(f,i[i.indexOf(f)+1]).replace("@media","@media not all and")}return{keys:i,values:s,up:a,down:l,between:c,only:u,not:d,unit:r,...n}}function Bx(e,t){if(!e.containerQueries)return t;const r=Object.keys(t).filter(o=>o.startsWith("@container")).sort((o,n)=>{var i,a;const s=/min-width:\s*([0-9.]+)/;return+(((i=o.match(s))==null?void 0:i[1])||0)-+(((a=n.match(s))==null?void 0:a[1])||0)});return r.length?r.reduce((o,n)=>{const s=t[n];return delete o[n],o[n]=s,o},{...t}):t}function Nx(e,t){return t==="@"||t.startsWith("@")&&(e.some(r=>t.startsWith(`@${r}`))||!!t.match(/^@\d/))}function jx(e,t){const r=t.match(/^@([^/]+)?\/?(.+)?$/);if(!r)return null;const[,o,n]=r,s=Number.isNaN(+o)?o||0:+o;return e.containerQueries(n).up(s)}function zx(e){const t=(s,i)=>s.replace("@media",i?`@container ${i}`:"@container");function r(s,i){s.up=(...a)=>t(e.breakpoints.up(...a),i),s.down=(...a)=>t(e.breakpoints.down(...a),i),s.between=(...a)=>t(e.breakpoints.between(...a),i),s.only=(...a)=>t(e.breakpoints.only(...a),i),s.not=(...a)=>{const l=t(e.breakpoints.not(...a),i);return l.includes("not all and")?l.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):l}}const o={},n=s=>(r(o,s),o);return r(n),{...e,containerQueries:n}}const Fx={borderRadius:4};function sn(e,t){return t?Oo(e,t,{clone:!1}):e}const Ds={xs:0,sm:600,md:900,lg:1200,xl:1536},vc={keys:["xs","sm","md","lg","xl"],up:e=>`@media (min-width:${Ds[e]}px)`},Dx={containerQueries:e=>({up:t=>{let r=typeof t=="number"?t:Ds[t]||t;return typeof r=="number"&&(r=`${r}px`),e?`@container ${e} (min-width:${r})`:`@container (min-width:${r})`}})};function cr(e,t,r){const o=e.theme||{};if(Array.isArray(t)){const s=o.breakpoints||vc;return t.reduce((i,a,l)=>(i[s.up(s.keys[l])]=r(t[l]),i),{})}if(typeof t=="object"){const s=o.breakpoints||vc;return Object.keys(t).reduce((i,a)=>{if(Nx(s.keys,a)){const l=jx(o.containerQueries?o:Dx,a);l&&(i[l]=r(t[a],a))}else if(Object.keys(s.values||Ds).includes(a)){const l=s.up(a);i[l]=r(t[a],a)}else{const l=a;i[l]=t[l]}return i},{})}return r(t)}function ad(e={}){var r;return((r=e.keys)==null?void 0:r.reduce((o,n)=>{const s=e.up(n);return o[s]={},o},{}))||{}}function ld(e,t){return e.reduce((r,o)=>{const n=r[o];return(!n||Object.keys(n).length===0)&&delete r[o],r},t)}function Wx(e,...t){const r=ad(e),o=[r,...t].reduce((n,s)=>Oo(n,s),{});return ld(Object.keys(r),o)}function Ux(e,t){if(typeof e!="object")return{};const r={},o=Object.keys(t);return Array.isArray(e)?o.forEach((n,s)=>{s<e.length&&(r[n]=!0)}):o.forEach(n=>{e[n]!=null&&(r[n]=!0)}),r}function bi({values:e,breakpoints:t,base:r}){const o=r||Ux(e,t),n=Object.keys(o);if(n.length===0)return e;let s;return n.reduce((i,a,l)=>(Array.isArray(e)?(i[a]=e[l]!=null?e[l]:e[s],s=l):typeof e=="object"?(i[a]=e[a]!=null?e[a]:e[s],s=a):i[a]=e,i),{})}function cd(e){if(typeof e!="string")throw new Error(Ax(7));return e.charAt(0).toUpperCase()+e.slice(1)}function Ws(e,t,r=!0){if(!t||typeof t!="string")return null;if(e&&e.vars&&r){const o=`vars.${t}`.split(".").reduce((n,s)=>n&&n[s]?n[s]:null,e);if(o!=null)return o}return t.split(".").reduce((o,n)=>o&&o[n]!=null?o[n]:null,e)}function cs(e,t,r,o=r){let n;return typeof e=="function"?n=e(r):Array.isArray(e)?n=e[r]||o:n=Ws(e,r)||o,t&&(n=t(n,o,e)),n}function ht(e){const{prop:t,cssProperty:r=e.prop,themeKey:o,transform:n}=e,s=i=>{if(i[t]==null)return null;const a=i[t],l=i.theme,c=Ws(l,o)||{};return cr(i,a,d=>{let f=cs(c,n,d);return d===f&&typeof d=="string"&&(f=cs(c,n,`${t}${d==="default"?"":cd(d)}`,d)),r===!1?f:{[r]:f}})};return s.propTypes={},s.filterProps=[t],s}function Hx(e){const t={};return r=>(t[r]===void 0&&(t[r]=e(r)),t[r])}const Vx={m:"margin",p:"padding"},_x={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},xc={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Gx=Hx(e=>{if(e.length>2)if(xc[e])e=xc[e];else return[e];const[t,r]=e.split(""),o=Vx[t],n=_x[r]||"";return Array.isArray(n)?n.map(s=>o+s):[o+n]}),Aa=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Oa=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...Aa,...Oa];function kn(e,t,r,o){const n=Ws(e,t,!0)??r;return typeof n=="number"||typeof n=="string"?s=>typeof s=="string"?s:typeof n=="string"?n.startsWith("var(")&&s===0?0:n.startsWith("var(")&&s===1?n:`calc(${s} * ${n})`:n*s:Array.isArray(n)?s=>{if(typeof s=="string")return s;const i=Math.abs(s),a=n[i];return s>=0?a:typeof a=="number"?-a:typeof a=="string"&&a.startsWith("var(")?`calc(-1 * ${a})`:`-${a}`}:typeof n=="function"?n:()=>{}}function La(e){return kn(e,"spacing",8)}function no(e,t){return typeof t=="string"||t==null?t:e(t)}function Kx(e,t){return r=>e.reduce((o,n)=>(o[n]=no(t,r),o),{})}function qx(e,t,r,o){if(!t.includes(r))return null;const n=Gx(r),s=Kx(n,o),i=e[r];return cr(e,i,s)}function ud(e,t){const r=La(e.theme);return Object.keys(e).map(o=>qx(e,t,o,r)).reduce(sn,{})}function lt(e){return ud(e,Aa)}lt.propTypes={};lt.filterProps=Aa;function ct(e){return ud(e,Oa)}ct.propTypes={};ct.filterProps=Oa;function Yx(e=8,t=La({spacing:e})){if(e.mui)return e;const r=(...o)=>(o.length===0?[1]:o).map(s=>{const i=t(s);return typeof i=="number"?`${i}px`:i}).join(" ");return r.mui=!0,r}function Us(...e){const t=e.reduce((o,n)=>(n.filterProps.forEach(s=>{o[s]=n}),o),{}),r=o=>Object.keys(o).reduce((n,s)=>t[s]?sn(n,t[s](o)):n,{});return r.propTypes={},r.filterProps=e.reduce((o,n)=>o.concat(n.filterProps),[]),r}function qt(e){return typeof e!="number"?e:`${e}px solid`}function sr(e,t){return ht({prop:e,themeKey:"borders",transform:t})}const Xx=sr("border",qt),Qx=sr("borderTop",qt),Zx=sr("borderRight",qt),Jx=sr("borderBottom",qt),eS=sr("borderLeft",qt),tS=sr("borderColor"),rS=sr("borderTopColor"),oS=sr("borderRightColor"),nS=sr("borderBottomColor"),sS=sr("borderLeftColor"),iS=sr("outline",qt),aS=sr("outlineColor"),Hs=e=>{if(e.borderRadius!==void 0&&e.borderRadius!==null){const t=kn(e.theme,"shape.borderRadius",4),r=o=>({borderRadius:no(t,o)});return cr(e,e.borderRadius,r)}return null};Hs.propTypes={};Hs.filterProps=["borderRadius"];Us(Xx,Qx,Zx,Jx,eS,tS,rS,oS,nS,sS,Hs,iS,aS);const Vs=e=>{if(e.gap!==void 0&&e.gap!==null){const t=kn(e.theme,"spacing",8),r=o=>({gap:no(t,o)});return cr(e,e.gap,r)}return null};Vs.propTypes={};Vs.filterProps=["gap"];const _s=e=>{if(e.columnGap!==void 0&&e.columnGap!==null){const t=kn(e.theme,"spacing",8),r=o=>({columnGap:no(t,o)});return cr(e,e.columnGap,r)}return null};_s.propTypes={};_s.filterProps=["columnGap"];const Gs=e=>{if(e.rowGap!==void 0&&e.rowGap!==null){const t=kn(e.theme,"spacing",8),r=o=>({rowGap:no(t,o)});return cr(e,e.rowGap,r)}return null};Gs.propTypes={};Gs.filterProps=["rowGap"];const lS=ht({prop:"gridColumn"}),cS=ht({prop:"gridRow"}),uS=ht({prop:"gridAutoFlow"}),dS=ht({prop:"gridAutoColumns"}),pS=ht({prop:"gridAutoRows"}),fS=ht({prop:"gridTemplateColumns"}),mS=ht({prop:"gridTemplateRows"}),gS=ht({prop:"gridTemplateAreas"}),hS=ht({prop:"gridArea"});Us(Vs,_s,Gs,lS,cS,uS,dS,pS,fS,mS,gS,hS);function ko(e,t){return t==="grey"?t:e}const bS=ht({prop:"color",themeKey:"palette",transform:ko}),yS=ht({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:ko}),vS=ht({prop:"backgroundColor",themeKey:"palette",transform:ko});Us(bS,yS,vS);function Dt(e){return e<=1&&e!==0?`${e*100}%`:e}const xS=ht({prop:"width",transform:Dt}),Ba=e=>{if(e.maxWidth!==void 0&&e.maxWidth!==null){const t=r=>{var n,s,i,a,l;const o=((i=(s=(n=e.theme)==null?void 0:n.breakpoints)==null?void 0:s.values)==null?void 0:i[r])||Ds[r];return o?((l=(a=e.theme)==null?void 0:a.breakpoints)==null?void 0:l.unit)!=="px"?{maxWidth:`${o}${e.theme.breakpoints.unit}`}:{maxWidth:o}:{maxWidth:Dt(r)}};return cr(e,e.maxWidth,t)}return null};Ba.filterProps=["maxWidth"];const SS=ht({prop:"minWidth",transform:Dt}),CS=ht({prop:"height",transform:Dt}),wS=ht({prop:"maxHeight",transform:Dt}),$S=ht({prop:"minHeight",transform:Dt});ht({prop:"size",cssProperty:"width",transform:Dt});ht({prop:"size",cssProperty:"height",transform:Dt});const kS=ht({prop:"boxSizing"});Us(xS,Ba,SS,CS,wS,$S,kS);const Na={border:{themeKey:"borders",transform:qt},borderTop:{themeKey:"borders",transform:qt},borderRight:{themeKey:"borders",transform:qt},borderBottom:{themeKey:"borders",transform:qt},borderLeft:{themeKey:"borders",transform:qt},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:qt},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Hs},color:{themeKey:"palette",transform:ko},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:ko},backgroundColor:{themeKey:"palette",transform:ko},p:{style:ct},pt:{style:ct},pr:{style:ct},pb:{style:ct},pl:{style:ct},px:{style:ct},py:{style:ct},padding:{style:ct},paddingTop:{style:ct},paddingRight:{style:ct},paddingBottom:{style:ct},paddingLeft:{style:ct},paddingX:{style:ct},paddingY:{style:ct},paddingInline:{style:ct},paddingInlineStart:{style:ct},paddingInlineEnd:{style:ct},paddingBlock:{style:ct},paddingBlockStart:{style:ct},paddingBlockEnd:{style:ct},m:{style:lt},mt:{style:lt},mr:{style:lt},mb:{style:lt},ml:{style:lt},mx:{style:lt},my:{style:lt},margin:{style:lt},marginTop:{style:lt},marginRight:{style:lt},marginBottom:{style:lt},marginLeft:{style:lt},marginX:{style:lt},marginY:{style:lt},marginInline:{style:lt},marginInlineStart:{style:lt},marginInlineEnd:{style:lt},marginBlock:{style:lt},marginBlockStart:{style:lt},marginBlockEnd:{style:lt},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Vs},rowGap:{style:Gs},columnGap:{style:_s},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Dt},maxWidth:{style:Ba},minWidth:{transform:Dt},height:{transform:Dt},maxHeight:{transform:Dt},minHeight:{transform:Dt},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function RS(...e){const t=e.reduce((o,n)=>o.concat(Object.keys(n)),[]),r=new Set(t);return e.every(o=>r.size===Object.keys(o).length)}function PS(e,t){return typeof e=="function"?e(t):e}function TS(){function e(r,o,n,s){const i={[r]:o,theme:n},a=s[r];if(!a)return{[r]:o};const{cssProperty:l=r,themeKey:c,transform:u,style:d}=a;if(o==null)return null;if(c==="typography"&&o==="inherit")return{[r]:o};const f=Ws(n,c)||{};return d?d(i):cr(i,o,h=>{let b=cs(f,u,h);return h===b&&typeof h=="string"&&(b=cs(f,u,`${r}${h==="default"?"":cd(h)}`,h)),l===!1?b:{[l]:b}})}function t(r){const{sx:o,theme:n={}}=r||{};if(!o)return null;const s=n.unstable_sxConfig??Na;function i(a){let l=a;if(typeof a=="function")l=a(n);else if(typeof a!="object")return a;if(!l)return null;const c=ad(n.breakpoints),u=Object.keys(c);let d=c;return Object.keys(l).forEach(f=>{const m=PS(l[f],n);if(m!=null)if(typeof m=="object")if(s[f])d=sn(d,e(f,m,n,s));else{const h=cr({theme:n},m,b=>({[f]:b}));RS(h,m)?d[f]=t({sx:m,theme:n}):d=sn(d,h)}else d=sn(d,e(f,m,n,s))}),Bx(n,ld(u,d))}return Array.isArray(o)?o.map(i):i(o)}return t}const us=TS();us.filterProps=["sx"];function IS(e,t){var o;const r=this;if(r.vars){if(!((o=r.colorSchemes)!=null&&o[e])||typeof r.getColorSchemeSelector!="function")return{};let n=r.getColorSchemeSelector(e);return n==="&"?t:((n.includes("data-")||n.includes("."))&&(n=`*:where(${n.replace(/\s*&$/,"")}) &`),{[n]:t})}return r.palette.mode===e?t:{}}function ja(e={},...t){const{breakpoints:r={},palette:o={},spacing:n,shape:s={},...i}=e,a=Lx(r),l=Yx(n);let c=Oo({breakpoints:a,direction:"ltr",components:{},palette:{mode:"light",...o},spacing:l,shape:{...Fx,...s}},i);return c=zx(c),c.applyStyles=IS,c=t.reduce((u,d)=>Oo(u,d),c),c.unstable_sxConfig={...Na,...i==null?void 0:i.unstable_sxConfig},c.unstable_sx=function(d){return us({sx:d,theme:this})},c}function ES(e){return Object.keys(e).length===0}function MS(e=null){const t=p.useContext(Bo);return!t||ES(t)?e:t}const AS=ja();function OS(e=AS){return MS(e)}const LS=e=>{var o;const t={systemProps:{},otherProps:{}},r=((o=e==null?void 0:e.theme)==null?void 0:o.unstable_sxConfig)??Na;return Object.keys(e).forEach(n=>{r[n]?t.systemProps[n]=e[n]:t.otherProps[n]=e[n]}),t};function BS(e){const{sx:t,...r}=e,{systemProps:o,otherProps:n}=LS(r);let s;return Array.isArray(t)?s=[o,...t]:typeof t=="function"?s=(...i)=>{const a=t(...i);return Fr(a)?{...o,...a}:o}:s={...o,...t},{...n,sx:s}}const Sc=e=>e,NS=()=>{let e=Sc;return{configure(t){e=t},generate(t){return e(t)},reset(){e=Sc}}},jS=NS(),zS={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function dd(e,t,r="Mui"){const o=zS[t];return o?`${r}-${o}`:`${jS.generate(e)}-${t}`}function F$(e,t,r="Mui"){const o={};return t.forEach(n=>{o[n]=dd(e,n,r)}),o}function FS(e){const{variants:t,...r}=e,o={variants:t,style:br(r),isProcessed:!0};return o.style===r||t&&t.forEach(n=>{typeof n.style!="function"&&(n.style=br(n.style))}),o}const DS=ja();function yi(e){return e!=="ownerState"&&e!=="theme"&&e!=="sx"&&e!=="as"}function WS(e){return e?(t,r)=>r[e]:null}function US(e,t,r){e.theme=_S(e.theme)?r:e.theme[t]||e.theme}function Zn(e,t){const r=typeof t=="function"?t(e):t;if(Array.isArray(r))return r.flatMap(o=>Zn(e,o));if(Array.isArray(r==null?void 0:r.variants)){let o;if(r.isProcessed)o=r.style;else{const{variants:n,...s}=r;o=s}return pd(e,r.variants,[o])}return r!=null&&r.isProcessed?r.style:r}function pd(e,t,r=[]){var n;let o;e:for(let s=0;s<t.length;s+=1){const i=t[s];if(typeof i.props=="function"){if(o??(o={...e,...e.ownerState,ownerState:e.ownerState}),!i.props(o))continue}else for(const a in i.props)if(e[a]!==i.props[a]&&((n=e.ownerState)==null?void 0:n[a])!==i.props[a])continue e;typeof i.style=="function"?(o??(o={...e,...e.ownerState,ownerState:e.ownerState}),r.push(i.style(o))):r.push(i.style)}return r}function HS(e={}){const{themeId:t,defaultTheme:r=DS,rootShouldForwardProp:o=yi,slotShouldForwardProp:n=yi}=e;function s(a){US(a,t,r)}return(a,l={})=>{ou(a,x=>x.filter(k=>k!==us));const{name:c,slot:u,skipVariantsResolver:d,skipSx:f,overridesResolver:m=WS(KS(u)),...h}=l,b=d!==void 0?d:u&&u!=="Root"&&u!=="root"||!1,y=f||!1;let S=yi;u==="Root"||u==="root"?S=o:u?S=n:GS(a)&&(S=void 0);const $=na(a,{shouldForwardProp:S,label:VS(),...h}),C=x=>{if(x.__emotion_real===x)return x;if(typeof x=="function")return function(T){return Zn(T,x)};if(Fr(x)){const k=FS(x);return k.variants?function(I){return Zn(I,k)}:k.style}return x},v=(...x)=>{const k=[],T=x.map(C),I=[];if(k.push(s),c&&m&&I.push(function(g){var E,F;const R=(F=(E=g.theme.components)==null?void 0:E[c])==null?void 0:F.styleOverrides;if(!R)return null;const O={};for(const z in R)O[z]=Zn(g,R[z]);return m(g,O)}),c&&!b&&I.push(function(g){var O,E;const P=g.theme,R=(E=(O=P==null?void 0:P.components)==null?void 0:O[c])==null?void 0:E.variants;return R?pd(g,R):null}),y||I.push(us),Array.isArray(T[0])){const L=T.shift(),g=new Array(k.length).fill(""),P=new Array(I.length).fill("");let R;R=[...g,...L,...P],R.raw=[...g,...L.raw,...P],k.unshift(R)}const A=[...k,...T,...I],M=$(...A);return a.muiName&&(M.muiName=a.muiName),M};return $.withConfig&&(v.withConfig=$.withConfig),v}}function VS(e,t){return void 0}function _S(e){for(const t in e)return!1;return!0}function GS(e){return typeof e=="string"&&e.charCodeAt(0)>96}function KS(e){return e&&e.charAt(0).toLowerCase()+e.slice(1)}const qS=HS();function fd(e,t){const r={...t};for(const o in e)if(Object.prototype.hasOwnProperty.call(e,o)){const n=o;if(n==="components"||n==="slots")r[n]={...e[n],...r[n]};else if(n==="componentsProps"||n==="slotProps"){const s=e[n],i=t[n];if(!i)r[n]=s||{};else if(!s)r[n]=i;else{r[n]={...i};for(const a in s)if(Object.prototype.hasOwnProperty.call(s,a)){const l=a;r[n][l]=fd(s[l],i[l])}}}else r[n]===void 0&&(r[n]=e[n])}return r}function YS(e){const{theme:t,name:r,props:o}=e;return!t||!t.components||!t.components[r]||!t.components[r].defaultProps?o:fd(t.components[r].defaultProps,o)}function XS({props:e,name:t,defaultTheme:r,themeId:o}){let n=OS(r);return o&&(n=n[o]||n),YS({theme:n,name:t,props:e})}const QS=p.createContext(),D$=()=>p.useContext(QS)??!1;function ZS(e,t,r=void 0){const o={};for(const n in e){const s=e[n];let i="",a=!0;for(let l=0;l<s.length;l+=1){const c=s[l];c&&(i+=(a===!0?"":" ")+t(c),a=!1,r&&r[c]&&(i+=" "+r[c]))}o[n]=i}return o}const JS=ja(),eC=qS("div",{name:"MuiStack",slot:"Root"});function tC(e){return XS({props:e,name:"MuiStack",defaultTheme:JS})}function rC(e,t){const r=p.Children.toArray(e).filter(Boolean);return r.reduce((o,n,s)=>(o.push(n),s<r.length-1&&o.push(p.cloneElement(t,{key:`separator-${s}`})),o),[])}const oC=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],nC=({ownerState:e,theme:t})=>{let r={display:"flex",flexDirection:"column",...cr({theme:t},bi({values:e.direction,breakpoints:t.breakpoints.values}),o=>({flexDirection:o}))};if(e.spacing){const o=La(t),n=Object.keys(t.breakpoints.values).reduce((l,c)=>((typeof e.spacing=="object"&&e.spacing[c]!=null||typeof e.direction=="object"&&e.direction[c]!=null)&&(l[c]=!0),l),{}),s=bi({values:e.direction,base:n}),i=bi({values:e.spacing,base:n});typeof s=="object"&&Object.keys(s).forEach((l,c,u)=>{if(!s[l]){const f=c>0?s[u[c-1]]:"column";s[l]=f}}),r=Oo(r,cr({theme:t},i,(l,c)=>e.useFlexGap?{gap:no(o,l)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${oC(c?s[c]:e.direction)}`]:no(o,l)}}))}return r=Wx(t.breakpoints,r),r};function sC(e={}){const{createStyledComponent:t=eC,useThemeProps:r=tC,componentName:o="MuiStack"}=e,n=()=>ZS({root:["root"]},l=>dd(o,l),{}),s=t(nC);return p.forwardRef(function(l,c){const u=r(l),d=BS(u),{component:f="div",direction:m="column",spacing:h=0,divider:b,children:y,className:S,useFlexGap:$=!1,...C}=d,v={direction:m,spacing:h,useFlexGap:$},x=n();return w.jsx(s,{as:f,ownerState:v,ref:c,className:V(x.root,S),...C,children:b?rC(y,b):y})})}const W$=sC();function Cc({array1:e,array2:t,parser:r=o=>o}){return e&&t&&e.length===t.length&&e.every((o,n)=>r(o)===r(t[n]))}function wc(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}function iC(e={}){const{ignoreAccents:t=!0,ignoreCase:r=!0,limit:o,matchFrom:n="any",stringify:s,trim:i=!1}=e;return(a,{inputValue:l,getOptionLabel:c})=>{let u=i?l.trim():l;r&&(u=u.toLowerCase()),t&&(u=wc(u));const d=u?a.filter(f=>{let m=(s||c)(f);return r&&(m=m.toLowerCase()),t&&(m=wc(m)),n==="start"?m.startsWith(u):m.includes(u)}):a;return typeof o=="number"?d.slice(0,o):d}}const aC=iC(),$c=5,lC=e=>{var t;return e.current!==null&&((t=e.current.parentElement)==null?void 0:t.contains(document.activeElement))},cC=[];function kc(e,t,r,o){if(t||e==null||o)return"";const n=r(e);return typeof n=="string"?n:""}function uC(e){const{unstable_isActiveElementInListbox:t=lC,unstable_classNamePrefix:r="Mui",autoComplete:o=!1,autoHighlight:n=!1,autoSelect:s=!1,blurOnSelect:i=!1,clearOnBlur:a=!e.freeSolo,clearOnEscape:l=!1,componentName:c="useAutocomplete",defaultValue:u=e.multiple?cC:null,disableClearable:d=!1,disableCloseOnSelect:f=!1,disabled:m,disabledItemsFocusable:h=!1,disableListWrap:b=!1,filterOptions:y=aC,filterSelectedOptions:S=!1,freeSolo:$=!1,getOptionDisabled:C,getOptionKey:v,getOptionLabel:x=U=>U.label??U,groupBy:k,handleHomeEndKeys:T=!e.freeSolo,id:I,includeInputInList:A=!1,inputValue:M,isOptionEqualToValue:L=(U,W)=>U===W,multiple:g=!1,onChange:P,onClose:R,onHighlightChange:O,onInputChange:E,onOpen:F,open:z,openOnFocus:N=!1,options:Q,readOnly:_=!1,renderValue:Z,selectOnFocus:se=!e.freeSolo,value:ye}=e,K=xr(I);let q=x;q=U=>{const W=x(U);return typeof W!="string"?String(W):W};const ie=p.useRef(!1),we=p.useRef(!0),he=p.useRef(null),ee=p.useRef(null),[Ce,oe]=p.useState(null),[ge,ve]=p.useState(-1),Le=n?0:-1,Me=p.useRef(Le),$e=p.useRef(kc(u??ye,g,q)).current,[J,Ae]=Wr({controlled:ye,default:u,name:c}),[pe,Re]=Wr({controlled:M,default:$e,name:c,state:"inputValue"}),[te,Ge]=p.useState(!1),je=p.useCallback((U,W,fe)=>{if(!(g?J.length<W.length:W!==null)&&!a)return;const Be=kc(W,g,q,Z);pe!==Be&&(Re(Be),E&&E(U,Be,fe))},[q,pe,g,E,Re,a,J,Z]),[H,Y]=Wr({controlled:z,default:!1,name:c,state:"open"}),[ae,xe]=p.useState(!0),Te=!g&&J!=null&&pe===q(J),X=H&&!_,G=X?y(Q.filter(U=>!(S&&(g?J:[J]).some(W=>W!==null&&L(U,W)))),{inputValue:Te&&ae?"":pe,getOptionLabel:q}):[],Pe=Sa({filteredOptions:G,value:J,inputValue:pe});p.useEffect(()=>{const U=J!==Pe.value;te&&!U||$&&!U||je(null,J,"reset")},[J,je,te,Pe.value,$]);const Ie=H&&G.length>0&&!_,ke=vt(U=>{if(U===-1)he.current.focus();else{const W=Z?"data-item-index":"data-tag-index";Ce.querySelector(`[${W}="${U}"]`).focus()}});p.useEffect(()=>{g&&ge>J.length-1&&(ve(-1),ke(-1))},[J,g,ge,ke]);function dt(U,W){if(!ee.current||U<0||U>=G.length)return-1;let fe=U;for(;;){const Ee=ee.current.querySelector(`[data-option-index="${fe}"]`),Be=h?!1:!Ee||Ee.disabled||Ee.getAttribute("aria-disabled")==="true";if(Ee&&Ee.hasAttribute("tabindex")&&!Be)return fe;if(W==="next"?fe=(fe+1)%G.length:fe=(fe-1+G.length)%G.length,fe===U)return-1}}const bt=vt(({event:U,index:W,reason:fe})=>{if(Me.current=W,W===-1?he.current.removeAttribute("aria-activedescendant"):he.current.setAttribute("aria-activedescendant",`${K}-option-${W}`),O&&["mouse","keyboard","touch"].includes(fe)&&O(U,W===-1?null:G[W],fe),!ee.current)return;const Ee=ee.current.querySelector(`[role="option"].${r}-focused`);Ee&&(Ee.classList.remove(`${r}-focused`),Ee.classList.remove(`${r}-focusVisible`));let Be=ee.current;if(ee.current.getAttribute("role")!=="listbox"&&(Be=ee.current.parentElement.querySelector('[role="listbox"]')),!Be)return;if(W===-1){Be.scrollTop=0;return}const wt=ee.current.querySelector(`[data-option-index="${W}"]`);if(wt&&(wt.classList.add(`${r}-focused`),fe==="keyboard"&&wt.classList.add(`${r}-focusVisible`),Be.scrollHeight>Be.clientHeight&&fe!=="mouse"&&fe!=="touch")){const ft=wt,ar=Be.clientHeight+Be.scrollTop,Pn=ft.offsetTop+ft.offsetHeight;Pn>ar?Be.scrollTop=Pn-Be.clientHeight:ft.offsetTop-ft.offsetHeight*(k?1.3:0)<Be.scrollTop&&(Be.scrollTop=ft.offsetTop-ft.offsetHeight*(k?1.3:0))}}),nt=vt(({event:U,diff:W,direction:fe="next",reason:Ee})=>{if(!X)return;const wt=dt((()=>{const ft=G.length-1;if(W==="reset")return Le;if(W==="start")return 0;if(W==="end")return ft;const ar=Me.current+W;return ar<0?ar===-1&&A?-1:b&&Me.current!==-1||Math.abs(W)>1?0:ft:ar>ft?ar===ft+1&&A?-1:b||Math.abs(W)>1?ft:0:ar})(),fe);if(bt({index:wt,reason:Ee,event:U}),o&&W!=="reset")if(wt===-1)he.current.value=pe;else{const ft=q(G[wt]);he.current.value=ft,ft.toLowerCase().indexOf(pe.toLowerCase())===0&&pe.length>0&&he.current.setSelectionRange(pe.length,ft.length)}}),kt=!Cc({array1:Pe.filteredOptions,array2:G,parser:q}),be=()=>{const U=(W,fe)=>{const Ee=W?q(W):"",Be=fe?q(fe):"";return Ee===Be};if(Me.current!==-1&&!Cc({array1:Pe.filteredOptions,array2:G,parser:q})&&Pe.inputValue===pe&&(g?J.length===Pe.value.length&&Pe.value.every((W,fe)=>q(J[fe])===q(W)):U(Pe.value,J))){const W=Pe.filteredOptions[Me.current];if(W)return G.findIndex(fe=>q(fe)===q(W))}return-1},Fe=p.useCallback(()=>{if(!X)return;const U=be();if(U!==-1){Me.current=U;return}const W=g?J[0]:J;if(G.length===0||W==null){nt({diff:"reset"});return}if(ee.current){if(W!=null){const fe=G[Me.current];if(g&&fe&&J.findIndex(Be=>L(fe,Be))!==-1)return;const Ee=G.findIndex(Be=>L(Be,W));Ee===-1?nt({diff:"reset"}):bt({index:Ee});return}if(Me.current>=G.length-1){bt({index:G.length-1});return}bt({index:Me.current})}},[G.length,g?!1:J,S,nt,bt,X,pe,g]),He=vt(U=>{Ei(ee,U),U&&Fe()});p.useEffect(()=>{kt&&Fe()},[Fe,kt]);const Ne=U=>{H||(Y(!0),xe(!0),F&&F(U))},Ve=(U,W)=>{H&&(Y(!1),R&&R(U,W))},Rt=(U,W,fe,Ee)=>{if(g){if(J.length===W.length&&J.every((Be,wt)=>Be===W[wt]))return}else if(J===W)return;P&&P(U,W,fe,Ee),Ae(W)},St=p.useRef(!1),st=(U,W,fe="selectOption",Ee="options")=>{let Be=fe,wt=W;if(g){wt=Array.isArray(J)?J.slice():[];const ft=wt.findIndex(ar=>L(W,ar));ft===-1?wt.push(W):Ee!=="freeSolo"&&(wt.splice(ft,1),Be="removeOption")}je(U,wt,Be),Rt(U,wt,Be,{option:W}),!f&&(!U||!U.ctrlKey&&!U.metaKey)&&Ve(U,Be),(i===!0||i==="touch"&&St.current||i==="mouse"&&!St.current)&&he.current.blur()};function Oe(U,W){if(U===-1)return-1;let fe=U;for(;;){if(W==="next"&&fe===J.length||W==="previous"&&fe===-1)return-1;const Ee=Z?"data-item-index":"data-tag-index",Be=Ce.querySelector(`[${Ee}="${fe}"]`);if(!Be||!Be.hasAttribute("tabindex")||Be.disabled||Be.getAttribute("aria-disabled")==="true")fe+=W==="next"?1:-1;else return fe}}const pt=(U,W)=>{if(!g)return;pe===""&&Ve(U,"toggleInput");let fe=ge;ge===-1?pe===""&&W==="previous"&&(fe=J.length-1):(fe+=W==="next"?1:-1,fe<0&&(fe=0),fe===J.length&&(fe=-1)),fe=Oe(fe,W),ve(fe),ke(fe)},ne=U=>{ie.current=!0,Re(""),E&&E(U,"","clear"),Rt(U,g?[]:null,"clear")},Se=U=>W=>{if(U.onKeyDown&&U.onKeyDown(W),!W.defaultMuiPrevented&&(ge!==-1&&!["ArrowLeft","ArrowRight"].includes(W.key)&&(ve(-1),ke(-1)),W.which!==229))switch(W.key){case"Home":X&&T&&(W.preventDefault(),nt({diff:"start",direction:"next",reason:"keyboard",event:W}));break;case"End":X&&T&&(W.preventDefault(),nt({diff:"end",direction:"previous",reason:"keyboard",event:W}));break;case"PageUp":W.preventDefault(),nt({diff:-$c,direction:"previous",reason:"keyboard",event:W}),Ne(W);break;case"PageDown":W.preventDefault(),nt({diff:$c,direction:"next",reason:"keyboard",event:W}),Ne(W);break;case"ArrowDown":W.preventDefault(),nt({diff:1,direction:"next",reason:"keyboard",event:W}),Ne(W);break;case"ArrowUp":W.preventDefault(),nt({diff:-1,direction:"previous",reason:"keyboard",event:W}),Ne(W);break;case"ArrowLeft":!g&&Z?ke(0):pt(W,"previous");break;case"ArrowRight":!g&&Z?ke(-1):pt(W,"next");break;case"Enter":if(Me.current!==-1&&X){const fe=G[Me.current],Ee=C?C(fe):!1;if(W.preventDefault(),Ee)return;st(W,fe,"selectOption"),o&&he.current.setSelectionRange(he.current.value.length,he.current.value.length)}else $&&pe!==""&&Te===!1&&(g&&W.preventDefault(),st(W,pe,"createOption","freeSolo"));break;case"Escape":X?(W.preventDefault(),W.stopPropagation(),Ve(W,"escape")):l&&(pe!==""||g&&J.length>0||Z)&&(W.preventDefault(),W.stopPropagation(),ne(W));break;case"Backspace":if(g&&!_&&pe===""&&J.length>0){const fe=ge===-1?J.length-1:ge,Ee=J.slice();Ee.splice(fe,1),Rt(W,Ee,"removeOption",{option:J[fe]})}!g&&Z&&!_&&(Ae(null),ke(-1));break;case"Delete":if(g&&!_&&pe===""&&J.length>0&&ge!==-1){const fe=ge,Ee=J.slice();Ee.splice(fe,1),Rt(W,Ee,"removeOption",{option:J[fe]})}!g&&Z&&!_&&(Ae(null),ke(-1));break}},De=U=>{Ge(!0),N&&!ie.current&&Ne(U)},We=U=>{if(t(ee)){he.current.focus();return}Ge(!1),we.current=!0,ie.current=!1,s&&Me.current!==-1&&X?st(U,G[Me.current],"blur"):s&&$&&pe!==""?st(U,pe,"blur","freeSolo"):a&&je(U,J,"blur"),Ve(U,"blur")},Ze=U=>{const W=U.target.value;pe!==W&&(Re(W),xe(!1),E&&E(U,W,"input")),W===""?!d&&!g&&Rt(U,null,"clear"):Ne(U)},At=U=>{const W=Number(U.currentTarget.getAttribute("data-option-index"));Me.current!==W&&bt({event:U,index:W,reason:"mouse"})},ir=U=>{bt({event:U,index:Number(U.currentTarget.getAttribute("data-option-index")),reason:"touch"}),St.current=!0},kr=U=>{const W=Number(U.currentTarget.getAttribute("data-option-index"));st(U,G[W],"selectOption"),St.current=!1},ao=U=>W=>{const fe=J.slice();fe.splice(U,1),Rt(W,fe,"removeOption",{option:J[U]})},Rr=U=>{Rt(U,null,"removeOption",{option:J})},xt=U=>{H?Ve(U,"toggleInput"):Ne(U)},Ct=U=>{U.currentTarget.contains(U.target)&&U.target.getAttribute("id")!==K&&U.preventDefault()},Or=U=>{U.currentTarget.contains(U.target)&&(he.current.focus(),se&&we.current&&he.current.selectionEnd-he.current.selectionStart===0&&he.current.select(),we.current=!1)},Ks=U=>{!m&&(pe===""||!H)&&xt(U)};let zo=$&&pe.length>0;zo=zo||(g?J.length>0:J!==null);let Rn=G;return k&&(Rn=G.reduce((U,W,fe)=>{const Ee=k(W);return U.length>0&&U[U.length-1].group===Ee?U[U.length-1].options.push(W):U.push({key:fe,index:fe,group:Ee,options:[W]}),U},[])),m&&te&&We(),{getRootProps:(U={})=>({...U,onKeyDown:Se(U),onMouseDown:Ct,onClick:Or}),getInputLabelProps:()=>({id:`${K}-label`,htmlFor:K}),getInputProps:()=>({id:K,value:pe,onBlur:We,onFocus:De,onChange:Ze,onMouseDown:Ks,"aria-activedescendant":X?"":null,"aria-autocomplete":o?"both":"list","aria-controls":Ie?`${K}-listbox`:void 0,"aria-expanded":Ie,autoComplete:"off",ref:he,autoCapitalize:"none",spellCheck:"false",role:"combobox",disabled:m}),getClearProps:()=>({tabIndex:-1,type:"button",onClick:ne}),getItemProps:({index:U=0}={})=>({...g&&{key:U},...Z?{"data-item-index":U}:{"data-tag-index":U},tabIndex:-1,...!_&&{onDelete:g?ao(U):Rr}}),getPopupIndicatorProps:()=>({tabIndex:-1,type:"button",onClick:xt}),getTagProps:({index:U})=>({key:U,"data-tag-index":U,tabIndex:-1,...!_&&{onDelete:ao(U)}}),getListboxProps:()=>({role:"listbox",id:`${K}-listbox`,"aria-labelledby":`${K}-label`,ref:He,onMouseDown:U=>{U.preventDefault()}}),getOptionProps:({index:U,option:W})=>{const fe=(g?J:[J]).some(Be=>Be!=null&&L(W,Be)),Ee=C?C(W):!1;return{key:(v==null?void 0:v(W))??q(W),tabIndex:-1,role:"option",id:`${K}-option-${U}`,onMouseMove:At,onClick:kr,onTouchStart:ir,"data-option-index":U,"aria-disabled":Ee,"aria-selected":fe}},id:K,inputValue:pe,value:J,dirty:zo,expanded:X&&Ce,popupOpen:X,focused:te||ge!==-1,anchorEl:Ce,setAnchorEl:oe,focusedItem:ge,focusedTag:ge,groupedOptions:Rn}}function dC(e){return ce("MuiListSubheader",e)}ue("MuiListSubheader",["root","colorPrimary","colorInherit","gutters","inset","sticky"]);const pC=e=>{const{classes:t,color:r,disableGutters:o,inset:n,disableSticky:s}=e,i={root:["root",r!=="default"&&`color${B(r)}`,!o&&"gutters",n&&"inset",!s&&"sticky"]};return de(i,dC,t)},fC=j("li",{name:"MuiListSubheader",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.color!=="default"&&t[`color${B(r.color)}`],!r.disableGutters&&t.gutters,r.inset&&t.inset,!r.disableSticky&&t.sticky]}})(re(({theme:e})=>({boxSizing:"border-box",lineHeight:"48px",listStyle:"none",color:(e.vars||e).palette.text.secondary,fontFamily:e.typography.fontFamily,fontWeight:e.typography.fontWeightMedium,fontSize:e.typography.pxToRem(14),variants:[{props:{color:"primary"},style:{color:(e.vars||e).palette.primary.main}},{props:{color:"inherit"},style:{color:"inherit"}},{props:({ownerState:t})=>!t.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:t})=>t.inset,style:{paddingLeft:72}},{props:({ownerState:t})=>!t.disableSticky,style:{position:"sticky",top:0,zIndex:1,backgroundColor:(e.vars||e).palette.background.paper}}]}))),Hi=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiListSubheader"}),{className:n,color:s="default",component:i="li",disableGutters:a=!1,disableSticky:l=!1,inset:c=!1,...u}=o,d={...o,color:s,component:i,disableGutters:a,disableSticky:l,inset:c},f=pC(d);return w.jsx(fC,{as:i,className:V(f.root,n),ref:r,ownerState:d,...u})});Hi&&(Hi.muiSkipListHighlight=!0);function mC(e){return ce("MuiInput",e)}const Dr={...jt,...ue("MuiInput",["root","underline","input"])};function gC(e){return ce("MuiFilledInput",e)}const zt={...jt,...ue("MuiFilledInput",["root","underline","input","adornedStart","adornedEnd","sizeSmall","multiline","hiddenLabel"])},hC=ur(w.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),md=ur(w.jsx("path",{d:"M7 10l5 5 5-5z"}));function bC(e){return ce("MuiAutocomplete",e)}const ze=ue("MuiAutocomplete",["root","expanded","fullWidth","focused","focusVisible","tag","tagSizeSmall","tagSizeMedium","hasPopupIcon","hasClearIcon","inputRoot","input","inputFocused","endAdornment","clearIndicator","popupIndicator","popupIndicatorOpen","popper","popperDisablePortal","paper","listbox","loading","noOptions","option","groupLabel","groupUl"]);var Rc,Pc;const yC=e=>{const{classes:t,disablePortal:r,expanded:o,focused:n,fullWidth:s,hasClearIcon:i,hasPopupIcon:a,inputFocused:l,popupOpen:c,size:u}=e,d={root:["root",o&&"expanded",n&&"focused",s&&"fullWidth",i&&"hasClearIcon",a&&"hasPopupIcon"],inputRoot:["inputRoot"],input:["input",l&&"inputFocused"],tag:["tag",`tagSize${B(u)}`],endAdornment:["endAdornment"],clearIndicator:["clearIndicator"],popupIndicator:["popupIndicator",c&&"popupIndicatorOpen"],popper:["popper",r&&"popperDisablePortal"],paper:["paper"],listbox:["listbox"],loading:["loading"],noOptions:["noOptions"],option:["option"],groupLabel:["groupLabel"],groupUl:["groupUl"]};return de(d,bC,t)},vC=j("div",{name:"MuiAutocomplete",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e,{fullWidth:o,hasClearIcon:n,hasPopupIcon:s,inputFocused:i,size:a}=r;return[{[`& .${ze.tag}`]:t.tag},{[`& .${ze.tag}`]:t[`tagSize${B(a)}`]},{[`& .${ze.inputRoot}`]:t.inputRoot},{[`& .${ze.input}`]:t.input},{[`& .${ze.input}`]:i&&t.inputFocused},t.root,o&&t.fullWidth,s&&t.hasPopupIcon,n&&t.hasClearIcon]}})({[`&.${ze.focused} .${ze.clearIndicator}`]:{visibility:"visible"},"@media (pointer: fine)":{[`&:hover .${ze.clearIndicator}`]:{visibility:"visible"}},[`& .${ze.tag}`]:{margin:3,maxWidth:"calc(100% - 6px)"},[`& .${ze.inputRoot}`]:{[`.${ze.hasPopupIcon}&, .${ze.hasClearIcon}&`]:{paddingRight:30},[`.${ze.hasPopupIcon}.${ze.hasClearIcon}&`]:{paddingRight:56},[`& .${ze.input}`]:{width:0,minWidth:30}},[`& .${Dr.root}`]:{paddingBottom:1,"& .MuiInput-input":{padding:"4px 4px 4px 0px"}},[`& .${Dr.root}.${jt.sizeSmall}`]:{[`& .${Dr.input}`]:{padding:"2px 4px 3px 0"}},[`& .${_t.root}`]:{padding:9,[`.${ze.hasPopupIcon}&, .${ze.hasClearIcon}&`]:{paddingRight:39},[`.${ze.hasPopupIcon}.${ze.hasClearIcon}&`]:{paddingRight:65},[`& .${ze.input}`]:{padding:"7.5px 4px 7.5px 5px"},[`& .${ze.endAdornment}`]:{right:9}},[`& .${_t.root}.${jt.sizeSmall}`]:{paddingTop:6,paddingBottom:6,paddingLeft:6,[`& .${ze.input}`]:{padding:"2.5px 4px 2.5px 8px"}},[`& .${zt.root}`]:{paddingTop:19,paddingLeft:8,[`.${ze.hasPopupIcon}&, .${ze.hasClearIcon}&`]:{paddingRight:39},[`.${ze.hasPopupIcon}.${ze.hasClearIcon}&`]:{paddingRight:65},[`& .${zt.input}`]:{padding:"7px 4px"},[`& .${ze.endAdornment}`]:{right:9}},[`& .${zt.root}.${jt.sizeSmall}`]:{paddingBottom:1,[`& .${zt.input}`]:{padding:"2.5px 4px"}},[`& .${jt.hiddenLabel}`]:{paddingTop:8},[`& .${zt.root}.${jt.hiddenLabel}`]:{paddingTop:0,paddingBottom:0,[`& .${ze.input}`]:{paddingTop:16,paddingBottom:17}},[`& .${zt.root}.${jt.hiddenLabel}.${jt.sizeSmall}`]:{[`& .${ze.input}`]:{paddingTop:8,paddingBottom:9}},[`& .${ze.input}`]:{flexGrow:1,textOverflow:"ellipsis",opacity:0},variants:[{props:{fullWidth:!0},style:{width:"100%"}},{props:{size:"small"},style:{[`& .${ze.tag}`]:{margin:2,maxWidth:"calc(100% - 4px)"}}},{props:{inputFocused:!0},style:{[`& .${ze.input}`]:{opacity:1}}},{props:{multiple:!0},style:{[`& .${ze.inputRoot}`]:{flexWrap:"wrap"}}}]}),xC=j("div",{name:"MuiAutocomplete",slot:"EndAdornment"})({position:"absolute",right:0,top:"50%",transform:"translate(0, -50%)"}),SC=j(xo,{name:"MuiAutocomplete",slot:"ClearIndicator"})({marginRight:-2,padding:4,visibility:"hidden"}),CC=j(xo,{name:"MuiAutocomplete",slot:"PopupIndicator",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.popupIndicator,r.popupOpen&&t.popupIndicatorOpen]}})({padding:2,marginRight:-2,variants:[{props:{popupOpen:!0},style:{transform:"rotate(180deg)"}}]}),wC=j(Os,{name:"MuiAutocomplete",slot:"Popper",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[{[`& .${ze.option}`]:t.option},t.popper,r.disablePortal&&t.popperDisablePortal]}})(re(({theme:e})=>({zIndex:(e.vars||e).zIndex.modal,variants:[{props:{disablePortal:!0},style:{position:"absolute"}}]}))),$C=j(so,{name:"MuiAutocomplete",slot:"Paper"})(re(({theme:e})=>({...e.typography.body1,overflow:"auto"}))),kC=j("div",{name:"MuiAutocomplete",slot:"Loading"})(re(({theme:e})=>({color:(e.vars||e).palette.text.secondary,padding:"14px 16px"}))),RC=j("div",{name:"MuiAutocomplete",slot:"NoOptions"})(re(({theme:e})=>({color:(e.vars||e).palette.text.secondary,padding:"14px 16px"}))),PC=j("ul",{name:"MuiAutocomplete",slot:"Listbox"})(re(({theme:e})=>({listStyle:"none",margin:0,padding:"8px 0",maxHeight:"40vh",overflow:"auto",position:"relative",[`& .${ze.option}`]:{minHeight:48,display:"flex",overflow:"hidden",justifyContent:"flex-start",alignItems:"center",cursor:"pointer",paddingTop:6,boxSizing:"border-box",outline:"0",WebkitTapHighlightColor:"transparent",paddingBottom:6,paddingLeft:16,paddingRight:16,[e.breakpoints.up("sm")]:{minHeight:"auto"},[`&.${ze.focused}`]:{backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},'&[aria-disabled="true"]':{opacity:(e.vars||e).palette.action.disabledOpacity,pointerEvents:"none"},[`&.${ze.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},'&[aria-selected="true"]':{backgroundColor:e.alpha((e.vars||e).palette.primary.main,(e.vars||e).palette.action.selectedOpacity),[`&.${ze.focused}`]:{backgroundColor:e.alpha((e.vars||e).palette.primary.main,`${(e.vars||e).palette.action.selectedOpacity} + ${(e.vars||e).palette.action.hoverOpacity}`),"@media (hover: none)":{backgroundColor:(e.vars||e).palette.action.selected}},[`&.${ze.focusVisible}`]:{backgroundColor:e.alpha((e.vars||e).palette.primary.main,`${(e.vars||e).palette.action.selectedOpacity} + ${(e.vars||e).palette.action.focusOpacity}`)}}}}))),TC=j(Hi,{name:"MuiAutocomplete",slot:"GroupLabel"})(re(({theme:e})=>({backgroundColor:(e.vars||e).palette.background.paper,top:-8}))),IC=j("ul",{name:"MuiAutocomplete",slot:"GroupUl"})({padding:0,[`& .${ze.option}`]:{paddingLeft:24}}),U$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiAutocomplete"}),{autoComplete:n=!1,autoHighlight:s=!1,autoSelect:i=!1,blurOnSelect:a=!1,ChipProps:l,className:c,clearIcon:u=Rc||(Rc=w.jsx(hC,{fontSize:"small"})),clearOnBlur:d=!o.freeSolo,clearOnEscape:f=!1,clearText:m="Clear",closeText:h="Close",componentsProps:b,defaultValue:y=o.multiple?[]:null,disableClearable:S=!1,disableCloseOnSelect:$=!1,disabled:C=!1,disabledItemsFocusable:v=!1,disableListWrap:x=!1,disablePortal:k=!1,filterOptions:T,filterSelectedOptions:I=!1,forcePopupIcon:A="auto",freeSolo:M=!1,fullWidth:L=!1,getLimitTagsText:g=_e=>`+${_e}`,getOptionDisabled:P,getOptionKey:R,getOptionLabel:O,isOptionEqualToValue:E,groupBy:F,handleHomeEndKeys:z=!o.freeSolo,id:N,includeInputInList:Q=!1,inputValue:_,limitTags:Z=-1,ListboxComponent:se,ListboxProps:ye,loading:K=!1,loadingText:q="Loading…",multiple:ie=!1,noOptionsText:we="No options",onChange:he,onClose:ee,onHighlightChange:Ce,onInputChange:oe,onOpen:ge,open:ve,openOnFocus:Le=!1,openText:Me="Open",options:$e,PaperComponent:J,PopperComponent:Ae,popupIcon:pe=Pc||(Pc=w.jsx(md,{})),readOnly:Re=!1,renderGroup:te,renderInput:Ge,renderOption:je,renderTags:H,renderValue:Y,selectOnFocus:ae=!o.freeSolo,size:xe="medium",slots:Te={},slotProps:X={},value:G,...Pe}=o,{getRootProps:Ie,getInputProps:ke,getInputLabelProps:dt,getPopupIndicatorProps:bt,getClearProps:nt,getItemProps:kt,getListboxProps:be,getOptionProps:Fe,value:He,dirty:Ne,expanded:Ve,id:Rt,popupOpen:St,focused:st,focusedItem:Oe,anchorEl:pt,setAnchorEl:ne,inputValue:Se,groupedOptions:De}=uC({...o,componentName:"Autocomplete"}),We=!S&&!C&&Ne&&!Re,Ze=(!M||A===!0)&&A!==!1,{onMouseDown:At}=ke(),{ref:ir,...kr}=be(),Rr=O||(_e=>_e.label??_e),xt={...o,disablePortal:k,expanded:Ve,focused:st,fullWidth:L,getOptionLabel:Rr,hasClearIcon:We,hasPopupIcon:Ze,inputFocused:Oe===-1,popupOpen:St,size:xe},Ct=yC(xt),Or={slots:{paper:J,popper:Ae,...Te},slotProps:{chip:l,listbox:ye,...b,...X}},[Ks,zo]=le("listbox",{elementType:PC,externalForwardedProps:Or,ownerState:xt,className:Ct.listbox,additionalProps:kr,ref:ir}),[Rn,U]=le("paper",{elementType:so,externalForwardedProps:Or,ownerState:xt,className:Ct.paper}),[W,fe]=le("popper",{elementType:Os,externalForwardedProps:Or,ownerState:xt,className:Ct.popper,additionalProps:{disablePortal:k,style:{width:pt?pt.clientWidth:null},role:"presentation",anchorEl:pt,open:St}});let Ee;const Be=_e=>({className:Ct.tag,disabled:C,...kt(_e)});if(ie?He.length>0&&(H?Ee=H(He,Be,xt):Y?Ee=Y(He,Be,xt):Ee=He.map((_e,Lr)=>{const{key:Br,...Fo}=Be({index:Lr});return w.jsx(Xh,{label:Rr(_e),size:xe,...Fo,...Or.slotProps.chip},Br)})):Y&&He!=null&&(Ee=Y(He,Be,xt)),Z>-1&&Array.isArray(Ee)){const _e=Ee.length-Z;!st&&_e>0&&(Ee=Ee.splice(0,Z),Ee.push(w.jsx("span",{className:Ct.tag,children:g(_e)},Ee.length)))}const ft=te||(_e=>w.jsxs("li",{children:[w.jsx(TC,{className:Ct.groupLabel,ownerState:xt,component:"div",children:_e.group}),w.jsx(IC,{className:Ct.groupUl,ownerState:xt,children:_e.children})]},_e.key)),Pn=je||((_e,Lr)=>{const{key:Br,...Fo}=_e;return w.jsx("li",{...Fo,children:Rr(Lr)},Br)}),_a=(_e,Lr)=>{const Br=Fe({option:_e,index:Lr});return Pn({...Br,className:Ct.option},_e,{selected:Br["aria-selected"],index:Lr,inputValue:Se},xt)},Tn=Or.slotProps.clearIndicator,In=Or.slotProps.popupIndicator;return w.jsxs(p.Fragment,{children:[w.jsx(vC,{ref:r,className:V(Ct.root,c),ownerState:xt,...Ie(Pe),children:Ge({id:Rt,disabled:C,fullWidth:!0,size:xe==="small"?"small":void 0,InputLabelProps:dt(),InputProps:{ref:ne,className:Ct.inputRoot,startAdornment:Ee,onMouseDown:_e=>{_e.target===_e.currentTarget&&At(_e)},...(We||Ze)&&{endAdornment:w.jsxs(xC,{className:Ct.endAdornment,ownerState:xt,children:[We?w.jsx(SC,{...nt(),"aria-label":m,title:m,ownerState:xt,...Tn,className:V(Ct.clearIndicator,Tn==null?void 0:Tn.className),children:u}):null,Ze?w.jsx(CC,{...bt(),disabled:C,"aria-label":St?h:Me,title:St?h:Me,ownerState:xt,...In,className:V(Ct.popupIndicator,In==null?void 0:In.className),children:pe}):null]})}},inputProps:{className:Ct.input,disabled:C,readOnly:Re,...ke()}})}),pt?w.jsx(wC,{as:W,...fe,children:w.jsxs($C,{as:Rn,...U,children:[K&&De.length===0?w.jsx(kC,{className:Ct.loading,ownerState:xt,children:q}):null,De.length===0&&!M&&!K?w.jsx(RC,{className:Ct.noOptions,ownerState:xt,role:"presentation",onMouseDown:_e=>{_e.preventDefault()},children:we}):null,De.length>0?w.jsx(Ks,{as:se,...zo,children:De.map((_e,Lr)=>F?ft({key:_e.key,group:_e.group,children:_e.options.map((Br,Fo)=>_a(Br,_e.index+Fo))}):_a(_e,Lr))}):null]})}):null]})});function EC(e){return ce("PrivateSwitchBase",e)}ue("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);const MC=e=>{const{classes:t,checked:r,disabled:o,edge:n}=e,s={root:["root",r&&"checked",o&&"disabled",n&&`edge${B(n)}`],input:["input"]};return de(s,EC,t)},AC=j(Sr,{name:"MuiSwitchBase"})({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:e,ownerState:t})=>e==="start"&&t.size!=="small",style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:e,ownerState:t})=>e==="end"&&t.size!=="small",style:{marginRight:-12}}]}),OC=j("input",{name:"MuiSwitchBase",shouldForwardProp:Et})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),gd=p.forwardRef(function(t,r){const{autoFocus:o,checked:n,checkedIcon:s,defaultChecked:i,disabled:a,disableFocusRipple:l=!1,edge:c=!1,icon:u,id:d,inputProps:f,inputRef:m,name:h,onBlur:b,onChange:y,onFocus:S,readOnly:$,required:C=!1,tabIndex:v,type:x,value:k,slots:T={},slotProps:I={},...A}=t,[M,L]=Wr({controlled:n,default:!!i,name:"SwitchBase",state:"checked"}),g=Ar(),P=K=>{S&&S(K),g&&g.onFocus&&g.onFocus(K)},R=K=>{b&&b(K),g&&g.onBlur&&g.onBlur(K)},O=K=>{if(K.nativeEvent.defaultPrevented)return;const q=K.target.checked;L(q),y&&y(K,q)};let E=a;g&&typeof E>"u"&&(E=g.disabled);const F=x==="checkbox"||x==="radio",z={...t,checked:M,disabled:E,disableFocusRipple:l,edge:c},N=MC(z),Q={slots:T,slotProps:{input:f,...I}},[_,Z]=le("root",{ref:r,elementType:AC,className:N.root,shouldForwardComponentProp:!0,externalForwardedProps:{...Q,component:"span",...A},getSlotProps:K=>({...K,onFocus:q=>{var ie;(ie=K.onFocus)==null||ie.call(K,q),P(q)},onBlur:q=>{var ie;(ie=K.onBlur)==null||ie.call(K,q),R(q)}}),ownerState:z,additionalProps:{centerRipple:!0,focusRipple:!l,disabled:E,role:void 0,tabIndex:null}}),[se,ye]=le("input",{ref:m,elementType:OC,className:N.input,externalForwardedProps:Q,getSlotProps:K=>({...K,onChange:q=>{var ie;(ie=K.onChange)==null||ie.call(K,q),O(q)}}),ownerState:z,additionalProps:{autoFocus:o,checked:n,defaultChecked:i,disabled:E,id:F?d:void 0,name:h,readOnly:$,required:C,tabIndex:v,type:x,...x==="checkbox"&&k===void 0?{}:{value:k}}});return w.jsxs(_,{...Z,children:[w.jsx(se,{...ye}),M?s:u]})}),LC=ur(w.jsx("path",{d:"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"})),BC=ur(w.jsx("path",{d:"M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})),NC=ur(w.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z"}));function jC(e){return ce("MuiCheckbox",e)}const vi=ue("MuiCheckbox",["root","checked","disabled","indeterminate","colorPrimary","colorSecondary","sizeSmall","sizeMedium"]),zC=e=>{const{classes:t,indeterminate:r,color:o,size:n}=e,s={root:["root",r&&"indeterminate",`color${B(o)}`,`size${B(n)}`]},i=de(s,jC,t);return{...t,...i}},FC=j(gd,{shouldForwardProp:e=>Et(e)||e==="classes",name:"MuiCheckbox",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.indeterminate&&t.indeterminate,t[`size${B(r.size)}`],r.color!=="default"&&t[`color${B(r.color)}`]]}})(re(({theme:e})=>({color:(e.vars||e).palette.text.secondary,variants:[{props:{color:"default",disableRipple:!1},style:{"&:hover":{backgroundColor:e.alpha((e.vars||e).palette.action.active,(e.vars||e).palette.action.hoverOpacity)}}},...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t,disableRipple:!1},style:{"&:hover":{backgroundColor:e.alpha((e.vars||e).palette[t].main,(e.vars||e).palette.action.hoverOpacity)}}})),...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t},style:{[`&.${vi.checked}, &.${vi.indeterminate}`]:{color:(e.vars||e).palette[t].main},[`&.${vi.disabled}`]:{color:(e.vars||e).palette.action.disabled}}})),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]}))),DC=w.jsx(BC,{}),WC=w.jsx(LC,{}),UC=w.jsx(NC,{}),H$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiCheckbox"}),{checkedIcon:n=DC,color:s="primary",icon:i=WC,indeterminate:a=!1,indeterminateIcon:l=UC,inputProps:c,size:u="medium",disableRipple:d=!1,className:f,slots:m={},slotProps:h={},...b}=o,y=a?l:i,S=a?l:n,$={...o,disableRipple:d,color:s,indeterminate:a,size:u},C=zC($),v=h.input??c,[x,k]=le("root",{ref:r,elementType:FC,className:V(C.root,f),shouldForwardComponentProp:!0,externalForwardedProps:{slots:m,slotProps:h,...b},ownerState:$,additionalProps:{type:"checkbox",icon:p.cloneElement(y,{fontSize:y.props.fontSize??u}),checkedIcon:p.cloneElement(S,{fontSize:S.props.fontSize??u}),disableRipple:d,slots:m,slotProps:{input:ha(typeof v=="function"?v($):v,{"data-indeterminate":a})}}});return w.jsx(x,{...k,classes:C})});function HC(e){return ce("MuiLinearProgress",e)}ue("MuiLinearProgress",["root","colorPrimary","colorSecondary","determinate","indeterminate","buffer","query","dashed","dashedColorPrimary","dashedColorSecondary","bar","bar1","bar2","barColorPrimary","barColorSecondary","bar1Indeterminate","bar1Determinate","bar1Buffer","bar2Indeterminate","bar2Buffer"]);const Vi=4,_i=wr`
  0% {
    left: -35%;
    right: 100%;
  }

  60% {
    left: 100%;
    right: -90%;
  }

  100% {
    left: 100%;
    right: -90%;
  }
`,VC=typeof _i!="string"?_r`
        animation: ${_i} 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
      `:null,Gi=wr`
  0% {
    left: -200%;
    right: 100%;
  }

  60% {
    left: 107%;
    right: -8%;
  }

  100% {
    left: 107%;
    right: -8%;
  }
`,_C=typeof Gi!="string"?_r`
        animation: ${Gi} 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite;
      `:null,Ki=wr`
  0% {
    opacity: 1;
    background-position: 0 -23px;
  }

  60% {
    opacity: 0;
    background-position: 0 -23px;
  }

  100% {
    opacity: 1;
    background-position: -200px -23px;
  }
`,GC=typeof Ki!="string"?_r`
        animation: ${Ki} 3s infinite linear;
      `:null,KC=e=>{const{classes:t,variant:r,color:o}=e,n={root:["root",`color${B(o)}`,r],dashed:["dashed",`dashedColor${B(o)}`],bar1:["bar","bar1",`barColor${B(o)}`,(r==="indeterminate"||r==="query")&&"bar1Indeterminate",r==="determinate"&&"bar1Determinate",r==="buffer"&&"bar1Buffer"],bar2:["bar","bar2",r!=="buffer"&&`barColor${B(o)}`,r==="buffer"&&`color${B(o)}`,(r==="indeterminate"||r==="query")&&"bar2Indeterminate",r==="buffer"&&"bar2Buffer"]};return de(n,HC,t)},za=(e,t)=>e.vars?e.vars.palette.LinearProgress[`${t}Bg`]:e.palette.mode==="light"?e.lighten(e.palette[t].main,.62):e.darken(e.palette[t].main,.5),qC=j("span",{name:"MuiLinearProgress",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[`color${B(r.color)}`],t[r.variant]]}})(re(({theme:e})=>({position:"relative",overflow:"hidden",display:"block",height:4,zIndex:0,"@media print":{colorAdjust:"exact"},variants:[...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t},style:{backgroundColor:za(e,t)}})),{props:({ownerState:t})=>t.color==="inherit"&&t.variant!=="buffer",style:{"&::before":{content:'""',position:"absolute",left:0,top:0,right:0,bottom:0,backgroundColor:"currentColor",opacity:.3}}},{props:{variant:"buffer"},style:{backgroundColor:"transparent"}},{props:{variant:"query"},style:{transform:"rotate(180deg)"}}]}))),YC=j("span",{name:"MuiLinearProgress",slot:"Dashed",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.dashed,t[`dashedColor${B(r.color)}`]]}})(re(({theme:e})=>({position:"absolute",marginTop:0,height:"100%",width:"100%",backgroundSize:"10px 10px",backgroundPosition:"0 -23px",variants:[{props:{color:"inherit"},style:{opacity:.3,backgroundImage:"radial-gradient(currentColor 0%, currentColor 16%, transparent 42%)"}},...Object.entries(e.palette).filter(et()).map(([t])=>{const r=za(e,t);return{props:{color:t},style:{backgroundImage:`radial-gradient(${r} 0%, ${r} 16%, transparent 42%)`}}})]})),GC||{animation:`${Ki} 3s infinite linear`}),XC=j("span",{name:"MuiLinearProgress",slot:"Bar1",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.bar,t.bar1,t[`barColor${B(r.color)}`],(r.variant==="indeterminate"||r.variant==="query")&&t.bar1Indeterminate,r.variant==="determinate"&&t.bar1Determinate,r.variant==="buffer"&&t.bar1Buffer]}})(re(({theme:e})=>({width:"100%",position:"absolute",left:0,bottom:0,top:0,transition:"transform 0.2s linear",transformOrigin:"left",variants:[{props:{color:"inherit"},style:{backgroundColor:"currentColor"}},...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t},style:{backgroundColor:(e.vars||e).palette[t].main}})),{props:{variant:"determinate"},style:{transition:`transform .${Vi}s linear`}},{props:{variant:"buffer"},style:{zIndex:1,transition:`transform .${Vi}s linear`}},{props:({ownerState:t})=>t.variant==="indeterminate"||t.variant==="query",style:{width:"auto"}},{props:({ownerState:t})=>t.variant==="indeterminate"||t.variant==="query",style:VC||{animation:`${_i} 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite`}}]}))),QC=j("span",{name:"MuiLinearProgress",slot:"Bar2",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.bar,t.bar2,t[`barColor${B(r.color)}`],(r.variant==="indeterminate"||r.variant==="query")&&t.bar2Indeterminate,r.variant==="buffer"&&t.bar2Buffer]}})(re(({theme:e})=>({width:"100%",position:"absolute",left:0,bottom:0,top:0,transition:"transform 0.2s linear",transformOrigin:"left",variants:[...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t},style:{"--LinearProgressBar2-barColor":(e.vars||e).palette[t].main}})),{props:({ownerState:t})=>t.variant!=="buffer"&&t.color!=="inherit",style:{backgroundColor:"var(--LinearProgressBar2-barColor, currentColor)"}},{props:({ownerState:t})=>t.variant!=="buffer"&&t.color==="inherit",style:{backgroundColor:"currentColor"}},{props:{color:"inherit"},style:{opacity:.3}},...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t,variant:"buffer"},style:{backgroundColor:za(e,t),transition:`transform .${Vi}s linear`}})),{props:({ownerState:t})=>t.variant==="indeterminate"||t.variant==="query",style:{width:"auto"}},{props:({ownerState:t})=>t.variant==="indeterminate"||t.variant==="query",style:_C||{animation:`${Gi} 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite`}}]}))),V$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiLinearProgress"}),{className:n,color:s="primary",value:i,valueBuffer:a,variant:l="indeterminate",...c}=o,u={...o,color:s,variant:l},d=KC(u),f=Mr(),m={},h={bar1:{},bar2:{}};if((l==="determinate"||l==="buffer")&&i!==void 0){m["aria-valuenow"]=Math.round(i),m["aria-valuemin"]=0,m["aria-valuemax"]=100;let b=i-100;f&&(b=-b),h.bar1.transform=`translateX(${b}%)`}if(l==="buffer"&&a!==void 0){let b=(a||0)-100;f&&(b=-b),h.bar2.transform=`translateX(${b}%)`}return w.jsxs(qC,{className:V(d.root,n),ownerState:u,role:"progressbar",...m,ref:r,...c,children:[l==="buffer"?w.jsx(YC,{className:d.dashed,ownerState:u}):null,w.jsx(XC,{className:d.bar1,ownerState:u,style:h.bar1}),l==="determinate"?null:w.jsx(QC,{className:d.bar2,ownerState:u,style:h.bar2})]})}),ZC=e=>{const{classes:t,disableUnderline:r}=e,n=de({root:["root",!r&&"underline"],input:["input"]},mC,t);return{...t,...n}},JC=j(js,{shouldForwardProp:e=>Et(e)||e==="classes",name:"MuiInput",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[...Bs(e,t),!r.disableUnderline&&t.underline]}})(re(({theme:e})=>{let r=e.palette.mode==="light"?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)";return e.vars&&(r=e.alpha(e.vars.palette.common.onBackground,e.vars.opacity.inputUnderline)),{position:"relative",variants:[{props:({ownerState:o})=>o.formControl,style:{"label + &":{marginTop:16}}},{props:({ownerState:o})=>!o.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:e.transitions.create("transform",{duration:e.transitions.duration.shorter,easing:e.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${Dr.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${Dr.error}`]:{"&::before, &::after":{borderBottomColor:(e.vars||e).palette.error.main}},"&::before":{borderBottom:`1px solid ${r}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:e.transitions.create("border-bottom-color",{duration:e.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${Dr.disabled}, .${Dr.error}):before`]:{borderBottom:`2px solid ${(e.vars||e).palette.text.primary}`,"@media (hover: none)":{borderBottom:`1px solid ${r}`}},[`&.${Dr.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(e.palette).filter(et()).map(([o])=>({props:{color:o,disableUnderline:!1},style:{"&::after":{borderBottom:`2px solid ${(e.vars||e).palette[o].main}`}}}))]}})),e1=j(zs,{name:"MuiInput",slot:"Input",overridesResolver:Ns})({}),Fa=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiInput"}),{disableUnderline:n=!1,components:s={},componentsProps:i,fullWidth:a=!1,inputComponent:l="input",multiline:c=!1,slotProps:u,slots:d={},type:f="text",...m}=o,h=ZC(o),y={root:{ownerState:{disableUnderline:n}}},S=u??i?$t(u??i,y):y,$=d.root??s.Root??JC,C=d.input??s.Input??e1;return w.jsx(Fs,{slots:{root:$,input:C},slotProps:S,fullWidth:a,inputComponent:l,multiline:c,ref:r,type:f,...m,classes:h})});Fa.muiName="Input";const t1=e=>{const{classes:t,disableUnderline:r,startAdornment:o,endAdornment:n,size:s,hiddenLabel:i,multiline:a}=e,l={root:["root",!r&&"underline",o&&"adornedStart",n&&"adornedEnd",s==="small"&&`size${B(s)}`,i&&"hiddenLabel",a&&"multiline"],input:["input"]},c=de(l,gC,t);return{...t,...c}},r1=j(js,{shouldForwardProp:e=>Et(e)||e==="classes",name:"MuiFilledInput",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[...Bs(e,t),!r.disableUnderline&&t.underline]}})(re(({theme:e})=>{const t=e.palette.mode==="light",r=t?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)",o=t?"rgba(0, 0, 0, 0.06)":"rgba(255, 255, 255, 0.09)",n=t?"rgba(0, 0, 0, 0.09)":"rgba(255, 255, 255, 0.13)",s=t?"rgba(0, 0, 0, 0.12)":"rgba(255, 255, 255, 0.12)";return{position:"relative",backgroundColor:e.vars?e.vars.palette.FilledInput.bg:o,borderTopLeftRadius:(e.vars||e).shape.borderRadius,borderTopRightRadius:(e.vars||e).shape.borderRadius,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shorter,easing:e.transitions.easing.easeOut}),"&:hover":{backgroundColor:e.vars?e.vars.palette.FilledInput.hoverBg:n,"@media (hover: none)":{backgroundColor:e.vars?e.vars.palette.FilledInput.bg:o}},[`&.${zt.focused}`]:{backgroundColor:e.vars?e.vars.palette.FilledInput.bg:o},[`&.${zt.disabled}`]:{backgroundColor:e.vars?e.vars.palette.FilledInput.disabledBg:s},variants:[{props:({ownerState:i})=>!i.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:e.transitions.create("transform",{duration:e.transitions.duration.shorter,easing:e.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${zt.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${zt.error}`]:{"&::before, &::after":{borderBottomColor:(e.vars||e).palette.error.main}},"&::before":{borderBottom:`1px solid ${e.vars?e.alpha(e.vars.palette.common.onBackground,e.vars.opacity.inputUnderline):r}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:e.transitions.create("border-bottom-color",{duration:e.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${zt.disabled}, .${zt.error}):before`]:{borderBottom:`1px solid ${(e.vars||e).palette.text.primary}`},[`&.${zt.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(e.palette).filter(et()).map(([i])=>{var a;return{props:{disableUnderline:!1,color:i},style:{"&::after":{borderBottom:`2px solid ${(a=(e.vars||e).palette[i])==null?void 0:a.main}`}}}}),{props:({ownerState:i})=>i.startAdornment,style:{paddingLeft:12}},{props:({ownerState:i})=>i.endAdornment,style:{paddingRight:12}},{props:({ownerState:i})=>i.multiline,style:{padding:"25px 12px 8px"}},{props:({ownerState:i,size:a})=>i.multiline&&a==="small",style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:i})=>i.multiline&&i.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:i})=>i.multiline&&i.hiddenLabel&&i.size==="small",style:{paddingTop:8,paddingBottom:9}}]}})),o1=j(zs,{name:"MuiFilledInput",slot:"Input",overridesResolver:Ns})(re(({theme:e})=>({paddingTop:25,paddingRight:12,paddingBottom:8,paddingLeft:12,...!e.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:e.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:e.palette.mode==="light"?null:"#fff",caretColor:e.palette.mode==="light"?null:"#fff",borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"}},...e.vars&&{"&:-webkit-autofill":{borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"},[e.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:t})=>t.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:t})=>t.startAdornment,style:{paddingLeft:0}},{props:({ownerState:t})=>t.endAdornment,style:{paddingRight:0}},{props:({ownerState:t})=>t.hiddenLabel&&t.size==="small",style:{paddingTop:8,paddingBottom:9}},{props:({ownerState:t})=>t.multiline,style:{paddingTop:0,paddingBottom:0,paddingLeft:0,paddingRight:0}}]}))),Da=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiFilledInput"}),{disableUnderline:n=!1,components:s={},componentsProps:i,fullWidth:a=!1,hiddenLabel:l,inputComponent:c="input",multiline:u=!1,slotProps:d,slots:f={},type:m="text",...h}=o,b={...o,disableUnderline:n,fullWidth:a,inputComponent:c,multiline:u,type:m},y=t1(o),S={root:{ownerState:b},input:{ownerState:b}},$=d??i?$t(S,d??i):S,C=f.root??s.Root??r1,v=f.input??s.Input??o1;return w.jsx(Fs,{slots:{root:C,input:v},slotProps:$,fullWidth:a,inputComponent:c,multiline:u,ref:r,type:m,...h,classes:y})});Da.muiName="Input";function n1(e){return ce("MuiFormControl",e)}ue("MuiFormControl",["root","marginNone","marginNormal","marginDense","fullWidth","disabled"]);const s1=e=>{const{classes:t,margin:r,fullWidth:o}=e,n={root:["root",r!=="none"&&`margin${B(r)}`,o&&"fullWidth"]};return de(n,n1,t)},i1=j("div",{name:"MuiFormControl",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[`margin${B(r.margin)}`],r.fullWidth&&t.fullWidth]}})({display:"inline-flex",flexDirection:"column",position:"relative",minWidth:0,padding:0,margin:0,border:0,verticalAlign:"top",variants:[{props:{margin:"normal"},style:{marginTop:16,marginBottom:8}},{props:{margin:"dense"},style:{marginTop:8,marginBottom:4}},{props:{fullWidth:!0},style:{width:"100%"}}]}),a1=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiFormControl"}),{children:n,className:s,color:i="primary",component:a="div",disabled:l=!1,error:c=!1,focused:u,fullWidth:d=!1,hiddenLabel:f=!1,margin:m="none",required:h=!1,size:b="medium",variant:y="outlined",...S}=o,$={...o,color:i,component:a,disabled:l,error:c,fullWidth:d,hiddenLabel:f,margin:m,required:h,size:b,variant:y},C=s1($),[v,x]=p.useState(()=>{let O=!1;return n&&p.Children.forEach(n,E=>{if(!Jo(E,["Input","Select"]))return;const F=Jo(E,["Select"])?E.props.input:E;F&&kv(F.props)&&(O=!0)}),O}),[k,T]=p.useState(()=>{let O=!1;return n&&p.Children.forEach(n,E=>{Jo(E,["Input","Select"])&&(as(E.props,!0)||as(E.props.inputProps,!0))&&(O=!0)}),O}),[I,A]=p.useState(!1);l&&I&&A(!1);const M=u!==void 0&&!l?u:I;let L;p.useRef(!1);const g=p.useCallback(()=>{T(!0)},[]),P=p.useCallback(()=>{T(!1)},[]),R=p.useMemo(()=>({adornedStart:v,setAdornedStart:x,color:i,disabled:l,error:c,filled:k,focused:M,fullWidth:d,hiddenLabel:f,size:b,onBlur:()=>{A(!1)},onFocus:()=>{A(!0)},onEmpty:P,onFilled:g,registerEffect:L,required:h,variant:y}),[v,i,l,c,k,M,d,f,L,P,g,h,b,y]);return w.jsx(Ls.Provider,{value:R,children:w.jsx(i1,{as:a,ownerState:$,className:V(C.root,s),ref:r,...S,children:n})})});function l1(e){return ce("MuiFormHelperText",e)}const Tc=ue("MuiFormHelperText",["root","error","disabled","sizeSmall","sizeMedium","contained","focused","filled","required"]);var Ic;const c1=e=>{const{classes:t,contained:r,size:o,disabled:n,error:s,filled:i,focused:a,required:l}=e,c={root:["root",n&&"disabled",s&&"error",o&&`size${B(o)}`,r&&"contained",a&&"focused",i&&"filled",l&&"required"]};return de(c,l1,t)},u1=j("p",{name:"MuiFormHelperText",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.size&&t[`size${B(r.size)}`],r.contained&&t.contained,r.filled&&t.filled]}})(re(({theme:e})=>({color:(e.vars||e).palette.text.secondary,...e.typography.caption,textAlign:"left",marginTop:3,marginRight:0,marginBottom:0,marginLeft:0,[`&.${Tc.disabled}`]:{color:(e.vars||e).palette.text.disabled},[`&.${Tc.error}`]:{color:(e.vars||e).palette.error.main},variants:[{props:{size:"small"},style:{marginTop:4}},{props:({ownerState:t})=>t.contained,style:{marginLeft:14,marginRight:14}}]}))),d1=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiFormHelperText"}),{children:n,className:s,component:i="p",disabled:a,error:l,filled:c,focused:u,margin:d,required:f,variant:m,...h}=o,b=Ar(),y=io({props:o,muiFormControl:b,states:["variant","size","disabled","error","filled","focused","required"]}),S={...o,component:i,contained:y.variant==="filled"||y.variant==="outlined",variant:y.variant,size:y.size,disabled:y.disabled,error:y.error,filled:y.filled,focused:y.focused,required:y.required};delete S.ownerState;const $=c1(S);return w.jsx(u1,{as:i,className:V($.root,s),ref:r,...h,ownerState:S,children:n===" "?Ic||(Ic=w.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):n})});function p1(e){return ce("MuiNativeSelect",e)}const Wa=ue("MuiNativeSelect",["root","select","multiple","filled","outlined","standard","disabled","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),f1=e=>{const{classes:t,variant:r,disabled:o,multiple:n,open:s,error:i}=e,a={select:["select",r,o&&"disabled",n&&"multiple",i&&"error"],icon:["icon",`icon${B(r)}`,s&&"iconOpen",o&&"disabled"]};return de(a,p1,t)},hd=j("select",{name:"MuiNativeSelect"})(({theme:e})=>({MozAppearance:"none",WebkitAppearance:"none",userSelect:"none",borderRadius:0,cursor:"pointer","&:focus":{borderRadius:0},[`&.${Wa.disabled}`]:{cursor:"default"},"&[multiple]":{height:"auto"},"&:not([multiple]) option, &:not([multiple]) optgroup":{backgroundColor:(e.vars||e).palette.background.paper},variants:[{props:({ownerState:t})=>t.variant!=="filled"&&t.variant!=="outlined",style:{"&&&":{paddingRight:24,minWidth:16}}},{props:{variant:"filled"},style:{"&&&":{paddingRight:32}}},{props:{variant:"outlined"},style:{borderRadius:(e.vars||e).shape.borderRadius,"&:focus":{borderRadius:(e.vars||e).shape.borderRadius},"&&&":{paddingRight:32}}}]})),m1=j(hd,{name:"MuiNativeSelect",slot:"Select",shouldForwardProp:Et,overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.select,t[r.variant],r.error&&t.error,{[`&.${Wa.multiple}`]:t.multiple}]}})({}),bd=j("svg",{name:"MuiNativeSelect"})(({theme:e})=>({position:"absolute",right:0,top:"calc(50% - .5em)",pointerEvents:"none",color:(e.vars||e).palette.action.active,[`&.${Wa.disabled}`]:{color:(e.vars||e).palette.action.disabled},variants:[{props:({ownerState:t})=>t.open,style:{transform:"rotate(180deg)"}},{props:{variant:"filled"},style:{right:7}},{props:{variant:"outlined"},style:{right:7}}]})),g1=j(bd,{name:"MuiNativeSelect",slot:"Icon",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.icon,r.variant&&t[`icon${B(r.variant)}`],r.open&&t.iconOpen]}})({}),h1=p.forwardRef(function(t,r){const{className:o,disabled:n,error:s,IconComponent:i,inputRef:a,variant:l="standard",...c}=t,u={...t,disabled:n,variant:l,error:s},d=f1(u);return w.jsxs(p.Fragment,{children:[w.jsx(m1,{ownerState:u,className:V(d.select,o),disabled:n,ref:a||r,...c}),t.multiple?null:w.jsx(g1,{as:i,ownerState:u,className:d.icon})]})});function yd(e){return ce("MuiSelect",e)}const Ko=ue("MuiSelect",["root","select","multiple","filled","outlined","standard","disabled","focused","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]);var Ec;const b1=j(hd,{name:"MuiSelect",slot:"Select",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[{[`&.${Ko.select}`]:t.select},{[`&.${Ko.select}`]:t[r.variant]},{[`&.${Ko.error}`]:t.error},{[`&.${Ko.multiple}`]:t.multiple}]}})({[`&.${Ko.select}`]:{height:"auto",minHeight:"1.4375em",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}),y1=j(bd,{name:"MuiSelect",slot:"Icon",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.icon,r.variant&&t[`icon${B(r.variant)}`],r.open&&t.iconOpen]}})({}),v1=j("input",{shouldForwardProp:e=>Ms(e)&&e!=="classes",name:"MuiSelect",slot:"NativeInput"})({bottom:0,left:0,position:"absolute",opacity:0,pointerEvents:"none",width:"100%",boxSizing:"border-box"});function Mc(e,t){return typeof t=="object"&&t!==null?e===t:String(e)===String(t)}function x1(e){return e==null||typeof e=="string"&&!e.trim()}const S1=e=>{const{classes:t,variant:r,disabled:o,multiple:n,open:s,error:i}=e,a={select:["select",r,o&&"disabled",n&&"multiple",i&&"error"],icon:["icon",`icon${B(r)}`,s&&"iconOpen",o&&"disabled"],nativeInput:["nativeInput"]};return de(a,yd,t)},C1=p.forwardRef(function(t,r){var dt,bt,nt,kt;const{"aria-describedby":o,"aria-label":n,autoFocus:s,autoWidth:i,children:a,className:l,defaultOpen:c,defaultValue:u,disabled:d,displayEmpty:f,error:m=!1,IconComponent:h,inputRef:b,labelId:y,MenuProps:S={},multiple:$,name:C,onBlur:v,onChange:x,onClose:k,onFocus:T,onOpen:I,open:A,readOnly:M,renderValue:L,required:g,SelectDisplayProps:P={},tabIndex:R,type:O,value:E,variant:F="standard",...z}=t,[N,Q]=Wr({controlled:E,default:u,name:"Select"}),[_,Z]=Wr({controlled:A,default:c,name:"Select"}),se=p.useRef(null),ye=p.useRef(null),[K,q]=p.useState(null),{current:ie}=p.useRef(A!=null),[we,he]=p.useState(),ee=ot(r,b),Ce=p.useCallback(be=>{ye.current=be,be&&q(be)},[]),oe=K==null?void 0:K.parentNode;p.useImperativeHandle(ee,()=>({focus:()=>{ye.current.focus()},node:se.current,value:N}),[N]),p.useEffect(()=>{c&&_&&K&&!ie&&(he(i?null:oe.clientWidth),ye.current.focus())},[K,i]),p.useEffect(()=>{s&&ye.current.focus()},[s]),p.useEffect(()=>{if(!y)return;const be=mt(ye.current).getElementById(y);if(be){const Fe=()=>{getSelection().isCollapsed&&ye.current.focus()};return be.addEventListener("click",Fe),()=>{be.removeEventListener("click",Fe)}}},[y]);const ge=(be,Fe)=>{be?I&&I(Fe):k&&k(Fe),ie||(he(i?null:oe.clientWidth),Z(be))},ve=be=>{be.button===0&&(be.preventDefault(),ye.current.focus(),ge(!0,be))},Le=be=>{ge(!1,be)},Me=p.Children.toArray(a),$e=be=>{const Fe=Me.find(He=>He.props.value===be.target.value);Fe!==void 0&&(Q(Fe.props.value),x&&x(be,Fe))},J=be=>Fe=>{let He;if(Fe.currentTarget.hasAttribute("tabindex")){if($){He=Array.isArray(N)?N.slice():[];const Ne=N.indexOf(be.props.value);Ne===-1?He.push(be.props.value):He.splice(Ne,1)}else He=be.props.value;if(be.props.onClick&&be.props.onClick(Fe),N!==He&&(Q(He),x)){const Ne=Fe.nativeEvent||Fe,Ve=new Ne.constructor(Ne.type,Ne);Object.defineProperty(Ve,"target",{writable:!0,value:{value:He,name:C}}),x(Ve,be)}$||ge(!1,Fe)}},Ae=be=>{M||[" ","ArrowUp","ArrowDown","Enter"].includes(be.key)&&(be.preventDefault(),ge(!0,be))},pe=K!==null&&_,Re=be=>{!pe&&v&&(Object.defineProperty(be,"target",{writable:!0,value:{value:N,name:C}}),v(be))};delete z["aria-invalid"];let te,Ge;const je=[];let H=!1;(as({value:N})||f)&&(L?te=L(N):H=!0);const Y=Me.map(be=>{if(!p.isValidElement(be))return null;let Fe;if($){if(!Array.isArray(N))throw new Error(Ir(2));Fe=N.some(He=>Mc(He,be.props.value)),Fe&&H&&je.push(be.props.children)}else Fe=Mc(N,be.props.value),Fe&&H&&(Ge=be.props.children);return p.cloneElement(be,{"aria-selected":Fe?"true":"false",onClick:J(be),onKeyUp:He=>{He.key===" "&&He.preventDefault(),be.props.onKeyUp&&be.props.onKeyUp(He)},role:"option",selected:Fe,value:void 0,"data-value":be.props.value})});H&&($?je.length===0?te=null:te=je.reduce((be,Fe,He)=>(be.push(Fe),He<je.length-1&&be.push(", "),be),[]):te=Ge);let ae=we;!i&&ie&&K&&(ae=oe.clientWidth);let xe;typeof R<"u"?xe=R:xe=d?null:0;const Te=P.id||(C?`mui-component-select-${C}`:void 0),X={...t,variant:F,value:N,open:pe,error:m},G=S1(X),Pe={...S.PaperProps,...typeof((dt=S.slotProps)==null?void 0:dt.paper)=="function"?S.slotProps.paper(X):(bt=S.slotProps)==null?void 0:bt.paper},Ie={...S.MenuListProps,...typeof((nt=S.slotProps)==null?void 0:nt.list)=="function"?S.slotProps.list(X):(kt=S.slotProps)==null?void 0:kt.list},ke=xr();return w.jsxs(p.Fragment,{children:[w.jsx(b1,{as:"div",ref:Ce,tabIndex:xe,role:"combobox","aria-controls":pe?ke:void 0,"aria-disabled":d?"true":void 0,"aria-expanded":pe?"true":"false","aria-haspopup":"listbox","aria-label":n,"aria-labelledby":[y,Te].filter(Boolean).join(" ")||void 0,"aria-describedby":o,"aria-required":g?"true":void 0,"aria-invalid":m?"true":void 0,onKeyDown:Ae,onMouseDown:d||M?null:ve,onBlur:Re,onFocus:T,...P,ownerState:X,className:V(P.className,G.select,l),id:Te,children:x1(te)?Ec||(Ec=w.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):te}),w.jsx(v1,{"aria-invalid":m,value:Array.isArray(N)?N.join(","):N,name:C,ref:se,"aria-hidden":!0,onChange:$e,tabIndex:-1,disabled:d,className:G.nativeInput,autoFocus:s,required:g,...z,ownerState:X}),w.jsx(y1,{as:h,className:G.icon,ownerState:X}),w.jsx(b0,{id:`menu-${C||""}`,anchorEl:oe,open:pe,onClose:Le,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},...S,slotProps:{...S.slotProps,list:{"aria-labelledby":y,role:"listbox","aria-multiselectable":$?"true":void 0,disableListWrap:!0,id:ke,...Ie},paper:{...Pe,style:{minWidth:ae,...Pe!=null?Pe.style:null}}},children:Y})]})}),w1=e=>{const{classes:t}=e,o=de({root:["root"]},yd,t);return{...t,...o}},Ua={name:"MuiSelect",slot:"Root",shouldForwardProp:e=>Et(e)&&e!=="variant"},$1=j(Fa,Ua)(""),k1=j(Ma,Ua)(""),R1=j(Da,Ua)(""),Ha=p.forwardRef(function(t,r){const o=me({name:"MuiSelect",props:t}),{autoWidth:n=!1,children:s,classes:i={},className:a,defaultOpen:l=!1,displayEmpty:c=!1,IconComponent:u=md,id:d,input:f,inputProps:m,label:h,labelId:b,MenuProps:y,multiple:S=!1,native:$=!1,onClose:C,onOpen:v,open:x,renderValue:k,SelectDisplayProps:T,variant:I="outlined",...A}=o,M=$?h1:C1,L=Ar(),g=io({props:o,muiFormControl:L,states:["variant","error"]}),P=g.variant||I,R={...o,variant:P,classes:i},O=w1(R),{root:E,...F}=O,z=f||{standard:w.jsx($1,{ownerState:R}),outlined:w.jsx(k1,{label:h,ownerState:R}),filled:w.jsx(R1,{ownerState:R})}[P],N=ot(r,Gr(z));return w.jsx(p.Fragment,{children:p.cloneElement(z,{inputComponent:M,inputProps:{children:s,error:g.error,IconComponent:u,variant:P,type:void 0,multiple:S,...$?{id:d}:{autoWidth:n,defaultOpen:l,displayEmpty:c,labelId:b,MenuProps:y,onClose:C,onOpen:v,open:x,renderValue:k,SelectDisplayProps:{id:d,...T}},...m,classes:m?$t(F,m.classes):F,...f?f.props.inputProps:{}},...(S&&$||c)&&P==="outlined"?{notched:!0}:{},ref:N,className:V(z.props.className,a,O.root),...!f&&{variant:P},...A})})});Ha.muiName="Select";function P1(e){return ce("MuiTextField",e)}ue("MuiTextField",["root"]);const T1={standard:Fa,filled:Da,outlined:Ma},I1=e=>{const{classes:t}=e;return de({root:["root"]},P1,t)},E1=j(a1,{name:"MuiTextField",slot:"Root"})({}),_$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiTextField"}),{autoComplete:n,autoFocus:s=!1,children:i,className:a,color:l="primary",defaultValue:c,disabled:u=!1,error:d=!1,FormHelperTextProps:f,fullWidth:m=!1,helperText:h,id:b,InputLabelProps:y,inputProps:S,InputProps:$,inputRef:C,label:v,maxRows:x,minRows:k,multiline:T=!1,name:I,onBlur:A,onChange:M,onFocus:L,placeholder:g,required:P=!1,rows:R,select:O=!1,SelectProps:E,slots:F={},slotProps:z={},type:N,value:Q,variant:_="outlined",...Z}=o,se={...o,autoFocus:s,color:l,disabled:u,error:d,fullWidth:m,multiline:T,required:P,select:O,variant:_},ye=I1(se),K=xr(b),q=h&&K?`${K}-helper-text`:void 0,ie=v&&K?`${K}-label`:void 0,we=T1[_],he={slots:F,slotProps:{input:$,inputLabel:y,htmlInput:S,formHelperText:f,select:E,...z}},ee={},Ce=he.slotProps.inputLabel;_==="outlined"&&(Ce&&typeof Ce.shrink<"u"&&(ee.notched=Ce.shrink),ee.label=v),O&&((!E||!E.native)&&(ee.id=void 0),ee["aria-describedby"]=void 0);const[oe,ge]=le("root",{elementType:E1,shouldForwardComponentProp:!0,externalForwardedProps:{...he,...Z},ownerState:se,className:V(ye.root,a),ref:r,additionalProps:{disabled:u,error:d,fullWidth:m,required:P,color:l,variant:_}}),[ve,Le]=le("input",{elementType:we,externalForwardedProps:he,additionalProps:ee,ownerState:se}),[Me,$e]=le("inputLabel",{elementType:hx,externalForwardedProps:he,ownerState:se}),[J,Ae]=le("htmlInput",{elementType:"input",externalForwardedProps:he,ownerState:se}),[pe,Re]=le("formHelperText",{elementType:d1,externalForwardedProps:he,ownerState:se}),[te,Ge]=le("select",{elementType:Ha,externalForwardedProps:he,ownerState:se}),je=w.jsx(ve,{"aria-describedby":q,autoComplete:n,autoFocus:s,defaultValue:c,fullWidth:m,multiline:T,name:I,rows:R,maxRows:x,minRows:k,type:N,value:Q,id:K,inputRef:C,onBlur:A,onChange:M,onFocus:L,placeholder:g,inputProps:Ae,slots:{input:F.htmlInput?J:void 0},...Le});return w.jsxs(oe,{...ge,children:[v!=null&&v!==""&&w.jsx(Me,{htmlFor:K,id:ie,...$e,children:v}),O?w.jsx(te,{"aria-describedby":q,id:K,labelId:ie,value:Q,input:je,...Ge,children:i}):je,h&&w.jsx(pe,{id:q,...Re,children:h})]})});function M1(e){return ce("MuiFormControlLabel",e)}const Qo=ue("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]),A1=e=>{const{classes:t,disabled:r,labelPlacement:o,error:n,required:s}=e,i={root:["root",r&&"disabled",`labelPlacement${B(o)}`,n&&"error",s&&"required"],label:["label",r&&"disabled"],asterisk:["asterisk",n&&"error"]};return de(i,M1,t)},O1=j("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[{[`& .${Qo.label}`]:t.label},t.root,t[`labelPlacement${B(r.labelPlacement)}`]]}})(re(({theme:e})=>({display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,[`&.${Qo.disabled}`]:{cursor:"default"},[`& .${Qo.label}`]:{[`&.${Qo.disabled}`]:{color:(e.vars||e).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:({labelPlacement:t})=>t==="start"||t==="top"||t==="bottom",style:{marginLeft:16}}]}))),L1=j("span",{name:"MuiFormControlLabel",slot:"Asterisk"})(re(({theme:e})=>({[`&.${Qo.error}`]:{color:(e.vars||e).palette.error.main}}))),G$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiFormControlLabel"}),{checked:n,className:s,componentsProps:i={},control:a,disabled:l,disableTypography:c,inputRef:u,label:d,labelPlacement:f="end",name:m,onChange:h,required:b,slots:y={},slotProps:S={},value:$,...C}=o,v=Ar(),x=l??a.props.disabled??(v==null?void 0:v.disabled),k=b??a.props.required,T={disabled:x,required:k};["checked","name","onChange","value","inputRef"].forEach(O=>{typeof a.props[O]>"u"&&typeof o[O]<"u"&&(T[O]=o[O])});const I=io({props:o,muiFormControl:v,states:["error"]}),A={...o,disabled:x,labelPlacement:f,required:k,error:I.error},M=A1(A),L={slots:y,slotProps:{...i,...S}},[g,P]=le("typography",{elementType:Xt,externalForwardedProps:L,ownerState:A});let R=d;return R!=null&&R.type!==Xt&&!c&&(R=w.jsx(g,{component:"span",...P,className:V(M.label,P==null?void 0:P.className),children:R})),w.jsxs(O1,{className:V(M.root,s),ownerState:A,ref:r,...C,children:[p.cloneElement(a,T),k?w.jsxs("div",{children:[R,w.jsxs(L1,{ownerState:A,"aria-hidden":!0,className:M.asterisk,children:[" ","*"]})]}):R]})});function B1(e){return ce("MuiSwitch",e)}const Mt=ue("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]),N1=e=>{const{classes:t,edge:r,size:o,color:n,checked:s,disabled:i}=e,a={root:["root",r&&`edge${B(r)}`,`size${B(o)}`],switchBase:["switchBase",`color${B(n)}`,s&&"checked",i&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},l=de(a,B1,t);return{...t,...l}},j1=j("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.edge&&t[`edge${B(r.edge)}`],t[`size${B(r.size)}`]]}})({display:"inline-flex",width:58,height:38,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,[`& .${Mt.thumb}`]:{width:16,height:16},[`& .${Mt.switchBase}`]:{padding:4,[`&.${Mt.checked}`]:{transform:"translateX(16px)"}}}}]}),z1=j(gd,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.switchBase,{[`& .${Mt.input}`]:t.input},r.color!=="default"&&t[`color${B(r.color)}`]]}})(re(({theme:e})=>({position:"absolute",top:0,left:0,zIndex:1,color:e.vars?e.vars.palette.Switch.defaultColor:`${e.palette.mode==="light"?e.palette.common.white:e.palette.grey[300]}`,transition:e.transitions.create(["left","transform"],{duration:e.transitions.duration.shortest}),[`&.${Mt.checked}`]:{transform:"translateX(20px)"},[`&.${Mt.disabled}`]:{color:e.vars?e.vars.palette.Switch.defaultDisabledColor:`${e.palette.mode==="light"?e.palette.grey[100]:e.palette.grey[600]}`},[`&.${Mt.checked} + .${Mt.track}`]:{opacity:.5},[`&.${Mt.disabled} + .${Mt.track}`]:{opacity:e.vars?e.vars.opacity.switchTrackDisabled:`${e.palette.mode==="light"?.12:.2}`},[`& .${Mt.input}`]:{left:"-100%",width:"300%"}})),re(({theme:e})=>({"&:hover":{backgroundColor:e.alpha((e.vars||e).palette.action.active,(e.vars||e).palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(e.palette).filter(et(["light"])).map(([t])=>({props:{color:t},style:{[`&.${Mt.checked}`]:{color:(e.vars||e).palette[t].main,"&:hover":{backgroundColor:e.alpha((e.vars||e).palette[t].main,(e.vars||e).palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Mt.disabled}`]:{color:e.vars?e.vars.palette.Switch[`${t}DisabledColor`]:`${e.palette.mode==="light"?e.lighten(e.palette[t].main,.62):e.darken(e.palette[t].main,.55)}`}},[`&.${Mt.checked} + .${Mt.track}`]:{backgroundColor:(e.vars||e).palette[t].main}}}))]}))),F1=j("span",{name:"MuiSwitch",slot:"Track"})(re(({theme:e})=>({height:"100%",width:"100%",borderRadius:14/2,zIndex:-1,transition:e.transitions.create(["opacity","background-color"],{duration:e.transitions.duration.shortest}),backgroundColor:e.vars?e.vars.palette.common.onBackground:`${e.palette.mode==="light"?e.palette.common.black:e.palette.common.white}`,opacity:e.vars?e.vars.opacity.switchTrack:`${e.palette.mode==="light"?.38:.3}`}))),D1=j("span",{name:"MuiSwitch",slot:"Thumb"})(re(({theme:e})=>({boxShadow:(e.vars||e).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"}))),K$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiSwitch"}),{className:n,color:s="primary",edge:i=!1,size:a="medium",sx:l,slots:c={},slotProps:u={},...d}=o,f={...o,color:s,edge:i,size:a},m=N1(f),h={slots:c,slotProps:u},[b,y]=le("root",{className:V(m.root,n),elementType:j1,externalForwardedProps:h,ownerState:f,additionalProps:{sx:l}}),[S,$]=le("thumb",{className:m.thumb,elementType:D1,externalForwardedProps:h,ownerState:f}),C=w.jsx(S,{...$}),[v,x]=le("track",{className:m.track,elementType:F1,externalForwardedProps:h,ownerState:f});return w.jsxs(b,{...y,children:[w.jsx(z1,{type:"checkbox",icon:C,checkedIcon:C,ref:r,ownerState:f,...d,classes:{...m,root:m.switchBase},slots:{...c.switchBase&&{root:c.switchBase},...c.input&&{input:c.input}},slotProps:{...u.switchBase&&{root:typeof u.switchBase=="function"?u.switchBase(f):u.switchBase},input:{role:"switch"},...u.input&&{input:typeof u.input=="function"?u.input(f):u.input}}}),w.jsx(v,{...x})]})});function W1(e){return ce("MuiToolbar",e)}ue("MuiToolbar",["root","gutters","regular","dense"]);const U1=e=>{const{classes:t,disableGutters:r,variant:o}=e;return de({root:["root",!r&&"gutters",o]},W1,t)},H1=j("div",{name:"MuiToolbar",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,!r.disableGutters&&t.gutters,t[r.variant]]}})(re(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",variants:[{props:({ownerState:t})=>!t.disableGutters,style:{paddingLeft:e.spacing(2),paddingRight:e.spacing(2),[e.breakpoints.up("sm")]:{paddingLeft:e.spacing(3),paddingRight:e.spacing(3)}}},{props:{variant:"dense"},style:{minHeight:48}},{props:{variant:"regular"},style:e.mixins.toolbar}]}))),V1=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiToolbar"}),{className:n,component:s="div",disableGutters:i=!1,variant:a="regular",...l}=o,c={...o,component:s,disableGutters:i,variant:a},u=U1(c);return w.jsx(H1,{as:s,className:V(u.root,n),ref:r,ownerState:c,...l})}),_1=ur(w.jsx("path",{d:"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"})),G1=ur(w.jsx("path",{d:"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"}));function K1(e){return ce("MuiTablePaginationActions",e)}ue("MuiTablePaginationActions",["root"]);const q1=e=>{const{classes:t}=e;return de({root:["root"]},K1,t)},Y1=j("div",{name:"MuiTablePaginationActions",slot:"Root"})({}),X1=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiTablePaginationActions"}),{backIconButtonProps:n,className:s,count:i,disabled:a=!1,getItemAriaLabel:l,nextIconButtonProps:c,onPageChange:u,page:d,rowsPerPage:f,showFirstButton:m,showLastButton:h,slots:b={},slotProps:y={},...S}=o,$=Mr(),v=q1(o),x=K=>{u(K,0)},k=K=>{u(K,d-1)},T=K=>{u(K,d+1)},I=K=>{u(K,Math.max(0,Math.ceil(i/f)-1))},A=b.firstButton??xo,M=b.lastButton??xo,L=b.nextButton??xo,g=b.previousButton??xo,P=b.firstButtonIcon??G1,R=b.lastButtonIcon??_1,O=b.nextButtonIcon??sd,E=b.previousButtonIcon??nd,F=$?M:A,z=$?L:g,N=$?g:L,Q=$?A:M,_=$?y.lastButton:y.firstButton,Z=$?y.nextButton:y.previousButton,se=$?y.previousButton:y.nextButton,ye=$?y.firstButton:y.lastButton;return w.jsxs(Y1,{ref:r,className:V(v.root,s),...S,children:[m&&w.jsx(F,{onClick:x,disabled:a||d===0,"aria-label":l("first",d),title:l("first",d),..._,children:$?w.jsx(R,{...y.lastButtonIcon}):w.jsx(P,{...y.firstButtonIcon})}),w.jsx(z,{onClick:k,disabled:a||d===0,color:"inherit","aria-label":l("previous",d),title:l("previous",d),...Z??n,children:$?w.jsx(O,{...y.nextButtonIcon}):w.jsx(E,{...y.previousButtonIcon})}),w.jsx(N,{onClick:T,disabled:a||(i!==-1?d>=Math.ceil(i/f)-1:!1),color:"inherit","aria-label":l("next",d),title:l("next",d),...se??c,children:$?w.jsx(E,{...y.previousButtonIcon}):w.jsx(O,{...y.nextButtonIcon})}),h&&w.jsx(Q,{onClick:I,disabled:a||d>=Math.ceil(i/f)-1,"aria-label":l("last",d),title:l("last",d),...ye,children:$?w.jsx(P,{...y.firstButtonIcon}):w.jsx(R,{...y.lastButtonIcon})})]})});function Q1(e){return ce("MuiTablePagination",e)}const an=ue("MuiTablePagination",["root","toolbar","spacer","selectLabel","selectRoot","select","selectIcon","input","menuItem","displayedRows","actions"]);var Ac;const Z1=j(Ui,{name:"MuiTablePagination",slot:"Root"})(re(({theme:e})=>({overflow:"auto",color:(e.vars||e).palette.text.primary,fontSize:e.typography.pxToRem(14),"&:last-child":{padding:0}}))),J1=j(V1,{name:"MuiTablePagination",slot:"Toolbar",overridesResolver:(e,t)=>({[`& .${an.actions}`]:t.actions,...t.toolbar})})(re(({theme:e})=>({minHeight:52,paddingRight:2,[`${e.breakpoints.up("xs")} and (orientation: landscape)`]:{minHeight:52},[e.breakpoints.up("sm")]:{minHeight:52,paddingRight:2},[`& .${an.actions}`]:{flexShrink:0,marginLeft:20}}))),ew=j("div",{name:"MuiTablePagination",slot:"Spacer"})({flex:"1 1 100%"}),tw=j("p",{name:"MuiTablePagination",slot:"SelectLabel"})(re(({theme:e})=>({...e.typography.body2,flexShrink:0}))),rw=j(Ha,{name:"MuiTablePagination",slot:"Select",overridesResolver:(e,t)=>({[`& .${an.selectIcon}`]:t.selectIcon,[`& .${an.select}`]:t.select,...t.input,...t.selectRoot})})({color:"inherit",fontSize:"inherit",flexShrink:0,marginRight:32,marginLeft:8,[`& .${an.select}`]:{paddingLeft:8,paddingRight:24,textAlign:"right",textAlignLast:"right"}}),ow=j(C0,{name:"MuiTablePagination",slot:"MenuItem"})({}),nw=j("p",{name:"MuiTablePagination",slot:"DisplayedRows"})(re(({theme:e})=>({...e.typography.body2,flexShrink:0})));function sw({from:e,to:t,count:r}){return`${e}–${t} of ${r!==-1?r:`more than ${t}`}`}function iw(e){return`Go to ${e} page`}const aw=e=>{const{classes:t}=e;return de({root:["root"],toolbar:["toolbar"],spacer:["spacer"],selectLabel:["selectLabel"],select:["select"],input:["input"],selectIcon:["selectIcon"],menuItem:["menuItem"],displayedRows:["displayedRows"],actions:["actions"]},Q1,t)},q$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiTablePagination"}),{ActionsComponent:n=X1,backIconButtonProps:s,colSpan:i,component:a=Ui,count:l,disabled:c=!1,getItemAriaLabel:u=iw,labelDisplayedRows:d=sw,labelRowsPerPage:f="Rows per page:",nextIconButtonProps:m,onPageChange:h,onRowsPerPageChange:b,page:y,rowsPerPage:S,rowsPerPageOptions:$=[10,25,50,100],SelectProps:C={},showFirstButton:v=!1,showLastButton:x=!1,slotProps:k={},slots:T={},...I}=o,A=o,M=aw(A),L=(k==null?void 0:k.select)??C,g=L.native?"option":ow;let P;(a===Ui||a==="td")&&(P=i||1e3);const R=xr(L.id),O=xr(L.labelId),E=()=>l===-1?(y+1)*S:S===-1?l:Math.min(l,(y+1)*S),F={slots:T,slotProps:k},[z,N]=le("root",{ref:r,className:M.root,elementType:Z1,externalForwardedProps:{...F,component:a,...I},ownerState:A,additionalProps:{colSpan:P}}),[Q,_]=le("toolbar",{className:M.toolbar,elementType:J1,externalForwardedProps:F,ownerState:A}),[Z,se]=le("spacer",{className:M.spacer,elementType:ew,externalForwardedProps:F,ownerState:A}),[ye,K]=le("selectLabel",{className:M.selectLabel,elementType:tw,externalForwardedProps:F,ownerState:A,additionalProps:{id:O}}),[q,ie]=le("select",{className:M.select,elementType:rw,externalForwardedProps:F,ownerState:A}),[we,he]=le("menuItem",{className:M.menuItem,elementType:g,externalForwardedProps:F,ownerState:A}),[ee,Ce]=le("displayedRows",{className:M.displayedRows,elementType:nw,externalForwardedProps:F,ownerState:A});return w.jsx(z,{...N,children:w.jsxs(Q,{..._,children:[w.jsx(Z,{...se}),$.length>1&&w.jsx(ye,{...K,children:f}),$.length>1&&w.jsx(q,{variant:"standard",...!L.variant&&{input:Ac||(Ac=w.jsx(Fs,{}))},value:S,onChange:b,id:R,labelId:O,...L,classes:{...L.classes,root:V(M.input,M.selectRoot,(L.classes||{}).root),select:V(M.select,(L.classes||{}).select),icon:V(M.selectIcon,(L.classes||{}).icon)},disabled:c,...ie,children:$.map(oe=>p.createElement(we,{...he,key:oe.label?oe.label:oe,value:oe.value?oe.value:oe},oe.label?oe.label:oe))}),w.jsx(ee,{...Ce,children:d({from:l===0?0:y*S+1,to:E(),count:l===-1?-1:l,page:y})}),w.jsx(n,{className:M.actions,backIconButtonProps:s,count:l,nextIconButtonProps:m,onPageChange:h,page:y,rowsPerPage:S,showFirstButton:v,showLastButton:x,slotProps:k.actions,slots:T.actions,getItemAriaLabel:u,disabled:c})]})})});function Oc(e){return e.substring(2).toLowerCase()}function lw(e,t){return t.documentElement.clientWidth<e.clientX||t.documentElement.clientHeight<e.clientY}function Y$(e){const{children:t,disableReactTree:r=!1,mouseEvent:o="onClick",onClickAway:n,touchEvent:s="onTouchEnd"}=e,i=p.useRef(!1),a=p.useRef(null),l=p.useRef(!1),c=p.useRef(!1);p.useEffect(()=>(setTimeout(()=>{l.current=!0},0),()=>{l.current=!1}),[]);const u=ot(Gr(t),a),d=vt(h=>{const b=c.current;c.current=!1;const y=mt(a.current);if(!l.current||!a.current||"clientX"in h&&lw(h,y))return;if(i.current){i.current=!1;return}let S;h.composedPath?S=h.composedPath().includes(a.current):S=!y.documentElement.contains(h.target)||a.current.contains(h.target),!S&&(r||!b)&&n(h)}),f=h=>b=>{c.current=!0;const y=t.props[h];y&&y(b)},m={ref:u};return s!==!1&&(m[s]=f(s)),p.useEffect(()=>{if(s!==!1){const h=Oc(s),b=mt(a.current),y=()=>{i.current=!0};return b.addEventListener(h,d),b.addEventListener("touchmove",y),()=>{b.removeEventListener(h,d),b.removeEventListener("touchmove",y)}}},[d,s]),o!==!1&&(m[o]=f(o)),p.useEffect(()=>{if(o!==!1){const h=Oc(o),b=mt(a.current);return b.addEventListener(h,d),()=>{b.removeEventListener(h,d)}}},[d,o]),p.cloneElement(t,m)}function cw(e){return ce("MuiSkeleton",e)}ue("MuiSkeleton",["root","text","rectangular","rounded","circular","pulse","wave","withChildren","fitContent","heightAuto"]);const uw=e=>{const{classes:t,variant:r,animation:o,hasChildren:n,width:s,height:i}=e;return de({root:["root",r,o,n&&"withChildren",n&&!s&&"fitContent",n&&!i&&"heightAuto"]},cw,t)},qi=wr`
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.4;
  }

  100% {
    opacity: 1;
  }
`,Yi=wr`
  0% {
    transform: translateX(-100%);
  }

  50% {
    /* +0.5s of delay between each loop */
    transform: translateX(100%);
  }

  100% {
    transform: translateX(100%);
  }
`,dw=typeof qi!="string"?_r`
        animation: ${qi} 2s ease-in-out 0.5s infinite;
      `:null,pw=typeof Yi!="string"?_r`
        &::after {
          animation: ${Yi} 2s linear 0.5s infinite;
        }
      `:null,fw=j("span",{name:"MuiSkeleton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[r.variant],r.animation!==!1&&t[r.animation],r.hasChildren&&t.withChildren,r.hasChildren&&!r.width&&t.fitContent,r.hasChildren&&!r.height&&t.heightAuto]}})(re(({theme:e})=>{const t=Rg(e.shape.borderRadius)||"px",r=Pg(e.shape.borderRadius);return{display:"block",backgroundColor:e.vars?e.vars.palette.Skeleton.bg:e.alpha(e.palette.text.primary,e.palette.mode==="light"?.11:.13),height:"1.2em",variants:[{props:{variant:"text"},style:{marginTop:0,marginBottom:0,height:"auto",transformOrigin:"0 55%",transform:"scale(1, 0.60)",borderRadius:`${r}${t}/${Math.round(r/.6*10)/10}${t}`,"&:empty:before":{content:'"\\00a0"'}}},{props:{variant:"circular"},style:{borderRadius:"50%"}},{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:({ownerState:o})=>o.hasChildren,style:{"& > *":{visibility:"hidden"}}},{props:({ownerState:o})=>o.hasChildren&&!o.width,style:{maxWidth:"fit-content"}},{props:({ownerState:o})=>o.hasChildren&&!o.height,style:{height:"auto"}},{props:{animation:"pulse"},style:dw||{animation:`${qi} 2s ease-in-out 0.5s infinite`}},{props:{animation:"wave"},style:{position:"relative",overflow:"hidden",WebkitMaskImage:"-webkit-radial-gradient(white, black)","&::after":{background:`linear-gradient(
                90deg,
                transparent,
                ${(e.vars||e).palette.action.hover},
                transparent
              )`,content:'""',position:"absolute",transform:"translateX(-100%)",bottom:0,left:0,right:0,top:0}}},{props:{animation:"wave"},style:pw||{"&::after":{animation:`${Yi} 2s linear 0.5s infinite`}}}]}})),X$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiSkeleton"}),{animation:n="pulse",className:s,component:i="span",height:a,style:l,variant:c="text",width:u,...d}=o,f={...o,animation:n,component:i,variant:c,hasChildren:!!d.children},m=uw(f);return w.jsx(fw,{as:i,ref:r,className:V(m.root,s),ownerState:f,...d,style:{width:u,height:a,...l}})});let Lc=0;function mw(e){const[t,r]=p.useState(e),o=e||t;return p.useEffect(()=>{t==null&&(Lc+=1,r(`mui-${Lc}`))},[t]),o}const gw={...ln},Bc=gw.useId;function Q$(e){if(Bc!==void 0){const t=Bc();return e??t}return mw(e)}function hw(e){return p.Children.toArray(e).filter(t=>p.isValidElement(t))}function bw(e){return ce("MuiDialogActions",e)}ue("MuiDialogActions",["root","spacing"]);const yw=e=>{const{classes:t,disableSpacing:r}=e;return de({root:["root",!r&&"spacing"]},bw,t)},vw=j("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,!r.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:e})=>!e.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),Z$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiDialogActions"}),{className:n,disableSpacing:s=!1,...i}=o,a={...o,disableSpacing:s},l=yw(a);return w.jsx(vw,{className:V(l.root,n),ownerState:a,ref:r,...i})}),xw={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",whiteSpace:"nowrap",width:"1px"};function Sw(e,t,r=(o,n)=>o===n){return e.length===t.length&&e.every((o,n)=>r(o,t[n]))}const Cw=2;function go(e,t,r,o,n){return r===1?Math.min(e+t,n):Math.max(e-t,o)}function vd(e,t){return e-t}function Nc(e,t){const{index:r}=e.reduce((o,n,s)=>{const i=Math.abs(t-n);return o===null||i<o.distance||i===o.distance?{distance:i,index:s}:o},null)??{};return r}function Un(e,t){if(t.current!==void 0&&e.changedTouches){const r=e;for(let o=0;o<r.changedTouches.length;o+=1){const n=r.changedTouches[o];if(n.identifier===t.current)return{x:n.clientX,y:n.clientY}}return!1}return{x:e.clientX,y:e.clientY}}function ds(e,t,r){return(e-t)*100/(r-t)}function ww(e,t,r){return(r-t)*e+t}function $w(e){if(Math.abs(e)<1){const r=e.toExponential().split("e-"),o=r[0].split(".")[1];return(o?o.length:0)+parseInt(r[1],10)}const t=e.toString().split(".")[1];return t?t.length:0}function kw(e,t,r){const o=Math.round((e-r)/t)*t+r;return Number(o.toFixed($w(t)))}function jc({values:e,newValue:t,index:r}){const o=e.slice();return o[r]=t,o.sort(vd)}function Hn({sliderRef:e,activeIndex:t,setActive:r}){var n,s,i;const o=mt(e.current);(!((n=e.current)!=null&&n.contains(o.activeElement))||Number((s=o==null?void 0:o.activeElement)==null?void 0:s.getAttribute("data-index"))!==t)&&((i=e.current)==null||i.querySelector(`[type="range"][data-index="${t}"]`).focus()),r&&r(t)}function Vn(e,t){return typeof e=="number"&&typeof t=="number"?e===t:typeof e=="object"&&typeof t=="object"?Sw(e,t):!1}const Rw={horizontal:{offset:e=>({left:`${e}%`}),leap:e=>({width:`${e}%`})},"horizontal-reverse":{offset:e=>({right:`${e}%`}),leap:e=>({width:`${e}%`})},vertical:{offset:e=>({bottom:`${e}%`}),leap:e=>({height:`${e}%`})}},Pw=e=>e;let _n;function zc(){return _n===void 0&&(typeof CSS<"u"&&typeof CSS.supports=="function"?_n=CSS.supports("touch-action","none"):_n=!0),_n}function Tw(e){const{"aria-labelledby":t,defaultValue:r,disabled:o=!1,disableSwap:n=!1,isRtl:s=!1,marks:i=!1,max:a=100,min:l=0,name:c,onChange:u,onChangeCommitted:d,orientation:f="horizontal",rootRef:m,scale:h=Pw,step:b=1,shiftStep:y=10,tabIndex:S,value:$}=e,C=p.useRef(void 0),[v,x]=p.useState(-1),[k,T]=p.useState(-1),[I,A]=p.useState(!1),M=p.useRef(0),L=p.useRef(null),[g,P]=Wr({controlled:$,default:r??l,name:"Slider"}),R=u&&((H,Y,ae)=>{const xe=H.nativeEvent||H,Te=new xe.constructor(xe.type,xe);Object.defineProperty(Te,"target",{writable:!0,value:{value:Y,name:c}}),L.current=Y,u(Te,Y,ae)}),O=Array.isArray(g);let E=O?g.slice().sort(vd):[g];E=E.map(H=>H==null?l:ho(H,l,a));const F=i===!0&&b!==null?[...Array(Math.floor((a-l)/b)+1)].map((H,Y)=>({value:l+b*Y})):i||[],z=F.map(H=>H.value),[N,Q]=p.useState(-1),_=p.useRef(null),Z=ot(m,_),se=H=>Y=>{var xe;const ae=Number(Y.currentTarget.getAttribute("data-index"));Vr(Y.target)&&Q(ae),T(ae),(xe=H==null?void 0:H.onFocus)==null||xe.call(H,Y)},ye=H=>Y=>{var ae;Vr(Y.target)||Q(-1),T(-1),(ae=H==null?void 0:H.onBlur)==null||ae.call(H,Y)},K=(H,Y)=>{const ae=Number(H.currentTarget.getAttribute("data-index")),xe=E[ae],Te=z.indexOf(xe);let X=Y;if(F&&b==null){const G=z[z.length-1];X>=G?X=G:X<=z[0]?X=z[0]:X=X<xe?z[Te-1]:z[Te+1]}if(X=ho(X,l,a),O){n&&(X=ho(X,E[ae-1]||-1/0,E[ae+1]||1/0));const G=X;X=jc({values:E,newValue:X,index:ae});let Pe=ae;n||(Pe=X.indexOf(G)),Hn({sliderRef:_,activeIndex:Pe})}P(X),Q(ae),R&&!Vn(X,g)&&R(H,X,ae),d&&d(H,L.current??X)},q=H=>Y=>{var ae;if(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight","PageUp","PageDown","Home","End"].includes(Y.key)){Y.preventDefault();const xe=Number(Y.currentTarget.getAttribute("data-index")),Te=E[xe];let X=null;if(b!=null){const G=Y.shiftKey?y:b;switch(Y.key){case"ArrowUp":X=go(Te,G,1,l,a);break;case"ArrowRight":X=go(Te,G,s?-1:1,l,a);break;case"ArrowDown":X=go(Te,G,-1,l,a);break;case"ArrowLeft":X=go(Te,G,s?1:-1,l,a);break;case"PageUp":X=go(Te,y,1,l,a);break;case"PageDown":X=go(Te,y,-1,l,a);break;case"Home":X=l;break;case"End":X=a;break}}else if(F){const G=z[z.length-1],Pe=z.indexOf(Te),Ie=[s?"ArrowRight":"ArrowLeft","ArrowDown","PageDown","Home"],ke=[s?"ArrowLeft":"ArrowRight","ArrowUp","PageUp","End"];Ie.includes(Y.key)?Pe===0?X=z[0]:X=z[Pe-1]:ke.includes(Y.key)&&(Pe===z.length-1?X=G:X=z[Pe+1])}X!=null&&K(Y,X)}(ae=H==null?void 0:H.onKeyDown)==null||ae.call(H,Y)};It(()=>{var H;o&&_.current.contains(document.activeElement)&&((H=document.activeElement)==null||H.blur())},[o]),o&&v!==-1&&x(-1),o&&N!==-1&&Q(-1);const ie=H=>Y=>{var ae;(ae=H.onChange)==null||ae.call(H,Y),K(Y,Y.target.valueAsNumber)},we=p.useRef(void 0);let he=f;s&&f==="horizontal"&&(he+="-reverse");const ee=({finger:H,move:Y=!1})=>{const{current:ae}=_,{width:xe,height:Te,bottom:X,left:G}=ae.getBoundingClientRect();let Pe;he.startsWith("vertical")?Pe=(X-H.y)/Te:Pe=(H.x-G)/xe,he.includes("-reverse")&&(Pe=1-Pe);let Ie;if(Ie=ww(Pe,l,a),b)Ie=kw(Ie,b,l);else{const dt=Nc(z,Ie);Ie=z[dt]}Ie=ho(Ie,l,a);let ke=0;if(O){Y?ke=we.current:ke=Nc(E,Ie),n&&(Ie=ho(Ie,E[ke-1]||-1/0,E[ke+1]||1/0));const dt=Ie;Ie=jc({values:E,newValue:Ie,index:ke}),n&&Y||(ke=Ie.indexOf(dt),we.current=ke)}return{newValue:Ie,activeIndex:ke}},Ce=vt(H=>{const Y=Un(H,C);if(!Y)return;if(M.current+=1,H.type==="mousemove"&&H.buttons===0){oe(H);return}const{newValue:ae,activeIndex:xe}=ee({finger:Y,move:!0});Hn({sliderRef:_,activeIndex:xe,setActive:x}),P(ae),!I&&M.current>Cw&&A(!0),R&&!Vn(ae,g)&&R(H,ae,xe)}),oe=vt(H=>{const Y=Un(H,C);if(A(!1),!Y)return;const{newValue:ae}=ee({finger:Y,move:!0});x(-1),H.type==="touchend"&&T(-1),d&&d(H,L.current??ae),C.current=void 0,ve()}),ge=vt(H=>{if(o)return;zc()||H.preventDefault();const Y=H.changedTouches[0];Y!=null&&(C.current=Y.identifier);const ae=Un(H,C);if(ae!==!1){const{newValue:Te,activeIndex:X}=ee({finger:ae});Hn({sliderRef:_,activeIndex:X,setActive:x}),P(Te),R&&!Vn(Te,g)&&R(H,Te,X)}M.current=0;const xe=mt(_.current);xe.addEventListener("touchmove",Ce,{passive:!0}),xe.addEventListener("touchend",oe,{passive:!0})}),ve=p.useCallback(()=>{const H=mt(_.current);H.removeEventListener("mousemove",Ce),H.removeEventListener("mouseup",oe),H.removeEventListener("touchmove",Ce),H.removeEventListener("touchend",oe)},[oe,Ce]);p.useEffect(()=>{const{current:H}=_;return H.addEventListener("touchstart",ge,{passive:zc()}),()=>{H.removeEventListener("touchstart",ge),ve()}},[ve,ge]),p.useEffect(()=>{o&&ve()},[o,ve]);const Le=H=>Y=>{var Te;if((Te=H.onMouseDown)==null||Te.call(H,Y),o||Y.defaultPrevented||Y.button!==0)return;Y.preventDefault();const ae=Un(Y,C);if(ae!==!1){const{newValue:X,activeIndex:G}=ee({finger:ae});Hn({sliderRef:_,activeIndex:G,setActive:x}),P(X),R&&!Vn(X,g)&&R(Y,X,G)}M.current=0;const xe=mt(_.current);xe.addEventListener("mousemove",Ce,{passive:!0}),xe.addEventListener("mouseup",oe)},Me=ds(O?E[0]:l,l,a),$e=ds(E[E.length-1],l,a)-Me,J=(H={})=>{const Y=en(H),ae={onMouseDown:Le(Y||{})},xe={...Y,...ae};return{...H,ref:Z,...xe}},Ae=H=>Y=>{var xe;(xe=H.onMouseOver)==null||xe.call(H,Y);const ae=Number(Y.currentTarget.getAttribute("data-index"));T(ae)},pe=H=>Y=>{var ae;(ae=H.onMouseLeave)==null||ae.call(H,Y),T(-1)},Re=(H={})=>{const Y=en(H),ae={onMouseOver:Ae(Y||{}),onMouseLeave:pe(Y||{})};return{...H,...Y,...ae}},te=H=>({pointerEvents:v!==-1&&v!==H?"none":void 0});let Ge;return f==="vertical"&&(Ge=s?"vertical-rl":"vertical-lr"),{active:v,axis:he,axisProps:Rw,dragging:I,focusedThumbIndex:N,getHiddenInputProps:(H={})=>{const Y=en(H),ae={onChange:ie(Y||{}),onFocus:se(Y||{}),onBlur:ye(Y||{}),onKeyDown:q(Y||{})},xe={...Y,...ae};return{tabIndex:S,"aria-labelledby":t,"aria-orientation":f,"aria-valuemax":h(a),"aria-valuemin":h(l),name:c,type:"range",min:e.min,max:e.max,step:e.step===null&&e.marks?"any":e.step??void 0,disabled:o,...H,...xe,style:{...xw,direction:s?"rtl":"ltr",width:"100%",height:"100%",writingMode:Ge}}},getRootProps:J,getThumbProps:Re,marks:F,open:k,range:O,rootRef:Z,trackLeap:$e,trackOffset:Me,values:E,getThumbStyle:te}}const Iw=e=>!e||!yr(e);function Ew(e){return ce("MuiSlider",e)}const Qt=ue("MuiSlider",["root","active","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","disabled","dragging","focusVisible","mark","markActive","marked","markLabel","markLabelActive","rail","sizeSmall","thumb","thumbColorPrimary","thumbColorSecondary","thumbColorError","thumbColorSuccess","thumbColorInfo","thumbColorWarning","track","trackInverted","trackFalse","thumbSizeSmall","valueLabel","valueLabelOpen","valueLabelCircle","valueLabelLabel","vertical"]),Mw=e=>{const{open:t}=e;return{offset:V(t&&Qt.valueLabelOpen),circle:Qt.valueLabelCircle,label:Qt.valueLabelLabel}};function Aw(e){const{children:t,className:r,value:o}=e,n=Mw(e);return t?p.cloneElement(t,{className:t.props.className},w.jsxs(p.Fragment,{children:[t.props.children,w.jsx("span",{className:V(n.offset,r),"aria-hidden":!0,children:w.jsx("span",{className:n.circle,children:w.jsx("span",{className:n.label,children:o})})})]})):null}function Fc(e){return e}const Ow=j("span",{name:"MuiSlider",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[`color${B(r.color)}`],r.size!=="medium"&&t[`size${B(r.size)}`],r.marked&&t.marked,r.orientation==="vertical"&&t.vertical,r.track==="inverted"&&t.trackInverted,r.track===!1&&t.trackFalse]}})(re(({theme:e})=>({borderRadius:12,boxSizing:"content-box",display:"inline-block",position:"relative",cursor:"pointer",touchAction:"none",WebkitTapHighlightColor:"transparent","@media print":{colorAdjust:"exact"},[`&.${Qt.disabled}`]:{pointerEvents:"none",cursor:"default",color:(e.vars||e).palette.grey[400]},[`&.${Qt.dragging}`]:{[`& .${Qt.thumb}, & .${Qt.track}`]:{transition:"none"}},variants:[...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}})),{props:{orientation:"horizontal"},style:{height:4,width:"100%",padding:"13px 0","@media (pointer: coarse)":{padding:"20px 0"}}},{props:{orientation:"horizontal",size:"small"},style:{height:2}},{props:{orientation:"horizontal",marked:!0},style:{marginBottom:20}},{props:{orientation:"vertical"},style:{height:"100%",width:4,padding:"0 13px","@media (pointer: coarse)":{padding:"0 20px"}}},{props:{orientation:"vertical",size:"small"},style:{width:2}},{props:{orientation:"vertical",marked:!0},style:{marginRight:44}}]}))),Lw=j("span",{name:"MuiSlider",slot:"Rail"})({display:"block",position:"absolute",borderRadius:"inherit",backgroundColor:"currentColor",opacity:.38,variants:[{props:{orientation:"horizontal"},style:{width:"100%",height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{height:"100%",width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:"inverted"},style:{opacity:1}}]}),Bw=j("span",{name:"MuiSlider",slot:"Track"})(re(({theme:e})=>({display:"block",position:"absolute",borderRadius:"inherit",border:"1px solid currentColor",backgroundColor:"currentColor",transition:e.transitions.create(["left","width","bottom","height"],{duration:e.transitions.duration.shortest}),variants:[{props:{size:"small"},style:{border:"none"}},{props:{orientation:"horizontal"},style:{height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:!1},style:{display:"none"}},...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t,track:"inverted"},style:{...e.vars?{backgroundColor:e.vars.palette.Slider[`${t}Track`],borderColor:e.vars.palette.Slider[`${t}Track`]}:{backgroundColor:e.lighten(e.palette[t].main,.62),borderColor:e.lighten(e.palette[t].main,.62),...e.applyStyles("dark",{backgroundColor:e.darken(e.palette[t].main,.5)}),...e.applyStyles("dark",{borderColor:e.darken(e.palette[t].main,.5)})}}}))]}))),Nw=j("span",{name:"MuiSlider",slot:"Thumb",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.thumb,t[`thumbColor${B(r.color)}`],r.size!=="medium"&&t[`thumbSize${B(r.size)}`]]}})(re(({theme:e})=>({position:"absolute",width:20,height:20,boxSizing:"border-box",borderRadius:"50%",outline:0,backgroundColor:"currentColor",display:"flex",alignItems:"center",justifyContent:"center",transition:e.transitions.create(["box-shadow","left","bottom"],{duration:e.transitions.duration.shortest}),"&::before":{position:"absolute",content:'""',borderRadius:"inherit",width:"100%",height:"100%",boxShadow:(e.vars||e).shadows[2]},"&::after":{position:"absolute",content:'""',borderRadius:"50%",width:42,height:42,top:"50%",left:"50%",transform:"translate(-50%, -50%)"},[`&.${Qt.disabled}`]:{"&:hover":{boxShadow:"none"}},variants:[{props:{size:"small"},style:{width:12,height:12,"&::before":{boxShadow:"none"}}},{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-50%, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 50%)"}},...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t},style:{[`&:hover, &.${Qt.focusVisible}`]:{boxShadow:`0px 0px 0px 8px ${e.alpha((e.vars||e).palette[t].main,.16)}`,"@media (hover: none)":{boxShadow:"none"}},[`&.${Qt.active}`]:{boxShadow:`0px 0px 0px 14px ${e.alpha((e.vars||e).palette[t].main,.16)}`}}}))]}))),jw=j(Aw,{name:"MuiSlider",slot:"ValueLabel"})(re(({theme:e})=>({zIndex:1,whiteSpace:"nowrap",...e.typography.body2,fontWeight:500,transition:e.transitions.create(["transform"],{duration:e.transitions.duration.shortest}),position:"absolute",backgroundColor:(e.vars||e).palette.grey[600],borderRadius:2,color:(e.vars||e).palette.common.white,display:"flex",alignItems:"center",justifyContent:"center",padding:"0.25rem 0.75rem",variants:[{props:{orientation:"horizontal"},style:{transform:"translateY(-100%) scale(0)",top:"-10px",transformOrigin:"bottom center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, 50%) rotate(45deg)",backgroundColor:"inherit",bottom:0,left:"50%"},[`&.${Qt.valueLabelOpen}`]:{transform:"translateY(-100%) scale(1)"}}},{props:{orientation:"vertical"},style:{transform:"translateY(-50%) scale(0)",right:"30px",top:"50%",transformOrigin:"right center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, -50%) rotate(45deg)",backgroundColor:"inherit",right:-8,top:"50%"},[`&.${Qt.valueLabelOpen}`]:{transform:"translateY(-50%) scale(1)"}}},{props:{size:"small"},style:{fontSize:e.typography.pxToRem(12),padding:"0.25rem 0.5rem"}},{props:{orientation:"vertical",size:"small"},style:{right:"20px"}}]}))),zw=j("span",{name:"MuiSlider",slot:"Mark",shouldForwardProp:e=>Ms(e)&&e!=="markActive",overridesResolver:(e,t)=>{const{markActive:r}=e;return[t.mark,r&&t.markActive]}})(re(({theme:e})=>({position:"absolute",width:2,height:2,borderRadius:1,backgroundColor:"currentColor",variants:[{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-1px, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 1px)"}},{props:{markActive:!0},style:{backgroundColor:(e.vars||e).palette.background.paper,opacity:.8}}]}))),Fw=j("span",{name:"MuiSlider",slot:"MarkLabel",shouldForwardProp:e=>Ms(e)&&e!=="markLabelActive"})(re(({theme:e})=>({...e.typography.body2,color:(e.vars||e).palette.text.secondary,position:"absolute",whiteSpace:"nowrap",variants:[{props:{orientation:"horizontal"},style:{top:30,transform:"translateX(-50%)","@media (pointer: coarse)":{top:40}}},{props:{orientation:"vertical"},style:{left:36,transform:"translateY(50%)","@media (pointer: coarse)":{left:44}}},{props:{markLabelActive:!0},style:{color:(e.vars||e).palette.text.primary}}]}))),Dw=e=>{const{disabled:t,dragging:r,marked:o,orientation:n,track:s,classes:i,color:a,size:l}=e,c={root:["root",t&&"disabled",r&&"dragging",o&&"marked",n==="vertical"&&"vertical",s==="inverted"&&"trackInverted",s===!1&&"trackFalse",a&&`color${B(a)}`,l&&`size${B(l)}`],rail:["rail"],track:["track"],mark:["mark"],markActive:["markActive"],markLabel:["markLabel"],markLabelActive:["markLabelActive"],valueLabel:["valueLabel"],thumb:["thumb",t&&"disabled",l&&`thumbSize${B(l)}`,a&&`thumbColor${B(a)}`],active:["active"],disabled:["disabled"],focusVisible:["focusVisible"]};return de(c,Ew,i)},Ww=({children:e})=>e,J$=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiSlider"}),n=Mr(),{"aria-label":s,"aria-valuetext":i,"aria-labelledby":a,component:l="span",components:c={},componentsProps:u={},color:d="primary",classes:f,className:m,disableSwap:h=!1,disabled:b=!1,getAriaLabel:y,getAriaValueText:S,marks:$=!1,max:C=100,min:v=0,name:x,onChange:k,onChangeCommitted:T,orientation:I="horizontal",shiftStep:A=10,size:M="medium",step:L=1,scale:g=Fc,slotProps:P,slots:R,tabIndex:O,track:E="normal",value:F,valueLabelDisplay:z="off",valueLabelFormat:N=Fc,...Q}=o,_={...o,isRtl:n,max:C,min:v,classes:f,disabled:b,disableSwap:h,orientation:I,marks:$,color:d,size:M,step:L,shiftStep:A,scale:g,track:E,valueLabelDisplay:z,valueLabelFormat:N},{axisProps:Z,getRootProps:se,getHiddenInputProps:ye,getThumbProps:K,open:q,active:ie,axis:we,focusedThumbIndex:he,range:ee,dragging:Ce,marks:oe,values:ge,trackOffset:ve,trackLeap:Le,getThumbStyle:Me}=Tw({..._,rootRef:r});_.marked=oe.length>0&&oe.some(Ne=>Ne.label),_.dragging=Ce,_.focusedThumbIndex=he;const $e=Dw(_),J=(R==null?void 0:R.root)??c.Root??Ow,Ae=(R==null?void 0:R.rail)??c.Rail??Lw,pe=(R==null?void 0:R.track)??c.Track??Bw,Re=(R==null?void 0:R.thumb)??c.Thumb??Nw,te=(R==null?void 0:R.valueLabel)??c.ValueLabel??jw,Ge=(R==null?void 0:R.mark)??c.Mark??zw,je=(R==null?void 0:R.markLabel)??c.MarkLabel??Fw,H=(R==null?void 0:R.input)??c.Input??"input",Y=(P==null?void 0:P.root)??u.root,ae=(P==null?void 0:P.rail)??u.rail,xe=(P==null?void 0:P.track)??u.track,Te=(P==null?void 0:P.thumb)??u.thumb,X=(P==null?void 0:P.valueLabel)??u.valueLabel,G=(P==null?void 0:P.mark)??u.mark,Pe=(P==null?void 0:P.markLabel)??u.markLabel,Ie=(P==null?void 0:P.input)??u.input,ke=Ot({elementType:J,getSlotProps:se,externalSlotProps:Y,externalForwardedProps:Q,additionalProps:{...Iw(J)&&{as:l}},ownerState:{..._,...Y==null?void 0:Y.ownerState},className:[$e.root,m]}),dt=Ot({elementType:Ae,externalSlotProps:ae,ownerState:_,className:$e.rail}),bt=Ot({elementType:pe,externalSlotProps:xe,additionalProps:{style:{...Z[we].offset(ve),...Z[we].leap(Le)}},ownerState:{..._,...xe==null?void 0:xe.ownerState},className:$e.track}),nt=Ot({elementType:Re,getSlotProps:K,externalSlotProps:Te,ownerState:{..._,...Te==null?void 0:Te.ownerState},className:$e.thumb}),kt=Ot({elementType:te,externalSlotProps:X,ownerState:{..._,...X==null?void 0:X.ownerState},className:$e.valueLabel}),be=Ot({elementType:Ge,externalSlotProps:G,ownerState:_,className:$e.mark}),Fe=Ot({elementType:je,externalSlotProps:Pe,ownerState:_,className:$e.markLabel}),He=Ot({elementType:H,getSlotProps:ye,externalSlotProps:Ie,ownerState:_});return w.jsxs(J,{...ke,children:[w.jsx(Ae,{...dt}),w.jsx(pe,{...bt}),oe.filter(Ne=>Ne.value>=v&&Ne.value<=C).map((Ne,Ve)=>{const Rt=ds(Ne.value,v,C),St=Z[we].offset(Rt);let st;return E===!1?st=ge.includes(Ne.value):st=E==="normal"&&(ee?Ne.value>=ge[0]&&Ne.value<=ge[ge.length-1]:Ne.value<=ge[0])||E==="inverted"&&(ee?Ne.value<=ge[0]||Ne.value>=ge[ge.length-1]:Ne.value>=ge[0]),w.jsxs(p.Fragment,{children:[w.jsx(Ge,{"data-index":Ve,...be,...!yr(Ge)&&{markActive:st},style:{...St,...be.style},className:V(be.className,st&&$e.markActive)}),Ne.label!=null?w.jsx(je,{"aria-hidden":!0,"data-index":Ve,...Fe,...!yr(je)&&{markLabelActive:st},style:{...St,...Fe.style},className:V($e.markLabel,Fe.className,st&&$e.markLabelActive),children:Ne.label}):null]},Ve)}),ge.map((Ne,Ve)=>{const Rt=ds(Ne,v,C),St=Z[we].offset(Rt),st=z==="off"?Ww:te;return w.jsx(st,{...!yr(st)&&{valueLabelFormat:N,valueLabelDisplay:z,value:typeof N=="function"?N(g(Ne),Ve):N,index:Ve,open:q===Ve||ie===Ve||z==="on",disabled:b},...kt,children:w.jsx(Re,{"data-index":Ve,...nt,className:V($e.thumb,nt.className,ie===Ve&&$e.active,he===Ve&&$e.focusVisible),style:{...St,...Me(Ve),...nt.style},children:w.jsx(H,{"data-index":Ve,"aria-label":y?y(Ve):s,"aria-valuenow":g(Ne),"aria-labelledby":a,"aria-valuetext":S?S(g(Ne),Ve):i,value:ge[Ve],...He})})},Ve)})]})});function Uw(e){return ce("MuiToggleButton",e)}const eo=ue("MuiToggleButton",["root","disabled","selected","standard","primary","secondary","sizeSmall","sizeMedium","sizeLarge","fullWidth"]),xd=p.createContext({}),Sd=p.createContext(void 0);function Hw(e,t){return t===void 0||e===void 0?!1:Array.isArray(t)?t.includes(e):e===t}const Vw=e=>{const{classes:t,fullWidth:r,selected:o,disabled:n,size:s,color:i}=e,a={root:["root",o&&"selected",n&&"disabled",r&&"fullWidth",`size${B(s)}`,i]};return de(a,Uw,t)},_w=j(Sr,{name:"MuiToggleButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[`size${B(r.size)}`]]}})(re(({theme:e})=>({...e.typography.button,borderRadius:(e.vars||e).shape.borderRadius,padding:11,border:`1px solid ${(e.vars||e).palette.divider}`,color:(e.vars||e).palette.action.active,[`&.${eo.disabled}`]:{color:(e.vars||e).palette.action.disabled,border:`1px solid ${(e.vars||e).palette.action.disabledBackground}`},"&:hover":{textDecoration:"none",backgroundColor:e.alpha((e.vars||e).palette.text.primary,(e.vars||e).palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[{props:{color:"standard"},style:{[`&.${eo.selected}`]:{color:(e.vars||e).palette.text.primary,backgroundColor:e.alpha((e.vars||e).palette.text.primary,(e.vars||e).palette.action.selectedOpacity),"&:hover":{backgroundColor:e.alpha((e.vars||e).palette.text.primary,`${(e.vars||e).palette.action.selectedOpacity} + ${(e.vars||e).palette.action.hoverOpacity}`),"@media (hover: none)":{backgroundColor:e.alpha((e.vars||e).palette.text.primary,(e.vars||e).palette.action.selectedOpacity)}}}}},...Object.entries(e.palette).filter(et()).map(([t])=>({props:{color:t},style:{[`&.${eo.selected}`]:{color:(e.vars||e).palette[t].main,backgroundColor:e.alpha((e.vars||e).palette[t].main,(e.vars||e).palette.action.selectedOpacity),"&:hover":{backgroundColor:e.alpha((e.vars||e).palette[t].main,`${(e.vars||e).palette.action.selectedOpacity} + ${(e.vars||e).palette.action.hoverOpacity}`),"@media (hover: none)":{backgroundColor:e.alpha((e.vars||e).palette[t].main,(e.vars||e).palette.action.selectedOpacity)}}}}})),{props:{fullWidth:!0},style:{width:"100%"}},{props:{size:"small"},style:{padding:7,fontSize:e.typography.pxToRem(13)}},{props:{size:"large"},style:{padding:15,fontSize:e.typography.pxToRem(15)}}]}))),ek=p.forwardRef(function(t,r){const{value:o,...n}=p.useContext(xd),s=p.useContext(Sd),i=Po({...n,selected:Hw(t.value,o)},t),a=me({props:i,name:"MuiToggleButton"}),{children:l,className:c,color:u="standard",disabled:d=!1,disableFocusRipple:f=!1,fullWidth:m=!1,onChange:h,onClick:b,selected:y,size:S="medium",value:$,...C}=a,v={...a,color:u,disabled:d,disableFocusRipple:f,fullWidth:m,size:S},x=Vw(v),k=I=>{b&&(b(I,$),I.defaultPrevented)||h&&h(I,$)},T=s||"";return w.jsx(_w,{className:V(n.className,x.root,c,T),disabled:d,focusRipple:!f,ref:r,onClick:k,onChange:h,value:$,ownerState:v,"aria-pressed":y,...C,children:l})});function Gw(e){return ce("MuiToggleButtonGroup",e)}const tt=ue("MuiToggleButtonGroup",["root","selected","horizontal","vertical","disabled","grouped","groupedHorizontal","groupedVertical","fullWidth","firstButton","lastButton","middleButton"]),Kw=e=>{const{classes:t,orientation:r,fullWidth:o,disabled:n}=e,s={root:["root",r,o&&"fullWidth"],grouped:["grouped",`grouped${B(r)}`,n&&"disabled"],firstButton:["firstButton"],lastButton:["lastButton"],middleButton:["middleButton"]};return de(s,Gw,t)},qw=j("div",{name:"MuiToggleButtonGroup",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[{[`& .${tt.grouped}`]:t.grouped},{[`& .${tt.grouped}`]:t[`grouped${B(r.orientation)}`]},{[`& .${tt.firstButton}`]:t.firstButton},{[`& .${tt.lastButton}`]:t.lastButton},{[`& .${tt.middleButton}`]:t.middleButton},t.root,r.orientation==="vertical"&&t.vertical,r.fullWidth&&t.fullWidth]}})(re(({theme:e})=>({display:"inline-flex",borderRadius:(e.vars||e).shape.borderRadius,variants:[{props:{orientation:"vertical"},style:{flexDirection:"column",[`& .${tt.grouped}`]:{[`&.${tt.selected} + .${tt.grouped}.${tt.selected}`]:{borderTop:0,marginTop:0}},[`& .${tt.firstButton},& .${tt.middleButton}`]:{borderBottomLeftRadius:0,borderBottomRightRadius:0},[`& .${tt.lastButton},& .${tt.middleButton}`]:{marginTop:-1,borderTop:"1px solid transparent",borderTopLeftRadius:0,borderTopRightRadius:0},[`& .${tt.lastButton}.${eo.disabled},& .${tt.middleButton}.${eo.disabled}`]:{borderTop:"1px solid transparent"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{orientation:"horizontal"},style:{[`& .${tt.grouped}`]:{[`&.${tt.selected} + .${tt.grouped}.${tt.selected}`]:{borderLeft:0,marginLeft:0}},[`& .${tt.firstButton},& .${tt.middleButton}`]:{borderTopRightRadius:0,borderBottomRightRadius:0},[`& .${tt.lastButton},& .${tt.middleButton}`]:{marginLeft:-1,borderLeft:"1px solid transparent",borderTopLeftRadius:0,borderBottomLeftRadius:0},[`& .${tt.lastButton}.${eo.disabled},& .${tt.middleButton}.${eo.disabled}`]:{borderLeft:"1px solid transparent"}}}]}))),tk=p.forwardRef(function(t,r){const o=me({props:t,name:"MuiToggleButtonGroup"}),{children:n,className:s,color:i="standard",disabled:a=!1,exclusive:l=!1,fullWidth:c=!1,onChange:u,orientation:d="horizontal",size:f="medium",value:m,...h}=o,b={...o,disabled:a,fullWidth:c,orientation:d,size:f},y=Kw(b),S=p.useCallback((T,I)=>{if(!u)return;const A=m&&m.indexOf(I);let M;m&&A>=0?(M=m.slice(),M.splice(A,1)):M=m?m.concat(I):[I],u(T,M)},[u,m]),$=p.useCallback((T,I)=>{u&&u(T,m===I?null:I)},[u,m]),C=p.useMemo(()=>({className:y.grouped,onChange:l?$:S,value:m,size:f,fullWidth:c,color:i,disabled:a}),[y.grouped,l,$,S,m,f,c,i,a]),v=hw(n),x=v.length,k=T=>{const I=T===0,A=T===x-1;return I&&A?"":I?y.firstButton:A?y.lastButton:y.middleButton};return w.jsx(qw,{role:"group",className:V(y.root,s),ref:r,ownerState:b,...h,children:w.jsx(xd.Provider,{value:C,children:v.map((T,I)=>w.jsx(Sd.Provider,{value:k(I),children:T},I))})})}),rk=hu({themeId:Zt}),ok=ti.oneOfType([ti.func,ti.object]);function Yw(e,t,r){return typeof e=="function"?e(t,r):e}function nk(e){const t=p.useRef(e);return ov(()=>{t.current=e}),p.useRef((...r)=>(0,t.current)(...r)).current}function Xw(...e){const t=p.useRef(void 0),r=p.useCallback(o=>{const n=e.map(s=>{if(s==null)return null;if(typeof s=="function"){const i=s,a=i(o);return typeof a=="function"?a:()=>{i(null)}}return s.current=o,()=>{s.current=null}});return()=>{n.forEach(s=>s==null?void 0:s())}},e);return p.useMemo(()=>e.every(o=>o==null)?null:o=>{t.current&&(t.current(),t.current=void 0),o!=null&&(t.current=r(o))},e)}function Qw(e){return typeof e=="string"}function Zw(e,t,r){return e===void 0||Qw(e)?t:{...t,ownerState:{...t.ownerState,...r}}}function Jw(e,t=[]){if(e===void 0)return{};const r={};return Object.keys(e).filter(o=>o.match(/^on[A-Z]/)&&typeof e[o]=="function"&&!t.includes(o)).forEach(o=>{r[o]=e[o]}),r}function Dc(e){if(e===void 0)return{};const t={};return Object.keys(e).filter(r=>!(r.match(/^on[A-Z]/)&&typeof e[r]=="function")).forEach(r=>{t[r]=e[r]}),t}function e$(e){const{getSlotProps:t,additionalProps:r,externalSlotProps:o,externalForwardedProps:n,className:s}=e;if(!t){const m=V(r==null?void 0:r.className,s,n==null?void 0:n.className,o==null?void 0:o.className),h={...r==null?void 0:r.style,...n==null?void 0:n.style,...o==null?void 0:o.style},b={...r,...n,...o};return m.length>0&&(b.className=m),Object.keys(h).length>0&&(b.style=h),{props:b,internalRef:void 0}}const i=Jw({...n,...o}),a=Dc(o),l=Dc(n),c=t(i),u=V(c==null?void 0:c.className,r==null?void 0:r.className,s,n==null?void 0:n.className,o==null?void 0:o.className),d={...c==null?void 0:c.style,...r==null?void 0:r.style,...n==null?void 0:n.style,...o==null?void 0:o.style},f={...c,...r,...l,...a};return u.length>0&&(f.className=u),Object.keys(d).length>0&&(f.style=d),{props:f,internalRef:c.ref}}function sk(e){var d;const{elementType:t,externalSlotProps:r,ownerState:o,skipResolvingSlotProps:n=!1,...s}=e,i=n?{}:Yw(r,o),{props:a,internalRef:l}=e$({...s,externalSlotProps:i}),c=Xw(l,i==null?void 0:i.ref,(d=e.additionalProps)==null?void 0:d.ref);return Zw(t,{...a,ref:c},o)}function ik(e){return e&&e.ownerDocument||document}function ak(e){const{controlled:t,default:r,name:o,state:n="value"}=e,{current:s}=p.useRef(t!==void 0),[i,a]=p.useState(r),l=s?t:i,c=p.useCallback(u=>{s||a(u)},[]);return[l,c]}const lk={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",whiteSpace:"nowrap",width:"1px"},Wc={};function t$(e,t){const r=p.useRef(Wc);return r.current===Wc&&(r.current=e(t)),r}const r$=[];function o$(e){p.useEffect(e,r$)}class Va{constructor(){Nr(this,"currentId",null);Nr(this,"clear",()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)});Nr(this,"disposeEffect",()=>this.clear)}static create(){return new Va}start(t,r){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,r()},t)}}function ck(){const e=t$(Va.create).current;return o$(e.disposeEffect),e}export{op as $,g$ as A,u$ as B,p$ as C,x$ as D,jt as E,mc as F,Og as G,_t as H,xo as I,M$ as J,D0 as K,Wv as L,b0 as M,Vt as N,Ma as O,rv as P,hi as Q,Yt as R,f$ as S,Xt as T,ma as U,c$ as V,E$ as W,ba as X,a$ as Y,i$ as Z,cn as _,Uc as a,Fs as a$,hx as a0,ov as a1,B$ as a2,j$ as a3,N$ as a4,V as a5,ti as a6,z$ as a7,so as a8,L$ as a9,Z$ as aA,Ar as aB,cd as aC,lk as aD,_$ as aE,Yw as aF,ck as aG,$r as aH,xa as aI,Sr as aJ,fn as aK,Au as aL,ok as aM,mi as aN,c0 as aO,Ha as aP,J$ as aQ,W$ as aR,tk as aS,ek as aT,ln as aU,qS as aV,wr as aW,Ts as aX,Ps as aY,X$ as aZ,V$ as a_,ur as aa,O$ as ab,A$ as ac,Q$ as ad,a1 as ae,H$ as af,d1 as ag,U$ as ah,G$ as ai,K$ as aj,dd as ak,F$ as al,D$ as am,j as an,ZS as ao,yi as ap,nk as aq,Xw as ar,Di as as,ss as at,zy as au,sk as av,ik as aw,Os as ax,rk as ay,ak as az,d$ as b,Qo as b0,q$ as b1,an as b2,So as b3,Y$ as b4,ic as b5,Hl as b6,ot as b7,YS as b8,S$ as c,C$ as d,Bu as e,h$ as f,Ul as g,k$ as h,$$ as i,w as j,R$ as k,P$ as l,w$ as m,En as n,C0 as o,T$ as p,I$ as q,p as r,Xh as s,b$ as t,l$ as u,y$ as v,Yr as w,v$ as x,m$ as y,Ue as z};
//# sourceMappingURL=mui.DwnGmjhN.js.map
