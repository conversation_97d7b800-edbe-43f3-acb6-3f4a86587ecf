import{A as e}from"./index-mpIrzq_n.js";const n=e.injectEndpoints({endpoints:i=>({atApiServiceEndpointsAdministrationJobsDeleteJobEndpoint:i.mutation({query:t=>({url:`/api/v2/jobs/${t.entityId}`,method:"DELETE"})}),atApiServiceEndpointsAdministrationJobsPutJobEndpoint:i.mutation({query:t=>({url:`/api/v2/jobs/${t.entityId}`,method:"PUT",body:t.atApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobDtoAndJobId})}),atApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpoint:i.mutation({query:t=>({url:`/api/v2/jobs/${t.entityId1}/triggers/${t.entityId2}`,method:"DELETE"})}),atApiServiceEndpointsAdministrationJobsPutJobTriggerEndpoint:i.mutation({query:t=>({url:`/api/v2/jobs/${t.entityId1}/triggers/${t.entityId2}`,method:"PUT",body:t.atApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId})}),atApiServiceEndpointsAdministrationJobsGetJobEndpoint:i.query({query:t=>({url:`/api/v2/jobs/${t.rootEntityId}`})}),atApiServiceEndpointsAdministrationJobsGetJobRunEndpoint:i.query({query:t=>({url:`/api/v2/jobs/jobRuns/${t.rootEntityId}`})}),atApiServiceEndpointsAdministrationJobsGetJobRunLogsEndpoint:i.query({query:t=>({url:`/api/v2/jobs/jobRuns/${t.jobRunId}/logs`,params:{$filter:t.$filter,$orderby:t.$orderby,$select:t.$select,$top:t.$top,$skip:t.$skip,$count:t.$count}})}),atApiServiceEndpointsAdministrationJobsGetJobRunsEndpoint:i.query({query:t=>({url:"/api/v2/jobs/jobRuns",params:{$filter:t.$filter,$orderby:t.$orderby,$select:t.$select,$top:t.$top,$skip:t.$skip,$count:t.$count}})}),atApiServiceEndpointsAdministrationJobsGetJobsEndpoint:i.query({query:t=>({url:"/api/v2/jobs",params:{$filter:t.$filter,$orderby:t.$orderby,$select:t.$select,$top:t.$top,$skip:t.$skip,$count:t.$count}})}),atApiServiceEndpointsAdministrationJobsPostJobEndpoint:i.mutation({query:t=>({url:"/api/v2/jobs",method:"POST",body:t.atApiServiceEndpointsAdministrationModelsJobDto})}),atApiServiceEndpointsAdministrationJobsGetJobTriggerEndpoint:i.query({query:t=>({url:`/api/v2/jobs/${t.rootEntityId}/triggers/${t.subEntityId1}`})}),atApiServiceEndpointsAdministrationJobsGetJobTriggersEndpoint:i.query({query:t=>({url:`/api/v2/jobs/${t.jobId}/triggers`,params:{$filter:t.$filter,$orderby:t.$orderby,$select:t.$select,$top:t.$top,$skip:t.$skip,$count:t.$count}})}),atApiServiceEndpointsAdministrationJobsPostJobTriggerEndpoint:i.mutation({query:t=>({url:`/api/v2/jobs/${t.jobId}/triggers`,method:"POST",body:t.atApiServiceEndpointsAdministrationModelsJobTriggerDto})})}),overrideExisting:!1}),{useAtApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpointMutation:s,useAtApiServiceEndpointsAdministrationJobsPutJobTriggerEndpointMutation:r,useAtApiServiceEndpointsAdministrationJobsGetJobRunEndpointQuery:d,useAtApiServiceEndpointsAdministrationJobsGetJobTriggerEndpointQuery:p,useAtApiServiceEndpointsAdministrationJobsGetJobTriggersEndpointQuery:a,useAtApiServiceEndpointsAdministrationJobsPostJobTriggerEndpointMutation:b}=n,E=n.enhanceEndpoints({addTagTypes:["Job","JobTrigger"],endpoints:{atApiServiceEndpointsAdministrationJobsGetJobsEndpoint:{providesTags:["Job"]},atApiServiceEndpointsAdministrationJobsGetJobEndpoint:{providesTags:(i,t,o)=>[{type:"Job",id:o.rootEntityId}]},atApiServiceEndpointsAdministrationJobsPostJobEndpoint:{invalidatesTags:["Job"]},atApiServiceEndpointsAdministrationJobsPutJobEndpoint:{invalidatesTags:(i,t,o)=>[{type:"Job",id:o.entityId},"Job"]},atApiServiceEndpointsAdministrationJobsDeleteJobEndpoint:{invalidatesTags:(i,t,o)=>[{type:"Job",id:o.entityId},"Job"]},atApiServiceEndpointsAdministrationJobsGetJobTriggersEndpoint:{providesTags:(i,t,o)=>[{type:"JobTrigger",id:`job-${o.jobId}`},"JobTrigger"]},atApiServiceEndpointsAdministrationJobsGetJobTriggerEndpoint:{providesTags:(i,t,o)=>[{type:"JobTrigger",id:o.subEntityId1}]},atApiServiceEndpointsAdministrationJobsPostJobTriggerEndpoint:{invalidatesTags:(i,t,o)=>[{type:"JobTrigger",id:`job-${o.jobId}`},"JobTrigger"]},atApiServiceEndpointsAdministrationJobsPutJobTriggerEndpoint:{invalidatesTags:(i,t,o)=>[{type:"JobTrigger",id:o.entityId2},{type:"JobTrigger",id:`job-${o.entityId1}`},"JobTrigger"]},atApiServiceEndpointsAdministrationJobsDeleteJobTriggerEndpoint:{invalidatesTags:(i,t,o)=>[{type:"JobTrigger",id:o.entityId2},{type:"JobTrigger",id:`job-${o.entityId1}`},"JobTrigger"]}}}),$=d,g=a,c=p,u=b,v=r,T=s;export{E as a,g as b,u as c,v as d,c as e,$ as f,T as u};
//# sourceMappingURL=administration.Dk_dmNC6.js.map
