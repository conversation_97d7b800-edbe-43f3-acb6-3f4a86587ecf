{"version": 3, "file": "Plus.es-C-SwutyH.js", "sources": ["../../../node_modules/@phosphor-icons/react/dist/defs/Plus.es.js", "../../../node_modules/@phosphor-icons/react/dist/ssr/Plus.es.js"], "sourcesContent": ["import * as e from \"react\";\nconst a = /* @__PURE__ */ new Map([\n  [\n    \"bold\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M228,128a12,12,0,0,1-12,12H140v76a12,12,0,0,1-24,0V140H40a12,12,0,0,1,0-24h76V40a12,12,0,0,1,24,0v76h76A12,12,0,0,1,228,128Z\" }))\n  ],\n  [\n    \"duotone\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\n      \"path\",\n      {\n        d: \"M216,56V200a16,16,0,0,1-16,16H56a16,16,0,0,1-16-16V56A16,16,0,0,1,56,40H200A16,16,0,0,1,216,56Z\",\n        opacity: \"0.2\"\n      }\n    ), /* @__PURE__ */ e.createElement(\"path\", { d: \"M224,128a8,8,0,0,1-8,8H136v80a8,8,0,0,1-16,0V136H40a8,8,0,0,1,0-16h80V40a8,8,0,0,1,16,0v80h80A8,8,0,0,1,224,128Z\" }))\n  ],\n  [\n    \"fill\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M208,32H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM184,136H136v48a8,8,0,0,1-16,0V136H72a8,8,0,0,1,0-16h48V72a8,8,0,0,1,16,0v48h48a8,8,0,0,1,0,16Z\" }))\n  ],\n  [\n    \"light\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M222,128a6,6,0,0,1-6,6H134v82a6,6,0,0,1-12,0V134H40a6,6,0,0,1,0-12h82V40a6,6,0,0,1,12,0v82h82A6,6,0,0,1,222,128Z\" }))\n  ],\n  [\n    \"regular\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M224,128a8,8,0,0,1-8,8H136v80a8,8,0,0,1-16,0V136H40a8,8,0,0,1,0-16h80V40a8,8,0,0,1,16,0v80h80A8,8,0,0,1,224,128Z\" }))\n  ],\n  [\n    \"thin\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M220,128a4,4,0,0,1-4,4H132v84a4,4,0,0,1-8,0V132H40a4,4,0,0,1,0-8h84V40a4,4,0,0,1,8,0v84h84A4,4,0,0,1,220,128Z\" }))\n  ]\n]);\nexport {\n  a as default\n};\n", "import * as o from \"react\";\nimport t from \"../lib/SSRBase.es.js\";\nimport a from \"../defs/Plus.es.js\";\nconst e = o.forwardRef((r, s) => /* @__PURE__ */ o.createElement(t, { ref: s, ...r, weights: a }));\ne.displayName = \"PlusIcon\";\nconst f = e;\nexport {\n  f as Plus,\n  e as PlusIcon\n};\n"], "names": ["a", "e.createElement", "e.Fragment", "e", "o.forward<PERSON>ef", "r", "s", "o.createElement", "t"], "mappings": "8EACA,MAAMA,EAAoB,IAAI,IAAI,CAChC,CACE,OACgBC,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,+HAAgI,CAAC,CACpO,EACE,CACE,UACgBA,gBAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAChE,OACA,CACE,EAAG,kGACH,QAAS,KACjB,CACA,EAAuBA,EAAAA,cAAgB,OAAQ,CAAE,EAAG,kHAAkH,CAAE,CAAC,CACzK,EACE,CACE,OACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,iMAAkM,CAAC,CACtS,EACE,CACE,QACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,mHAAoH,CAAC,CACxN,EACE,CACE,UACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,mHAAoH,CAAC,CACxN,EACE,CACE,OACgBA,EAAAA,cAAgBC,EAAAA,SAAY,KAAsBD,EAAAA,cAAgB,OAAQ,CAAE,EAAG,gHAAiH,CAAC,CACrN,CACA,CAAC,EC7BKE,EAAIC,EAAAA,WAAa,CAACC,EAAGC,IAAsBC,EAAAA,cAAgBC,EAAG,CAAE,IAAKF,EAAG,GAAGD,EAAG,QAASL,CAAC,CAAE,CAAC,EACjGG,EAAE,YAAc", "x_google_ignoreList": [0, 1]}