{"version": 3, "file": "jobs-emails-list-CwGQNb10.js", "sources": ["../../../node_modules/@phosphor-icons/react/dist/ssr/ArrowLeft.es.js"], "sourcesContent": ["import * as r from \"react\";\nimport a from \"../lib/SSRBase.es.js\";\nimport f from \"../defs/ArrowLeft.es.js\";\nconst o = r.forwardRef((e, t) => /* @__PURE__ */ r.createElement(a, { ref: t, ...e, weights: f }));\no.displayName = \"ArrowLeftIcon\";\nconst c = o;\nexport {\n  c as ArrowLeft,\n  o as ArrowLeftIcon\n};\n"], "names": ["o", "r.forward<PERSON><PERSON>", "e", "t", "r.<PERSON>", "a", "f"], "mappings": "wHAGK,MAACA,EAAIC,EAAAA,WAAa,CAACC,EAAGC,IAAsBC,EAAAA,cAAgBC,EAAG,CAAE,IAAKF,EAAG,GAAGD,EAAG,QAASI,CAAC,CAAE,CAAC,EACjGN,EAAE,YAAc", "x_google_ignoreList": [0]}