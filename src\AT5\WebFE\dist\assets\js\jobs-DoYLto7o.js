import{aa as v,j as e,R as V,r as w,B as S,ab as W,ac as D,ad as C,S as z,b as j,I as H}from"./mui-51Y1Yx8M.js";import{s as $,c as J,f as O,g as _,h as L,i as B,j as K,k as q,t as X,p as P,l as Q,m as U,n as Y,o as Z,q as ee,u as N,H as te,v as se,x as ne,y as ie,z as ae,R as T}from"./index-mpIrzq_n.js";import{c as E}from"./odata-grid-data-source-3Wd95GSY.js";import{C as oe,B as re}from"./redux-BKAL-i9G.js";import{o as le}from"./PencilSimple.es-DvJcW97f.js";import{a as F}from"./administration-rFUZJOEr.js";import{u as G,D as k,G as de,a as A,b as M,c as ce,g as he,d as ue}from"./DataGridPremium-B9W7LygO.js";import{useNavigate as pe,useLoaderData as me}from"./router-Bd8Y3ArC.js";import"./vendor-Csw2ODfV.js";import"./IconBase.es-DrgWL7aQ.js";const ge=v(e.jsx("path",{d:"M10 18h4v-2h-4zM3 6v2h18V6zm3 7h12v-2H6z"})),fe=v(e.jsx("path",{d:"M14.67 5v14H9.33V5zm1 14H21V5h-5.33zm-7.34 0V5H3v14z"})),xe=v(e.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"})),be=v(e.jsx("path",{d:"M16 9v10H8V9zm-1.5-6h-5l-1 1H5v2h14V4h-3.5zM18 7H6v12c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2z"})),je=v(e.jsx("path",{d:"M10 8.64 15.27 12 10 15.36zM8 5v14l11-7z"})),R=()=>oe(),l=re;function Se(){const n=G(),o=R(),h=l($),u=l(J),p=l(O),m=l(_),d=pe(),[g,f]=V.useState(()=>E(F.endpoints.atApiServiceEndpointsAdministrationJobsGetJobRunsEndpoint.initiate,()=>{var c;const t=n.current;return t?(((c=t.getVisibleColumns)==null?void 0:c.call(t))??[]).map(a=>a.field).filter(a=>a&&a!=="actions"&&!a.startsWith("__")):void 0})),x=[{field:"id",headerName:"Id",minWidth:90,align:"center",headerAlign:"center",type:"number"},{field:"triggered",headerName:"Triggered",minWidth:220,align:"center",headerAlign:"center",type:"dateTime",valueGetter:t=>t?new Date(t):null},{field:"started",headerName:"Started",minWidth:220,type:"dateTime",valueGetter:t=>t?new Date(t):null},{field:"finished",headerName:"Finished",minWidth:220,type:"dateTime",valueGetter:t=>t?new Date(t):null},{field:"jobName",headerName:"Job Name",minWidth:200,type:"string"},{field:"authorId",headerName:"Author Id",width:90,type:"number"}];return e.jsx(k,{apiRef:n,columns:x,dataSource:g,filterMode:"server",getRowId:t=>t.id,onRowDoubleClick:t=>{d(P.administration.jobs.jobRuns.view(t.row.id))},onDataSourceError:t=>{X(`DataGrid dataSource error: ${t}`)},showToolbar:!0,disablePivoting:!0,filterDebounceMs:500,ignoreDiacritics:!0,pagination:!0,pageSizeOptions:[10,20,50,100],paginationModel:{pageSize:h,page:u},onPaginationModelChange:t=>{t.pageSize!==h&&o(K(t.pageSize)),t.page!==u&&o(q(t.page))},sortModel:p,onSortModelChange:t=>{o(B(t.map(r=>({field:r.field,sort:r.sort||null}))))},filterModel:m,onFilterModelChange:t=>{o(L(t))},hideFooterSelectedRowCount:!0})}const ve={title:"Jobs"};function we({apiRef:n,createNewPath:o,onDelete:h,onRun:u}){const p=A(n,he),m=p.size,d=m>0,g=C(),f=C(),x=C(),t=C(),r=A(n,ue),c=r.open&&r.openedPanelValue===M.filters,a=r.open&&r.openedPanelValue===M.columns,{filterPanelTriggerRef:I,columnsPanelTriggerRef:s}=ce();return e.jsxs(S,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",p:1},children:[e.jsxs(z,{direction:"row",spacing:1,alignItems:"center",children:[e.jsx(j,{ref:s,id:x,variant:"outlined",sx:{minWidth:100},startIcon:e.jsx(fe,{}),"aria-haspopup":"true","aria-expanded":a?"true":void 0,"aria-controls":a?t:void 0,onClick:()=>{const i=n==null?void 0:n.current;i&&(a?i.hidePreferences():i.showPreferences(M.columns,t,x))},children:"Columns"}),e.jsx(j,{ref:I,id:g,variant:"outlined",sx:{minWidth:100},startIcon:e.jsx(ge,{}),"aria-haspopup":"true","aria-expanded":c?"true":void 0,"aria-controls":c?f:void 0,onClick:()=>{const i=n==null?void 0:n.current;i&&(c?i.hidePreferences():i.showPreferences(M.filters,f,g))},children:"Filter"})]}),e.jsx(z,{direction:"row",spacing:1,alignItems:"center",children:d?e.jsxs(e.Fragment,{children:[e.jsxs(S,{sx:{display:"flex",alignItems:"center",fontSize:14,color:"text.secondary",pr:.5},children:[m," selected   |"]}),e.jsx(j,{startIcon:e.jsx(be,{}),color:"error",variant:"text",onClick:()=>h(Array.from(p.keys())),sx:{border:"none",minWidth:90},children:"Delete"}),e.jsx(j,{startIcon:e.jsx(je,{}),variant:"contained",color:"primary",onClick:()=>u(Array.from(p.keys())),children:"Run"})]}):e.jsx(j,{startIcon:e.jsx(xe,{}),variant:"contained",component:T,href:`${o}`,children:"Create New"})})]})}function Ee(){const n=R(),o=l(Q),h=l(U),u=l(Y),p=l(Z),m=l(ee),d=G(),{showLogs:g}=me(),[f,x]=w.useState(()=>E(F.endpoints.atApiServiceEndpointsAdministrationJobsGetJobsEndpoint.initiate,()=>{var y;const s=d.current;return s?(((y=s.getVisibleColumns)==null?void 0:y.call(s))??[]).map(b=>b.field).filter(b=>b&&b!=="actions"&&!b.startsWith("__")):void 0}));w.useEffect(()=>{g&&n(N(1))},[]);const t=[{field:"id",headerName:"Id",width:90,type:"number"},{field:"name",headerName:"Name",width:400,type:"string"},{field:"description",headerName:"Description",width:400,type:"string"},{field:"parameters",headerName:"Parameters",width:500,type:"string"},{field:"disabled",headerName:"Disabled",width:100,type:"boolean"},{field:"successEmails",headerName:"Success Emails",width:200,type:"string"},{field:"errorEmails",headerName:"Error Emails",width:200,type:"string"},{field:"type",headerName:"Type",width:200},{field:"actions",renderCell:s=>e.jsx(H,{component:T,href:P.administration.jobs.edit(s.row.id),children:e.jsx(le,{})}),headerName:"Actions",width:100,align:"right",type:"actions"}],r=(s,i)=>{n(N(i))},c=s=>{alert("Delete selected jobs: "+s.join(", "))},a=s=>{alert("Run selected jobs: "+s.join(", "))},I=w.useMemo(()=>({toolbar:()=>e.jsx(we,{apiRef:d,createNewPath:P.administration.jobs.create,onDelete:c,onRun:a})}),[d]);return e.jsxs(w.Fragment,{children:[e.jsx(te,{children:e.jsx("title",{children:ve.title})}),e.jsxs(S,{sx:{maxWidth:"var(--Content-maxWidth)",m:"var(--Content-margin)",p:"var(--Content-padding)",width:"var(--Content-width)"},children:[e.jsxs(W,{value:o,onChange:r,sx:{mb:2},children:[e.jsx(D,{label:"Jobs"}),e.jsx(D,{label:"Logs"})]}),e.jsx(S,{sx:{display:o===0?"block":"none"},children:e.jsx(k,{apiRef:d,columns:t,dataSource:f,filterMode:"server",showToolbar:!0,disablePivoting:!0,filterDebounceMs:500,ignoreDiacritics:!0,pagination:!0,pageSizeOptions:[10,20,50,100],paginationModel:{pageSize:h,page:u},onPaginationModelChange:s=>{s.pageSize!==h&&n(ie(s.pageSize)),s.page!==u&&n(ae(s.page))},sortModel:p,onSortModelChange:s=>{n(ne(s.map(i=>({field:i.field,sort:i.sort||null}))))},filterModel:m,onFilterModelChange:s=>{n(se(s))},checkboxSelection:!0,slots:I,slotProps:{columnsManagement:{getTogglableColumns:s=>s.map(i=>i.field).filter(i=>i!==de)}},hideFooterSelectedRowCount:!0})}),e.jsx(S,{sx:{display:o===1?"block":"none"},children:e.jsx(Se,{})})]})]})}export{Ee as Page};
//# sourceMappingURL=jobs.aVVlT0Hh.js.map
